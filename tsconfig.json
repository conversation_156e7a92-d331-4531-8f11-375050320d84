{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@vben/ts-config/vue-app.json", "compilerOptions": {"baseUrl": ".", "declaration": false, "paths": {"@/*": ["src/*"], "#/*": ["types/*"]}, "typeRoots": ["./node_modules/@types", "./src/auto-imports.d.ts"], "allowSyntheticDefaultImports": true, "moduleResolution": "node"}, "include": ["tests/**/*.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.ts", "src/store/changzhou/mapStore.ts", "src/store/changzhou/wirelessStore.js"], "exclude": ["node_modules", "tests/server/**/*.ts", "dist", "**/*.js"]}