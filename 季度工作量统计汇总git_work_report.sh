#!/bin/bash

# 定义目标作者列表
authors=( "HULINZI" )

# 设置时间范围（从今年4月1日至今）
since_date="2025-04-01"
output_dir="git_work_reports_$(date +%Y%m%d)"
mkdir -p "$output_dir"

# CSV文件头
csv_header="提交ID,日期,增加行数,删除行数,总变更行数,影响文件数,描述,Message"

# 为每个作者生成单独的CSV报告文件
for author in "${authors[@]}"; do
    # 创建作者专属CSV文件
    csv_file="${output_dir}/${author}_report_$(date +%Y%m%d).csv"
    echo "$csv_header" > "$csv_file"
    
    # 获取该作者的提交数据并格式化为CSV
    git log --since="$since_date" \
        --author="$author" \
        --pretty=format:"%H|%ad|%s" \
        --date=format:"%Y-%m-%d" \
        --numstat | \
    awk '
    BEGIN { 
        FS="\t"; 
        OFS=","; 
        print "开始处理'"$author"'的提交记录..." > "/dev/stderr"
    }
    /^$/ { next }  # 跳过空行
    /^[0-9a-f]{40}\|/ {
        # 处理上一个提交的统计
        if (commit_hash != "") {
            # 转义描述和message中的特殊字符（特别是双引号和逗号）
            gsub(/"/, "\"\"", commit_msg);  # 转义双引号
            gsub(/\r?\n/, " ", commit_msg); # 替换换行符为空格
            
            # 计算统计信息
            total_changes = insertions + deletions
            
            # 输出CSV行
            print substr(commit_hash, 1, 7), 
                  commit_date, 
                  insertions, 
                  deletions, 
                  total_changes, 
                  file_changes, 
                  "\"" description "\"", 
                  "\"" commit_msg "\""
        }
        # 开始新提交记录
        split($0, commit_info, "|")
        commit_hash = commit_info[1]
        commit_date = commit_info[2]
        commit_msg = commit_info[3]
        description = ""  # 可以在此处添加提取描述的逻辑
        insertions = 0
        deletions = 0
        file_changes = 0
    }
    /^[0-9]+\t[0-9]+\t/ {
        # 统计文件变更
        insertions += $1
        deletions += $2
        file_changes++
    }
    END {
        # 处理最后一个提交
        if (commit_hash != "") {
            gsub(/"/, "\"\"", commit_msg);
            gsub(/\r?\n/, " ", commit_msg);
            total_changes = insertions + deletions
            print substr(commit_hash, 1, 7), 
                  commit_date, 
                  insertions, 
                  deletions, 
                  total_changes, 
                  file_changes, 
                  "\"" description "\"", 
                  "\"" commit_msg "\""
        }
    }
    ' >> "$csv_file"
    
    echo "已生成 ${author} 的CSV报告: ${csv_file}"
done

echo "所有CSV报告生成完成！文件保存在 ${output_dir} 目录中。"