@basic-menu-prefix-cls: ~'@{namespace}-basic-menu';

.app-top-menu-popup {
  min-width: 180px;

  .ant-menu {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 255, 0.98) 100%) !important;
    backdrop-filter: blur(12px) !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(30, 58, 138, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(30, 58, 138, 0.1) !important;
    padding: 8px !important;

    .ant-menu-item {
      border-radius: 8px !important;
      margin: 2px 0 !important;
      padding: 8px 16px !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      color: #4a5568 !important;
      font-weight: 500 !important;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(30, 58, 138, 0.05) 0%, rgba(255, 215, 0, 0.03) 100%);
        border-radius: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        background: linear-gradient(135deg, rgba(30, 58, 138, 0.08) 0%, rgba(255, 215, 0, 0.05) 100%) !important;
        color: #1e3a8a !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(30, 58, 138, 0.1) !important;

        &::before {
          opacity: 1;
        }
      }

      &.ant-menu-item-selected {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%) !important;
        color: #fff !important;
        box-shadow: 0 4px 16px rgba(30, 58, 138, 0.25), inset 0 1px 0 rgba(255, 215, 0, 0.2) !important;
        border: 1px solid rgba(255, 215, 0, 0.3) !important;

        &::before {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 215, 0, 0.1) 100%);
          opacity: 1;
        }
      }
    }

    .ant-menu-submenu {
      .ant-menu-submenu-title {
        border-radius: 8px !important;
        margin: 2px 0 !important;
        padding: 8px 16px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        color: #4a5568 !important;
        font-weight: 500 !important;

        &:hover {
          background: linear-gradient(135deg, rgba(30, 58, 138, 0.08) 0%, rgba(255, 215, 0, 0.05) 100%) !important;
          color: #1e3a8a !important;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(30, 58, 138, 0.1) !important;
        }
      }

      &.ant-menu-submenu-selected {
        .ant-menu-submenu-title {
          background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%) !important;
          color: #fff !important;
          box-shadow: 0 4px 16px rgba(30, 58, 138, 0.25), inset 0 1px 0 rgba(255, 215, 0, 0.2) !important;
          border: 1px solid rgba(255, 215, 0, 0.3) !important;
        }
      }
    }
  }
}

.@{basic-menu-prefix-cls} {
  width: 100%;

  .ant-menu-item {
    transition: unset;
  }

  &__sidebar-hor {
    &.ant-menu-horizontal {
      display: flex;
      align-items: center;

      &.ant-menu-dark {
        background-color: transparent;

        .ant-menu-submenu:hover,
        .ant-menu-item-open,
        .ant-menu-submenu-open,
        .ant-menu-item-selected,
        .ant-menu-submenu-selected,
        .ant-menu-item:hover,
        .ant-menu-item-active,
        .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
        .ant-menu-submenu-active,
        .ant-menu-submenu-title:hover {
          background-color: @top-menu-active-bg-color !important;
          color: #fff;
        }

        .ant-menu-item:hover,
        .ant-menu-item-active,
        .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
        .ant-menu-submenu-active,
        .ant-menu-submenu-title:hover {
          background-color: @top-menu-active-bg-color;
        }

        .@{basic-menu-prefix-cls}-item__level1 {
          background-color: transparent;

          &.ant-menu-item-selected,
          &.ant-menu-submenu-selected {
            background-color: @top-menu-active-bg-color !important;
          }
        }

        .ant-menu-item,
        .ant-menu-submenu {
          &.@{basic-menu-prefix-cls}-item__level1,
          .ant-menu-submenu-title {
            height: @header-height;
            line-height: @header-height;
          }
        }
      }
    }
  }

  .ant-menu-submenu,
  .ant-menu-submenu-inline {
    transition: unset;
  }

  .ant-menu-inline.ant-menu-sub {
    transition: unset;
    box-shadow: unset !important;
  }
}
