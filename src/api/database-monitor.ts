import { defHttp } from '@/utils/http/axios';

export interface DatabaseShardingInfo {
  idleConnections: number;
  targetDataSourceType: string;
  activeConnections: number;
  shardingCode: string;
  totalConnections: number;
  threadsAwaitingConnection: number;
  isValid: boolean;
  validateMessage: string;
  maxPoolSize: number;
  dataSourceType: string;
}

export interface DatabaseMonitorResponse {
  response: DatabaseShardingInfo[];
  resultCode: string;
}

/**
 * 获取所有数据库分片验证信息
 * @returns 数据库分片信息列表
 */
export const validateAllSharding = async (): Promise<DatabaseMonitorResponse> => {
  try {
    const response = await defHttp.post<DatabaseMonitorResponse>({
      url: '/daas-dev-api/database/validate-all-sharding',
      data: {},
    });
    return response;
  } catch (error) {
    console.error('获取数据库分片信息失败:', error);
    throw error;
  }
};

/**
 * 刷新数据库连接池
 * @param shardingCode 分片代码
 */
export const refreshConnectionPool = async (shardingCode: string) => {
  try {
    const response = await defHttp.post({
      url: '/daas-dev-api/database/refresh-connection-pool',
      data: { shardingCode },
    });
    return response;
  } catch (error) {
    console.error('刷新数据库连接池失败:', error);
    throw error;
  }
};
