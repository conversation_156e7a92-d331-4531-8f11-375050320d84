import { defHttp } from '@/utils/http/axios';

export interface DatabaseShardingInfo {
  idleConnections: number;
  targetDataSourceType: string;
  activeConnections: number;
  shardingCode: string;
  totalConnections: number;
  threadsAwaitingConnection: number;
  isValid: boolean;
  validateMessage: string;
  maxPoolSize: number;
  dataSourceType: string;
}

export interface DatabaseMonitorResponse {
  response: DatabaseShardingInfo[];
  resultCode: string;
}

/**
 * 获取所有数据库分片验证信息
 * @param rootPath API根路径
 * @returns 数据库分片信息列表
 */
export const validateAllSharding = async (rootPath: string = '/daas-dev-api'): Promise<DatabaseMonitorResponse> => {
  try {
    const response = await defHttp.post<DatabaseMonitorResponse>({
      url: `${rootPath}/database/validate-all-sharding`,
      data: {},
    }, {
      isTransformResponse: false,
    });
    return response;
  } catch (error) {
    console.error('获取数据库分片信息失败:', error);
    throw error;
  }
};

/**
 * 刷新数据库连接池
 * @param shardingCode 分片代码
 * @param rootPath API根路径
 */
export const refreshConnectionPool = async (shardingCode: string, rootPath: string = '/daas-dev-api') => {
  try {
    const response = await defHttp.post({
      url: `${rootPath}/database/validate-sharding`,
      data: { shardingCode },
    }, {
      isTransformResponse: false,
    });
    return response;
  } catch (error) {
    console.error('刷新数据库连接池失败:', error);
    throw error;
  }
};
