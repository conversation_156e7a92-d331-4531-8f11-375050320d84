import { defHttp } from '@/utils/http/axios';

/**
 * 双路由光路检测运营统计API接口
 * 使用 doCreateNew 方式调用后端 Controller
 */

// 查询参数接口定义
export interface DualRouteStatisticsParams {
  startDate?: string;
  endDate?: string;
  metricType?: 'remediation' | 'confirmation' | 'improvement' | 'deterioration';
  objectId?: string;
  pageSize?: number;
  currentPage?: number;
  timeRange?: '1m' | '3m' | '6m' | '1y';
  riskLevel?: 'high_risk' | 'medium_risk' | 'low_risk';
  reportType?: 'monthly' | 'trend' | 'detail' | 'summary';
  problemTypes?: string[];
  areaCodes?: string[];
  // 注意：areaCode不在这里传递，而是通过cityCode参数确定shardingCode
}

// 月度统计数据接口
export interface MonthlyStatistics {
  success: boolean;
  message: string;
  data: {
    queryParams: {
      startDate: string;
      endDate: string;
      areaCode?: string;
    };
    summary: {
      totalRemediation: number;
      totalConfirmation: number;
      totalImprovement: number;
      totalDeterioration: number;
      totalChecks: number;
      remediationRate: number;
      improvementRate: number;
      deteriorationRate: number;
    };
    monthlyStats: Array<{
      stat_month: string;
      area_code: string;
      remediation_count: number;
      confirmation_count: number;
      improvement_count: number;
      deterioration_count: number;
      total_checks: number;
      remediation_rate: number;
      improvement_rate: number;
      deterioration_rate: number;
    }>;
    statusDistribution: Array<{
      status: string;
      count: number;
      unique_objects: number;
      percentage: number;
    }>;
    areaStats: Array<{
      area_code: string;
      total_checks: number;
      normal_count: number;
      problem_count: number;
      normal_rate: number;
    }>;
  };
}

// 趋势分析数据接口
export interface TrendAnalysis {
  success: boolean;
  message: string;
  data: {
    queryParams: {
      startDate: string;
      endDate: string;
      areaCode?: string;
    };
    summary: {
      totalDetections: number;
      totalProblems: number;
      avgProblemRate: number;
      analysisMonths: number;
    };
    monthlyTrend: Array<{
      stat_month: string;
      total_detections: number;
      problem_detections: number;
      unique_objects: number;
      problem_rate: number;
    }>;
    statusChangeTrend: Array<{
      stat_month: string;
      fixed_count: number;
      degraded_count: number;
      improved_count: number;
      stable_count: number;
      fix_rate: number;
    }>;
    problemDuration: Array<{
      problem_severity: string;
      problem_count: number;
      avg_duration_days: number;
      min_duration_days: number;
      max_duration_days: number;
      short_term_count: number;
      medium_term_count: number;
      long_term_count: number;
      chronic_count: number;
    }>;
    riskObjects: Array<{
      risk_level: string;
      object_count: number;
      avg_problem_frequency: number;
      current_problem_objects: number;
    }>;
  };
}

// 详细数据接口
export interface DetailData {
  success: boolean;
  message: string;
  data: Array<{
    object_id: string;
    group_code: string;
    group_name: string;
    area_code: string;
    area_name: string;
    previous_status: string;
    current_status: string;
    metric_type: string;
    deal_result: string;
    confirm_status: string;
    confirm_op: string;
    create_time: string;
    check_time: string;
    confirm_time: string;
    change_description: string;
  }>;
  pagination: {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  queryParams: DualRouteStatisticsParams;
}

/**
 * 获取双路由月度运营统计数据
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns 月度统计数据
 */
export const getDualRouteMonthlyStatistics = async (
  cityCode: string,
  params: DualRouteStatisticsParams = {}
): Promise<MonthlyStatistics> => {
  const requestData = {
    ...params,
    areaCode: cityCode
  };

  console.log('📊 [月度统计API] 发送请求:', requestData);

  try {
    const response = await defHttp.post(
      {
        url: '/graph-rest-api/api/dual-route-statistics/monthly',
        data: requestData
      },
      { isTransformResponse: false }
    );

    console.log('✅ [月度统计API] 请求成功:', response);
    return response;
  } catch (error) {
    console.error('❌ [月度统计API] 请求失败:', error);
    throw error;
  }
};

// 隐患整改统计参数接口
export interface HazardRemediationParams {
  startDate?: string;
  endDate?: string;
  groupId?: string;
  objectType?: 'optGroup' | 'CircuitPair';  // 保护组类型：optGroup=光路保护组, CircuitPair=电路保护组
  historyLimit?: number;
  pageSize?: number;
  currentPage?: number;
}

// 隐患整改统计结果接口
export interface HazardRemediationStatistics {
  success: boolean;
  message: string;
  data: Array<{
    光路组id: number;
    地市名称: string;
    区县: string;
    专业: string;
    保护组编码: string;
    保护组名称: string;
    保护组类型: string;
    最新检测结果: string;
    上一次检测结果: string;
    最新异常信息: string;
    上一次检测异常信息: string;
    最新检测时间: string;
    历史检测时间: string;
    group_idx: number;
  }>;
  pageInfo: {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
  };
  queryParams: {
    startDate?: string;
    endDate?: string;
    groupId?: string;
    objectType?: string;
    historyLimit: number;
  };
}

// 获取双路由变化统计
export const getDualRouteChangeStatistics = async (
  cityCode: string,
  params: HazardRemediationParams = {}
): Promise<HazardRemediationStatistics> => {
  // 智能设置historyLimit
  let historyLimit = params.historyLimit;
  if (historyLimit === undefined) {
    if (params.groupId) {
      // 单个光路组查询时，查看更多历史记录
      historyLimit = 20;
    } else {
      // 全量统计时，只查看最新的变化
      historyLimit = 2;
    }
  } else {
    // 对用户设置的historyLimit进行合理性检查
    if (!params.groupId && historyLimit > 5) {
      // 全量查询时限制不要太大
      console.warn('全量查询时historyLimit不建议超过5，当前设置:', historyLimit);
      historyLimit = 5;
    } else if (params.groupId && historyLimit > 50) {
      // 单个光路组查询时的最大限制
      console.warn('单个光路组查询时historyLimit不建议超过50，当前设置:', historyLimit);
      historyLimit = 50;
    }
  }

  const requestData = {
    ...params,
    areaCode: cityCode,
    historyLimit
  };

  console.log('🔧 [变化统计API] 发送请求:', requestData);

  try {
    const response = await defHttp.post(
      {
        url: '/graph-rest-api/api/dual-route-statistics/change-statistics',
        data: requestData
      },
      { isTransformResponse: false }
    );

    console.log('✅ [变化统计API] 请求成功:', response);
    return response;
  } catch (error) {
    console.error('❌ [变化统计API] 请求失败:', error);
    throw error;
  }
};

// 便捷方法：获取全量统计（最新变化）
export const getDualRouteChangeSummary = async (
  cityCode: string,
  params: Omit<HazardRemediationParams, 'groupId' | 'historyLimit'> = {}
): Promise<HazardRemediationStatistics> => {
  return getDualRouteChangeStatistics(cityCode, {
    ...params,
    historyLimit: 2  // 强制设置为2，只看最新变化
  });
};

// 便捷方法：获取单个光路组详细历史
export const getDualRouteChangeDetail = async (
  cityCode: string,
  groupId: string,
  params: Omit<HazardRemediationParams, 'groupId'> = {}
): Promise<HazardRemediationStatistics> => {
  return getDualRouteChangeStatistics(cityCode, {
    ...params,
    groupId,
    historyLimit: params.historyLimit || 20  // 默认查看20条历史记录
  });
};

// 保持向后兼容的别名
export const getDualRouteHazardRemediationStatistics = getDualRouteChangeStatistics;
export const getDualRouteHazardRemediationSummary = getDualRouteChangeSummary;
export const getDualRouteHazardRemediationDetail = getDualRouteChangeDetail;

/**
 * 获取双路由历史趋势分析数据
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns 趋势分析数据
 */
export const getDualRouteTrendAnalysis = async (
  cityCode: string,
  params: DualRouteStatisticsParams = {}
): Promise<TrendAnalysis> => {
  const requestData = {
    ...params,
    areaCode: cityCode
  };

  console.log('📈 [趋势分析API] 发送请求:', requestData);

  try {
    const response = await defHttp.post(
      {
        url: '/graph-rest-api/api/dual-route-statistics/trend',
        data: requestData
      },
      { isTransformResponse: false }
    );

    console.log('✅ [趋势分析API] 请求成功:', response);
    return response;
  } catch (error) {
    console.error('❌ [趋势分析API] 请求失败:', error);
    throw error;
  }
};







/**
 * 格式化日期参数
 * @param date 日期对象或字符串
 * @param isEndDate 是否为结束日期
 * @returns 格式化后的日期字符串
 */
export const formatDateParam = (date: Date | string, isEndDate: boolean = false): string => {
  let dateObj: Date;

  if (typeof date === 'string') {
    dateObj = new Date(date);
  } else {
    dateObj = date;
  }

  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');

  if (isEndDate) {
    return `${year}-${month}-${day} 23:59:59`;
  } else {
    return `${year}-${month}-${day} 00:00:00`;
  }
};

/**
 * 获取默认时间范围（最近3个月）
 * @returns 时间范围对象
 */
export const getDefaultTimeRange = () => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - 3);

  return {
    startDate: formatDateParam(startDate, false),
    endDate: formatDateParam(endDate, true),
  };
};
