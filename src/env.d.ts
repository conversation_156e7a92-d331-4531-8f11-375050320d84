/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_USE_MOCK: string
  readonly VITE_PUBLIC_PATH: string
  readonly VITE_GLOB_APP_TITLE: string
  readonly VITE_BUILD_COMPRESS: string
  readonly VITE_GLOB_APP_SHORT_NAME: string
  readonly VITE_USE_PWA: string
  readonly VITE_DROP_CONSOLE: string
  readonly VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE: string
  readonly VITE_LEGACY: string
  readonly VITE_USE_IMAGEMIN: string
  readonly VITE_GENERATE_UI: string
  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
