@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }
}

/* 大屏可视化特定样式 */
html,
body {
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

/* 固定1920x1080大小的大屏 */
.dashboard-container {
  width: 1920px;
  height: 1080px;
  position: relative;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  overflow: hidden;
  transform-origin: top left;
  background-image:
    radial-gradient(circle at 15% 50%, rgba(59, 130, 246, 0.05) 0%, transparent 25%),
    radial-gradient(circle at 85% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 25%);
}

/* 自动缩放以适应屏幕 */
@media screen {
  .dashboard-container {
    transform: scale(var(--scale-ratio, 1));
    position: absolute;
    top: 0;
    left: 0;
  }
}

/* 高科技背景元素 */
.tech-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/placeholder.svg?height=1080&width=1920');
  background-size: cover;
  opacity: 0.15;
  z-index: 0;
}

.tech-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(248, 250, 252, 0.3) 0%, rgba(226, 232, 240, 0.3) 100%);
  z-index: 1;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 5%),
    radial-gradient(circle at 20% 40%, rgba(59, 130, 246, 0.08) 0%, transparent 5%),
    radial-gradient(circle at 30% 60%, rgba(59, 130, 246, 0.08) 0%, transparent 5%),
    radial-gradient(circle at 40% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 5%),
    radial-gradient(circle at 50% 10%, rgba(59, 130, 246, 0.08) 0%, transparent 5%),
    radial-gradient(circle at 60% 30%, rgba(59, 130, 246, 0.08) 0%, transparent 5%),
    radial-gradient(circle at 70% 50%, rgba(59, 130, 246, 0.08) 0%, transparent 5%),
    radial-gradient(circle at 80% 70%, rgba(59, 130, 246, 0.08) 0%, transparent 5%),
    radial-gradient(circle at 90% 90%, rgba(59, 130, 246, 0.08) 0%, transparent 5%);
  z-index: 2;
}

.tech-circuit {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/placeholder.svg?height=1080&width=1920');
  background-size: cover;
  opacity: 0.1;
  z-index: 3;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.15) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 4;
}

/* 顶部标题栏 */
.dashboard-header {
  height: 60px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 10;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.8) 20%,
    rgba(59, 130, 246, 0.8) 80%,
    transparent 100%
  );
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.header-left-decoration,
.header-right-decoration {
  position: absolute;
  top: 0;
  width: 150px;
  height: 100%;
  z-index: -1;
}

.header-left-decoration {
  left: 0;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.2) 0%, transparent 100%);
}

.header-right-decoration {
  right: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.2) 100%);
}

.header-menu-item {
  position: relative;
  padding: 8px 16px;
  color: rgba(59, 130, 246, 0.8);
  font-size: 14px;
  cursor: pointer;
  border: 1px solid transparent;
}

.header-menu-item::before,
.header-menu-item::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  border: 1px solid rgba(59, 130, 246, 0.8);
  opacity: 0;
  transition: all 0.3s;
}

.header-menu-item::before {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.header-menu-item::after {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.header-menu-item:hover::before,
.header-menu-item:hover::after {
  opacity: 1;
}

.time-display {
  position: relative;
  padding: 5px 10px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.6);
}

.time-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.8) 50%, transparent 100%);
}

/* 卡片样式 */
.dashboard-card {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  z-index: 10;
}

.card-decoration {
  position: absolute;
  width: 15px;
  height: 15px;
  z-index: 1;
}

.card-decoration.top-left {
  top: -1px;
  left: -1px;
  border-top: 2px solid rgba(59, 130, 246, 0.8);
  border-left: 2px solid rgba(59, 130, 246, 0.8);
  box-shadow: -2px -2px 5px rgba(59, 130, 246, 0.3);
}

.card-decoration.top-right {
  top: -1px;
  right: -1px;
  border-top: 2px solid rgba(59, 130, 246, 0.8);
  border-right: 2px solid rgba(59, 130, 246, 0.8);
  box-shadow: 2px -2px 5px rgba(59, 130, 246, 0.3);
}

.card-decoration.bottom-left {
  bottom: -1px;
  left: -1px;
  border-bottom: 2px solid rgba(59, 130, 246, 0.8);
  border-left: 2px solid rgba(59, 130, 246, 0.8);
  box-shadow: -2px 2px 5px rgba(59, 130, 246, 0.3);
}

.card-decoration.bottom-right {
  bottom: -1px;
  right: -1px;
  border-bottom: 2px solid rgba(59, 130, 246, 0.8);
  border-right: 2px solid rgba(59, 130, 246, 0.8);
  box-shadow: 2px 2px 5px rgba(59, 130, 246, 0.3);
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 0 20px rgba(59, 130, 246, 0.1);
  pointer-events: none;
  z-index: 0;
}

.dashboard-card-header {
  padding: 10px 16px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.1) 100%);
}

.dashboard-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.5) 20%,
    rgba(59, 130, 246, 0.5) 80%,
    transparent 100%
  );
}

.dashboard-card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40%;
  height: 1px;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.8) 0%, transparent 100%);
}

.dashboard-card-content {
  flex: 1;
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.dashboard-card-content::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 40%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.5) 100%);
  z-index: -1;
}

.chart-container {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
}

/* 数据盒子样式 */
.data-box {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 2px;
  padding: 6px 4px;
  position: relative;
  overflow: hidden;
}

.data-box-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.8) 50%, transparent 100%);
}

.data-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.8) 50%, transparent 100%);
}

/* 菜单按钮样式 */
.menu-button {
  padding: 4px 12px;
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: rgba(59, 130, 246, 0.8);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.menu-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.8) 50%, transparent 100%);
}

.menu-button.active {
  background-color: rgba(59, 130, 246, 0.2);
  color: #1e293b;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.bandwidth-resources {
  grid-column: 1 / 4;
  grid-row: 3 / 5;
}

.network-status {
  grid-column: 10 / 13;
  grid-row: 3 / 5;
}

.network-capacity {
  grid-column: 10 / 13;
  grid-row: 5 / 7;
}

.network-trend {
  grid-column: 1 / 4;
  grid-row: 5 / 7;
}

/* 添加一些微妙的动画效果 */
@keyframes pulse-light {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

.animate-pulse-light {
  animation: pulse-light 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 添加扫描线动画 */
.dashboard-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(180deg, rgba(43, 212, 241, 0.2) 0%, transparent 100%);
  animation: scan 8s linear infinite;
  pointer-events: none;
  z-index: 5;
}

@keyframes scan {
  0% {
    top: -10px;
  }
  100% {
    top: 1080px;
  }
}

/* 数字雨效果 */
@keyframes digital-rain {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(1080px);
    opacity: 0;
  }
}

.tech-particles::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 1%),
    radial-gradient(circle at 20% 40%, rgba(59, 130, 246, 0.1) 0%, transparent 1%),
    radial-gradient(circle at 30% 60%, rgba(59, 130, 246, 0.1) 0%, transparent 1%),
    radial-gradient(circle at 40% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 1%),
    radial-gradient(circle at 50% 10%, rgba(59, 130, 246, 0.1) 0%, transparent 1%),
    radial-gradient(circle at 60% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 1%),
    radial-gradient(circle at 70% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 1%),
    radial-gradient(circle at 80% 70%, rgba(59, 130, 246, 0.1) 0%, transparent 1%),
    radial-gradient(circle at 90% 90%, rgba(59, 130, 246, 0.1) 0%, transparent 1%);
  animation: digital-rain 15s linear infinite;
  z-index: 3;
}
