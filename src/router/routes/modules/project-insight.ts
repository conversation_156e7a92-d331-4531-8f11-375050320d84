import type { AppRouteModule } from '@/router/types';

import { LAYOUT } from '@/router/constant';

const develop: AppRouteModule = {
  path: '/nrm/project-insight',
  name: 'nrm-roject-insight',
  component: LAYOUT,
  meta: {
    orderNo: 999,
    icon: 'ion:analytics',
    title: 'API调用分析',
    ignoreKeepAlive: false,
    keepalive: true,
    hideMenu: true
  },
  children: [
    {
      path: 'ETL-analysis',
      name: 'ETL-analysis',
      component: () => import('@/views/nrm/project-insight/ETL-analysis/index.vue'),
      meta: {
        title: 'ETL平台',
        ignoreKeepAlive: false,
        hideBreadcrumb: false,
        // hideMenu: true
      },
      children: [],
    },
    {
      path: 'apischeduler-analysis',
      name: 'aischeduler-analysis',
      component: () => import('@/views/nrm/project-insight/apischeduler-analysis/index.vue'),
      meta: {
        title: 'API调度平台',
        ignoreKeepAlive: false,
        hideBreadcrumb: false,
        // hideMenu: true
      },
      children: [],
    },
    
  ],
};

export default develop;
