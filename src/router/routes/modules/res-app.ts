import type { AppRouteModule } from '@/router/types';

import { LAYOUT } from '@/router/constant';

const develop: AppRouteModule = {
  path: '/nrm/res-app',
  name: 'nrm-res-app',
  component: LAYOUT,
  meta: {
    orderNo: 10,
    icon: 'ion:extension-puzzle-outline',
    title: '资源应用',
    ignoreKeepAlive: false,
    keepalive: true,

    // hideMenu: true
  },
  children: [
    {
      path: 'faultpositioning',
      name: 'dashboard-workbench',
      component: () => import('@/views/nrm/res-app/groupfault/dashboard/workbench/index.vue'),

      meta: {
        title: '障碍处理工作台',
        ignoreKeepAlive: false,
        ignoreAuth: true,
        roles: ['障碍处理工作台'],
        keepalive: true,
      },
      children: [
        {
          path: 'dashboard-analysis',
          name: 'dashboard-analysis',

          component: () => import('@/views/nrm/res-app/groupfault/dashboard/analysis/index.vue'),
          meta: {
            hideMenu: true,
            title: '障碍分析',
            ignoreKeepAlive: false,
            keepalive: true,
          },
          children: [],
        },

        // {
        //   path: 'dashboard-workbench',
        //   name: 'dashboard-workbench',
        //   component: () => import('@/views/nrm/res-app/groupfault/dashboard/workbench/index.vue'),
        //   meta: {
        //     title: '障碍工作台',
        //     ignoreKeepAlive: false,
        //     hideMenu: true,

        //   },
        //   children: [],
        // },

        {
          path: 'groupfault',
          name: 'groupfault',
          component: () => import('@/views/nrm/res-app/groupfault/groupfault-index.vue'),
          meta: {
            title: '障碍定位',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['障碍定位（菜单）'],
            keepalive: true,
          },
          children: [],
        },
        {
          path: 'groupfault-nantong',
          name: 'groupfault-nantong',
          component: () => import('@/views/nrm/res-app/groupfault/groupfault-nantong.vue'),
          meta: {
            title: '障碍定位（南通）',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['南通开发角色'],
            keepalive: true,
          },
          children: [],
        },

        {
          path: 'groupfault-tab',
          name: 'groupfault-tab',
          component: () => import('@/views/nrm/res-app/groupfault/groupfault-tab.vue'),
          meta: {
            title: '申告汇聚定位页',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['障碍视图（菜单）'],
            keepalive: true,
          },
          children: [],
        },

        {
          path: 'fault-hologram',
          name: 'fault-hologram',
          component: () =>
            import('@/views/nrm/res-app/groupfault/fault-hologram/fault-hologram-index.vue'),
          meta: {
            title: '障碍视图',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['障碍视图（菜单）'],
            keepalive: true,
          },
          // src/components/Table/index.ts
        },

        {
          path: 'zd-fault-positioning',
          name: 'zd-fault-positioning',
          component: () =>
            import('@/views/nrm/res-app/groupfault/fault-hologram/zd-fault-positioning.vue'),
          meta: {
            title: '障碍单定位',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['障碍视图（菜单）'],
            keepalive: true,
          },
          // src/components/Table/index.ts
        },

        {
          path: 'fault-hologram-new',
          name: 'fault-hologram-new',
          component: () =>
            import('@/views/nrm/res-app/groupfault/fault-hologram/fault-hologram-new.vue'),
          meta: {
            title: '障碍视图-新',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['障碍视图（菜单）'],
            keepalive: true,
          },
          // src/components/Table/index.ts
        },

        {
          path: 'faultIntercept',
          name: 'faultIntercept',
          component: () => import('@/views/nrm/res-app/groupfault/FaultIntercept.vue'),
          meta: {
            title: '发起拦截（障碍/割接）',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['主动拦截（菜单）'],
            keepalive: true,
          },
          children: [],
        },
        {
          path: 'querycause',
          name: 'querycause',
          component: () => import('@/views/nrm/res-app/groupfault/queryCause.vue'),
          meta: {
            title: '查询拦截记录',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['拦截查询（菜单）'],
            keepalive: true,
          },
          children: [],
        },
        {
          path: 'externalforcepoints',
          name: 'externalforcepoints',
          component: () =>
            import(
              '@/views/nrm/res-app/influenceanalysis/external-force-points/externalForceAnalysis-index.vue'
            ),
          meta: {
            title: '外力点管理',
            ignoreKeepAlive: false,
            ignoreAuth: true,
            hideMenu: true,
            roles: ['外力点管理（菜单）'],
            keepalive: true,
          },
          children: [],
        },
        {
          path: 'check-pon',
          name: 'check-pon',
          component: () => import('@/views/nrm/res-app/groupfault/poncheck/ponCheck.vue'),
          meta: {
            title: 'PON口实时在线检测',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['PON口在线检测（菜单）'],
            keepalive: true,
          },
          children: [],
        },
        {
          path: 'otdrPositioning',
          name: 'otdrPositioning',
          component: () => import('@/views/nrm/res-app/groupfault/otdrPositioning.vue'),
          meta: {
            title: '光缆断点定位',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['PON口在线检测（菜单）'],
            keepalive: true,
          },
          children: [],
        },
        {
          path: 'ExportResInfluence',
          name: 'ExportResInfluence',
          component: () => import('@/views/nrm/res-app/groupfault/ExportResInfluence.vue'),
          meta: {
            title: '资源影响导出',
            ignoreKeepAlive: false,
            hideMenu: true,
            roles: ['资源影响分析导出（菜单）'],
            keepalive: true,
          },
          children: [],
        },
      ],
    },


    {
      path: 'risk-manage',
      name: 'risk-manage',
      component: () => import('@/views/nrm/res-app/risk-analyse/risk_dash/workbench/index_copy.vue'),
      meta: {
        title: '双路由整治工作台',
        ignoreKeepAlive: false,
        keepalive: true,
      },
      children: [
        {
          path: 'risk-faultpositioning',
          name: 'RiskDashboardWorkbench',
          component: () => import('@/views/nrm/res-app/risk-analyse/risk_dash/workbench/index_copy.vue'),
          meta: {
            title: '双路由整治工作台',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'group_obstacle',
          name: 'GroupObstacle',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/group_obstacle/group_obstacle_management.vue'),
          meta: {
            title: '隐患排查',
            ignoreKeepAlive: false,
            hideMenu: true,
            //ignoreAuth: true,
            // 隐患排查入口下线，只保留admin权限
            roles: ['admin'],
            keepalive: true,
          },
        },
        {
          path: 'circuit_pair_management',
          name: 'CircuitPairManagement',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/circuit-pair/circuit_pair_management.vue'),
          meta: {
            title: '电路保护组管理',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            roles: ['隐患排查管理角色', '系统管理角色', 'admin'],
            keepalive: true,
          },
        },
        {
          path: 'opt_road_group_management',
          name: 'OptRoadGroupManagement',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/opt-road/opt_road_group_management.vue'),
          meta: {
            title: '光路保护组管理',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            roles: ['隐患排查管理角色', '系统管理角色', 'admin'],
            keepalive: true,
          },
        },
        {
          path: 'opt_road_group_statistics',
          name: 'OptRoadGroupStatistics',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/opt-road/opt_road_group_statistics.vue'),
          meta: {
            title: '光路组统计分析',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            roles: ['隐患排查管理角色', '系统管理角色', 'admin'],
            keepalive: true,
            currentActiveMenu: '/nrm/res-app/risk-manage/opt_road_group_management',
          },
        },
        {
          path: 'dual_route_operations_analytics',
          name: 'DualRouteOperationsAnalytics',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/opt-road/dual_route_operations_analytics.vue'),
          meta: {
            title: '双路由运营数据分析',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            roles: ['隐患排查管理角色', '系统管理角色', 'admin'],
            keepalive: true,
            currentActiveMenu: '/nrm/res-app/risk-manage/opt_road_group_management',
          },
        },
        {
          path: 'dual_route_operation_log',
          name: 'DualRouteOperationLog',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/opt-road/dual_route_operation_log.vue'),
          meta: {
            title: '双路由管理操作日志',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            roles: ['隐患排查管理角色', '系统管理角色', 'admin'],
            keepalive: true,
            currentActiveMenu: '/nrm/res-app/risk-manage/opt_road_group_management',
          },
        },
        {
          path: 'dual_route_log_analytics',
          name: 'DualRouteLogAnalytics',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/opt-road/dual_route_log_analytics.vue'),
          meta: {
            title: '双路由日志分析',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            hideBreadcrumb: false,
            keepalive: true,
            currentActiveMenu: '/nrm/res-app/risk-manage/opt_road_group_management',
          },
        },

        {
          path: 'AccessDeviceUplink',
          name: 'AccessDeviceUplink',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/access-device-uplink/AccessDeviceUplink.vue'),
          meta: {
            title: '接入设备管理',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            roles: ['隐患排查管理角色', '系统管理角色', 'admin'],
            keepalive: true,
          },
        },
        {
          path: 'data_overview',
          name: 'DataOverview',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/data_overview/data_overview.vue'),
          meta: {
            title: '光路数据总览',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            roles: ['隐患排查管理角色', '系统管理角色', 'admin'],
            keepalive: true,
          },
        },
        // {
        //   path: 'opt_road_group_management',
        //   name: 'opt_road_group_management',
        //   component: () =>
        //     import('@/views/nrm/res-app/risk-analyse/opt-road/opt_road_group_management.vue'),
        //   meta: {
        //     title: '光路保护组管理',
        //     ignoreKeepAlive: false,
        //     hideMenu: false,
        //     ignoreAuth: true,
        //   },
        // },
        // {
        //   path: 'AccessDeviceUplink',
        //   name: 'AccessDeviceUplink',
        //   component: () =>
        //     import('@/views/nrm/res-app/risk-analyse/access-device-uplink/AccessDeviceUplink.vue'),
        //   meta: {
        //     title: '接入设备安全管理',
        //     ignoreKeepAlive: false,
        //     hideMenu: false,
        //     ignoreAuth: true,
        //   },
        // },

        {
          path: 'risk_show_access_device_uplink/:id',
          name: 'risk_show_access_device_uplink',
          component: () =>
            import(
              '@/views/nrm/res-app/risk-analyse/access-device-uplink/risk_show_access_device_uplink.vue'
            ),
          meta: {
            title: '接入设备光路组双路由',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
          },
        },

        {
          path: 'life_circuit_risk_analyze',
          name: 'life_circuit_risk_analyze',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/life-circuit/life_circuit_risk_analyze.vue'),
          meta: {
            title: '生命线业务隐患分析',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'risk-show/:type/:id',
          name: 'risk-show',
          component: () => import('@/views/nrm/res-app/risk-analyse/circuit-pair/risk-show.vue'),
          meta: {
            title: '电路双路由',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'risk-show-opt-road/:id',
          name: 'risk-show-opt-road',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/opt-road/risk-show-opt-road.vue'),
          meta: {
            title: '光路组双路由',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'smart_route',
          name: 'smart_route',
          component: () => import('@/views/nrm/res-app/risk-analyse/opt-road/smart_route.vue'),
          meta: {
            title: '智能推荐',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'lifeline-management',
          name: 'LifelineManagement',
          component: () => import('@/views/nrm/res-app/cust-view/cust-view-list.vue'),
          meta: {
            title: '生命线管理',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'lifeline-management/cust-view-show/:id',
          name: 'LifelineManagementCustViewShow',
          component: () => import('@/views/nrm/res-app/cust-view/cust-view-show.vue'),
          meta: {
            title: '客户视图呈现',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'hazard-remediation-list',
          name: 'HazardRemediationList',
          component: () => import('@/views/nrm/res-app/risk-analyse/hazard-remediation/HazardRemediationList.vue'),
          meta: {
            title: '隐患整改管理',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'hazard-remediation-form',
          name: 'HazardRemediationForm',
          component: () => import('@/views/nrm/res-app/risk-analyse/hazard-remediation/HazardRemediationForm.vue'),
          meta: {
            title: '发起隐患整改',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        // {
        //   path: 'hazard-remediation-view',
        //   name: 'HazardRemediationView',
        //   component: () => import('@/views/nrm/res-app/risk-analyse/hazard-remediation/HazardRemediationView.vue'),
        //   meta: {
        //     title: '隐患整改详情',
        //     ignoreKeepAlive: false,
        //     hideMenu: true,
        //     ignoreAuth: true,
        //     keepalive: true,
        //   },
        // },

        {
          path: 'smart-route-page/:opticalPathListString/:ds',
          name: 'smart-route-page',
          component: () => import('@/views/nrm/res-app/risk-analyse/opt-road/smart-route-page.vue'),
          meta: {
            title: '智能路由推荐',
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'risk-show-zuwangshebei/:id',
          name: 'risk-show-zuwangshebei',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/zuwangshebei/risk-show-zuwangshebei.vue'),
          meta: {
            title: '组网设备光路组双路由',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'risk-show-singleCircuit/:type/:id',
          name: 'risk-show-singleCircuit',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/life-circuit/risk-show-singleCircuit.vue'),
          meta: {
            title: '双路由呈现',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },

        {
          path: 'white_list_management',
          name: 'white_list_management',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/white-list/white_list_management.vue'),
          meta: {
            title: '白名单管理',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'white_add/:id',
          name: 'white_add',
          component: () => import('@/views/nrm/res-app/risk-analyse/white-list/white_add.vue'),
          meta: {
            title: '白名单新增',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'white_add',
          name: 'white_add_add',
          component: () => import('@/views/nrm/res-app/risk-analyse/white-list/white_add.vue'),
          meta: {
            title: '白名单新增',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'white_deal_list',
          name: 'white_deal_list',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/white-list/white_deal_list.vue'),
          meta: {
            title: '白名单审核待办',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'risk_add/:id/:taskId',
          name: 'risk_add',
          component: () => import('@/views/nrm/res-app/risk-analyse/risk-flow/risk_add.vue'),
          meta: {
            title: '隐患派单',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'risk_add',
          name: 'risk_add_add',
          component: () => import('@/views/nrm/res-app/risk-analyse/risk-flow/risk_add.vue'),
          meta: {
            title: '隐患派单',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'risk_deal_list',
          name: 'risk_deal_list',
          component: () => import('@/views/nrm/res-app/risk-analyse/risk-flow/risk_deal_list.vue'),
          meta: {
            title: '隐患审核待办',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },

        //               {
        //                 path: 'access_device_uplink',
        //                 name: 'access_device_uplink',
        //                 component: () => import('@/views/nrm/res-app/risk-analyse/access_device_uplink.vue'),
        //                 meta: {
        //                   title: '接入设备安全管理',
        //                   ignoreKeepAlive: false,
        //                   hideMenu: false,
        //                 },
        //               },


        {
          path: 'res-view',
          name: 'res-view',
          component: LAYOUT,
          meta: {
            title: '资源视图',
            ignoreKeepAlive: false,
            ignoreAuth: true,
            hideBreadcrumb: false,
            keepalive: true,
            hideMenu:true
          },
          children: [
            {
              path: 'zuwang_shebei',
              name: 'zuwang_device',
              component: LAYOUT,
              meta: {
                title: '组网视图',
                ignoreKeepAlive: false,
                ignoreAuth: true,
                hideBreadcrumb: false,
                keepalive: true,
              },
              children: [
                {
                  path: 'AccessDevice_Uplink',
                  name: 'AccessDevice_Uplink',
                  component: () =>
                    import(
                      '@/views/nrm/res-app/risk-analyse/access-device-uplink/AccessDeviceUplink.vue'
                    ),
                  meta: {
                    title: '接入设备',
                    ignoreKeepAlive: false,
                    hideMenu: false,
                    keepalive: true,
                  },
                },

                {
                  path: 'zuwang_shebei',
                  name: 'zuwang_shebei',
                  component: () =>
                    import('@/views/nrm/res-app/risk-analyse/zuwangshebei/zuwangshebei.vue'),
                  meta: {
                    title: '传输',
                    ignoreKeepAlive: false,
                    hideMenu: false,
                    keepalive: true,
                  },
                },
                {
                  path: 'zuwang_shebei_ipran',
                  name: 'zuwang_shebei_ipran',
                  component: () =>
                    import('@/views/nrm/res-app/risk-analyse/zuwangshebei/zuwangshebei-ipran.vue'),
                  meta: {
                    title: 'IPRAN/STN',
                    ignoreKeepAlive: false,
                    hideMenu: false,
                    keepalive: true,
                  },
                },
                {
                  path: 'zuwang_shebei1',
                  name: 'zuwang_shebei1',
                  component: LAYOUT,
                  meta: {
                    title: '数据',
                    ignoreKeepAlive: false,
                    hideMenu: false,
                    keepalive: true,
                  },
                },
                {
                  path: 'zuwang_shebei3',
                  name: 'zuwang_shebei3',
                  component: LAYOUT,
                  meta: {
                    title: '无线',
                    ignoreKeepAlive: false,
                    hideMenu: false,
                    keepalive: true,
                  },
                },
                {
                  path: 'oltViewList',
                  name: 'oltViewList',
                  component: () => import('@/views/nrm/res-app/cust-view/topo/oltViewList.vue'),
                  meta: {
                    title: 'OLT',
                    ignoreKeepAlive: false,
                    ignoreAuth: true,
                    keepalive: true,
                  },
                  children: [],
                },
              ],
            },

            {
              path: 'risk_show/:type/:id',
              name: 'risk_show',
              component: () => import('@/views/nrm/res-app/risk-analyse/circuit-pair/risk-show.vue'),
              meta: {
                title: '双路由',
                ignoreKeepAlive: false,
                hideMenu: true,
                keepalive: true,
              },
            },
            {
              path: 'risk_show_opt_road/:id',
              name: 'risk_show_opt_road',
              component: () =>
                import('@/views/nrm/res-app/risk-analyse/opt-road/risk-show-opt-road.vue'),
              meta: {
                title: '光路组双路由',
                ignoreKeepAlive: false,
                hideMenu: true,
                keepalive: true,
              },
            },
            {
              path: 'risk_show_zuwangshebei/:id',
              name: 'risk_show_zuwangshebei',
              component: () =>
                import('@/views/nrm/res-app/risk-analyse/zuwangshebei/risk-show-zuwangshebei.vue'),
              meta: {
                title: '重要设备光路组双路由',
                ignoreKeepAlive: false,
                hideMenu: true,
                keepalive: true,
              },
            },
            {
              path: 'zuwangshebei_ipran_detail/:id',
              name: 'zuwangshebei_ipran_detail',
              component: () =>
                import('@/views/nrm/res-app/risk-analyse/zuwangshebei/zuwangshebei-ipran-detail.vue'),
              meta: {
                title: '重要设备光路组双路由',
                ignoreKeepAlive: false,
                hideMenu: true,
                keepalive: true,
              },
            },

            {
              path: 'risk_show_singleCircuit/:type/:id',
              name: 'risk_show_singleCircuit',
              component: () =>
                import('@/views/nrm/res-app/risk-analyse/life-circuit/risk-show-singleCircuit.vue'),
              meta: {
                title: '双路由呈现',
                ignoreKeepAlive: false,
                hideMenu: true,
                keepalive: true,
              },
            },
            {
              path: 'opt_cableview',
              name: 'opt_cableview',
              component: () => import('@/views/nrm/res-app/risk-analyse/opt_cable_view.vue'),
              meta: {
                title: '光缆网视图',
                ignoreKeepAlive: false,
                hideMenu: false,
                keepalive: true,
              },
            },
            {
              path: 'smart-route-adjustment',
              name: 'SmartRouteAdjustment',
              component: () => import('@/views/nrm/res-app/risk-analyse/smart_route_adjustment.vue'),
              meta: {
                title: '双路由整改方案智能输出',
                ignoreKeepAlive: false,
                keepalive: true,
              },
            },

          ],
        },

      ],
    },
    /*
    {
      path: 'cableview',
      name: 'cableview',
      //src\views\nrm\gis\gis_DSJ\cutover-index.vue
      //gis_DSJ_fullscreen
      //src\views\nrm\cableView\cableView.vue
      component: () => import('@/views/nrm/res-app/cableView/cutover-index.vue'), //todo
      meta: {
        title: '光缆视图',
        ignoreKeepAlive: false,
        ignoreAuth: true,
      },
      children: [],
    },
    {
      path: 'cable-hot',
      name: 'cable-hot',
      //src\views\nrm\gis\gis_DSJ\cutover-index.vue
      //gis_DSJ_fullscreen
      //src\views\nrm\cableView\cableView.vue
      component: () => import('@/views/nrm/res-app/cableView/cable-hot.vue'), //todo
      meta: {
        title: '光缆热力图',
        ignoreKeepAlive: false,
        ignoreAuth: true,
      },
      children: [],
    },

     */

    // D:\code\nrm-web-app\src\views\nrm\influenceanalysis

    {
      path: 'cut-over',
      name: 'influenceanalysis',
      redirect: '/nrm/res-app/cut-over/cut-over',
      component: () => import('/@/views/nrm/res-app/cutover/cutover-list.vue'), //todo
      meta: {
        title: '割接流程',
        ignoreKeepAlive: false,
        ignoreAuth: true,
        keepalive: true,
      },
      children: [
        {
          path: 'cut-over/new',
          name: 'cut-over-new',
          component: () => import('/@/views/nrm/res-app/cutover/cutover-index.vue'), //todo
          meta: {
            title: '割接确认发起',
            ignoreKeepAlive: false,
            ignoreAuth: true,
            hideMenu: true,
            keepalive: true,
          },
          children: [],
        },
        {
          path: 'cut-over/:id',
          name: 'cut-over-info',
          component: () => import('/@/views/nrm/res-app/cutover/cutover-index.vue'), //todo
          meta: {
            title: '割接确认',
            ignoreKeepAlive: false,
            ignoreAuth: true,
            hideMenu: true,
            keepalive: true,
          },
          children: [],
        },
        {
          path: 'cut-over',
          name: 'cut-over-list',
          //src\views\nrm\gis\gis_DSJ\cutover-index.vue
          //gis_DSJ_fullscreen
          //src\views\nrm\cableView\cableView.vue
          component: () => import('/@/views/nrm/res-app/cutover/cutover-list.vue'), //todo
          meta: {
            title: '割接单查询',
            ignoreKeepAlive: false,
            ignoreAuth: true,
            hideMenu: true,
            keepalive: true,
          },
          children: [],
        },
      ],
    },

    {
      path: 'business-view',
      name: 'business-view',
      component: LAYOUT,
      meta: {
        title: '业务视图',
        ignoreKeepAlive: false,
        ignoreAuth: true,
        hideBreadcrumb: false,
        keepalive: true,
      },
      children: [
        {
          path: 'cust_view_circuit_analyze/:id',
          name: 'cust_view_circuit_analyze',
          component: () => import('@/views/nrm/res-app/cust-view/cust-view-circuit-analyze.vue'),
          meta: {
            title: '客户风险分析',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'circuits-order-list',
          name: 'circuits-order-list',
          component: () => import('@/views/nrm/res-app/cust-view/circuits-order-list.vue'),
          meta: {
            title: '业务电路查询',
            ignoreKeepAlive: false,
            hideMenu: false,
            ignoreAuth: true,
            keepalive: true,
          },
        },
        {
          path: 'logical-topology',
          name: 'logical-topology',
          component: LAYOUT,
          meta: {
            title: '业务组网拓扑',
            ignoreKeepAlive: false,
            hideMenu: false,
            ignoreAuth: true,
            keepalive: true,
          },
          children: [
            {
              path: 'mstp-gis-v2',
              name: 'mstp-gis-v2',
              component: () => import('@/views/nrm/res-app/cust-view/topo/mstp.vue'),
              meta: {
                title: '本地组网专线',
                ignoreKeepAlive: false,
                ignoreAuth: true,
                keepalive: true,
              },
              children: [],
            },
            {
              path: 'otn',
              name: 'otn',
              component: () => import('@/views/nrm/res-app/cust-view/topo/otn.vue'),
              meta: {
                title: 'OTN精品专线',
                ignoreKeepAlive: false,
                ignoreAuth: true,
                keepalive: true,
              },
              children: [],
            },
            {
              path: 'changtu',
              name: 'changtu',
              component: () => import('@/views/nrm/res-app/cust-view/topo/changtu.vue'),
              meta: {
                title: '长途专线',
                ignoreKeepAlive: false,
                ignoreAuth: true,
                keepalive: true,
              },
              children: [],
            },
            {
              path: 'opt-road',
              name: 'OptRoad',
              component: () => import('@/views/nrm/res-app/cust-view/topo/opt-road.vue'),
              meta: {
                title: '光路',
                ignoreKeepAlive: false,
                ignoreAuth: true,
                keepalive: true,
              },
              children: [],
            },
          ],
        },

        {
          path: 'hologram',
          name: 'hologram',
          component: () => import('@/views/nrm/res-app/cust-view/topo/hologram.vue'),
          meta: {
            title: '业务电路全息视图',
            ignoreKeepAlive: false,
            ignoreAuth: true,
            keepalive: true,
          },
          children: [],
        },

        {
          path: 'gov_enterprise_customer_health_record',
          name: 'gov_enterprise_customer_health_record',
          component: () =>
            import('@/views/nrm/res-app/risk-analyse/gov_enterprise_customer_health_record.vue'),
          meta: {
            title: '政企客户健康档案',
            ignoreKeepAlive: false,
            keepalive: true,
          },
        },
        {
          path: 'circuitAudit',
          name: 'circuitAudit',
          component: () => import('@/views/nrm/res-app/cust-view/topo/circuitAudit.vue'),
          meta: {
            title: '电路稽核',
            ignoreKeepAlive: false,
            ignoreAuth: true,
            keepalive: true,
          },
          children: [],
        },

        {
          path: 'customer_health_view/:id',
          name: 'customer_health_view',
          component: () =>
            import(
              '@/views/nrm/res-app/risk-analyse/gov_enterprise_customer_health_record_view.vue'
            ),
          meta: {
            title: '客户详情',
            ignoreKeepAlive: false,
            hideMenu: true,
            ignoreAuth: true,
            keepalive: true,
          },
        },
      ],
    },

    {
      path: 'test-fun',
      name: 'test-fun',
      meta: {
        title: '测试功能',
        ignoreKeepAlive: false,
        ignoreAuth: true,
        hideBreadcrumb: false,
        roles: ['测试菜单'],
        keepalive: true,
      },
      children: [
        {
          path: 'test',
          name: 'test',
          component: () => import('@/views/nrm/res-app/test/test.vue'),
          meta: {
            title: '权限测试菜单',
            ignoreKeepAlive: false,
            roles: ['测试菜单'],
            keepalive: true,
          },
        },
        {
          path: 'nt-test',
          name: 'nt-test',
          component: () => import('@/views/nrm/res-app/test/test-nt.vue'),
          meta: {
            title: '权限测试菜单(南通)',
            ignoreKeepAlive: false,
            roles: ['南通开发角色'],
            keepalive: true,
          },
        },
        {
          path: 'circuits-order-list-test',
          name: 'circuits-order-list-test',
          component: () => import('@/views/nrm/res-app/test/circuits-order-list.vue'),
          meta: {
            title: '业务电路查询',
            ignoreKeepAlive: false,
            hideMenu: false,
            ignoreAuth: true,
            roles: ['测试菜单'],
            keepalive: true,
          },
        },
        {
          path: 'ai-dialog',
          name: 'ai-dialog',
          component: () => import('@/views/nrm/res-app/ai-dialog/ai-dialog-index.vue'),
          meta: {
            title: 'AI对话',
            ignoreKeepAlive: false,
            hideMenu: false,
            ignoreAuth: true,
            roles: ['测试菜单'],
            keepalive: true,
          },
        },
        {
          path: 'about',
          name: 'about',
          component: () => import('@/views/nrm/res-app/test/about.vue'),
          meta: {
            title: '关于',
            ignoreKeepAlive: false,
            roles: ['测试菜单'],
            keepalive: true,
          },
        },
       {
          path: 'resource-warning',
          name: 'ResourceWarning',
          component: () => import('@/views/nrm/res-app/risk-analyse/resource-warning/resourcewarning.vue'),
          meta: {
            title: '资源预警',
            ignoreKeepAlive: false,
            roles: ['测试菜单'],
            keepalive: true,
          },
        children: [
          {
            path: 'fiber-cable-health',
            name: 'fiber-cable-health',
            component: () => import('@/views/nrm/res-app/risk-analyse/resource-warning/fiber-cable-health.vue'),
           meta: {
                      title: '光缆占用健康度预警',
                      ignoreKeepAlive: false,
                      hideMenu: true,
                      ignoreAuth: true,
                      roles: ['测试菜单'],
                      keepalive: true,
                    },
          },
        {
                    path: 'splitter-port',
                    name: 'splitter-port',
                    component: () => import('@/views/nrm/res-app/risk-analyse/resource-warning/splitter-port.vue'),
                        meta: {
                                        title: '分光器端口预警',
                                        ignoreKeepAlive: false,
                                        hideMenu: true,
                                        ignoreAuth: true,
                                        roles: ['测试菜单'],
                                        keepalive: true,
                                      },
                  },
                {
                            path: 'external-force-point',
                            name: 'external-force-point',
                            component: () => import('@/views/nrm/res-app/risk-analyse/resource-warning/external-force-point.vue'),
                                meta: {
                                                title: '光缆外力点隐患预警',
                                                ignoreKeepAlive: false,
                                                hideMenu: true,
                                                ignoreAuth: true,
                                                roles: ['测试菜单'],
                                                keepalive: true,
                                              },
                          },

        ]
       },
      ],
    },

    /*{
      path: 'gl-gj',
      name: 'gl-gj',
      component: LAYOUT,
      redirect: '/nrm/res-app/gl-gj/pair_fiber_upload',

      //redirect: {to :'/res-app/cust-view/cust-view-list'},
      meta: {
        title: '光缆割接资料准备',
        ignoreKeepAlive: true,
      },
      children: [
        {
          path: 'pair_fiber_upload',
          name: 'pair_fiber_upload',
          component: () => import('/@/views/nrm/gl_gj/pair_fiber_upload.vue'),
          meta: {
            title: '光路组导入',
            ignoreKeepAlive: false,
            hideMenu: true,
          }
        }
      ],
    },*/

    // {
    //   path: 'otn-check',
    //   name: 'otn-check',
    //   component: LAYOUT,
    //   meta: {
    //     title: '电路稽核',
    //     ignoreKeepAlive: false,
    //     ignoreAuth: true,
    //     hideBreadcrumb: false,
    //   },
    //   children: [
    //     {
    //       path: 'otn-check-res',
    //       name: 'otn-check-res',
    //       component: () => import('@/views/nrm/res-app/otn-check/otn-check-res.vue'),
    //       meta: {
    //         title: 'OTN稽核结果报表',
    //         ignoreKeepAlive: false,
    //       },
    //       children: [],
    //     },
    //     {
    //       path: 'circuit-check-res',
    //       name: 'circuit-check-res',
    //       component: () => import('@/views/nrm/res-app/otn-check/circuit-check-res.vue'),
    //       meta: {
    //         title: 'OTN电路稽核详情',
    //         ignoreKeepAlive: false,
    //       },
    //       children: [],
    //     },
    //     {
    //       path: 'circuit-check-res-info/:line_no',
    //       name: 'circuit-check-res-info',
    //       component: () => import('@/views/nrm/res-app/otn-check/circuit-check-res-info.vue'),
    //       meta: {
    //         title: 'OTN电路稽核清单',
    //         ignoreKeepAlive: false,
    //         hideMenu: true,
    //       },
    //       children: [],
    //     },
    //   ],
    // },
  ],
};

export default develop;
