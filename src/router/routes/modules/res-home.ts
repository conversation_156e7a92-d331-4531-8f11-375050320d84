import type { AppRouteModule } from '@/router/types';

import { LAYOUT } from '@/router/constant';

const develop: AppRouteModule = {
  path: '/nrm/res-home',
  name: 'nrm-res-home',
  component: LAYOUT,
  redirect: '/nrm/res-home/home',
  meta: {
    orderNo: 1,
    icon: 'ion:home-outline',
    title: '首页',
    ignoreKeepAlive: false,

    
  },
  children: [
    {
      path: 'home',
      name: 'home',
      component: () => import('@/views/nrm/home/<USER>'),
      meta: {
        title: '首页',
        ignoreKeepAlive: false,
        hideBreadcrumb: false,
        hideMenu: true,
        keepalive: true,
      },
      children: [],
    },
    /*
    {
      path: 'todo-list',
      name: 'todo-list',
      component: () => import('@/views/nrm/home/<USER>/cutover-ai-dialog-index.vue'),
      meta: {
        title: '我的待办',
        ignoreKeepAlive: false,
        hideBreadcrumb: false,
        // hideMenu: true
      },
      children: [],
    },
     */
  ],
};

export default develop;
