import type { AppRouteModule } from '@/router/types';
import { RoleEnum } from '@/enums/roleEnum';
import { LAYOUT } from '@/router/constant';

const develop: AppRouteModule = {
  path: '/nrm/res-manage',
  name: 'nrm-res-manages',
  component: LAYOUT,

  meta: {
    orderNo: 11,
    icon: 'ion:git-network-outline',
    title: '资源管理',
    ignoreKeepAlive: false,

    // hideMenu: true
  },
  children: [
    {
      path: 'pon',
      name: 'pon',
      meta: {
        title: 'PON专业',
        ignoreKeepAlive: false,
      },
      children: [
        {
          path: 'pon_manage_ip',
          name: 'pon_manage_ip',
          component: () => import('@/views/nrm/res-manage/pon/pon_ip_index.vue'),
          meta: {
            title: 'PON管理IP查询',
            ignoreKeepAlive: false,
          },
          children: [],
        },
      ],
    },
    {
      path: 'ip',
      name: 'ip',
      meta: {
        title: 'IP地址管理',
        ignoreKeepAlive: false,
      },
      children: [
        // {
        //   path: 'ip-classify',
        //   name: 'ip-classify',
        //   meta: {
        //     title: 'IP地址分类管理',
        //     ignoreKeepAlive: false,
        //   },
        //   children: [
        //     {
        //       path: 'ip-use-for',
        //       name: 'ip-use-for',
        //       component: () => import('@/views/nrm/res-manage/ip-use-for/ai-dialog-index.vue'),
        //       meta: {
        //         title: 'IP地址用途管理',
        //         ignoreKeepAlive: false,
        //       },
        //       children: [],
        //     },
        //     {
        //       path: 'ip-net',
        //       name: 'ip-net',
        //       component: () => import('@/views/nrm/res-manage/ip-net/ai-dialog-index.vue'),
        //       meta: {
        //         title: 'IP网络管理',
        //         ignoreKeepAlive: false,
        //       },
        //       children: [],
        //     },
        //     {
        //       path: 'ip-pool',
        //       name: 'ip-pool',
        //       component: () => import('@/views/nrm/res-manage/ip-pool/ai-dialog-index.vue'),
        //       meta: {
        //         title: 'IP地址池规划',
        //         ignoreKeepAlive: false,
        //       },
        //       children: [],
        //     },
        //   ],
        // },
        {
          path: 'ip-pool/:id/split',
          name: 'ip-pool-split',
          component: () => import('@/views/nrm/res-manage/ip-pool/ip-pool-split.vue'),
          meta: {
            title: 'IP拆分查询',
            ignoreKeepAlive: false,
            hideMenu: true,
          },
          children: [],
        },
        {
          path: 'ip-pool/:id/segment',
          name: 'ip-pool-segment',
          component: () => import('@/views/nrm/res-manage/ip-pool/ip-segment/index.vue'),
          meta: {
            title: 'IP段查询',
            ignoreKeepAlive: false,
            hideMenu: true,
          },
          children: [],
        },
        {
          path: 'ip-segment-mgr',
          name: 'IpSegmentMgr',
          component: () => import('@/views/nrm/res-manage/ip-segment-mgr/index.vue'),
          meta: {
            title: 'IP地址规划段导入',
            ignoreKeepAlive: false,
          },
          children: [],
        },
        {
          path: 'ip-assign',
          name: 'ip-assign',
          component: () => import('@/views/nrm/res-manage/ip-assign/index.vue'),
          meta: {
            title: 'IP地址拆分导入',
            ignoreKeepAlive: false,
          },
          children: [],
        },
        {
          path: 'ip-split',
          name: 'ip-split',
          component: () => import('@/views/nrm/res-manage/ip-mgr/index.vue'),
          meta: {
            title: 'IP地址查询占用修改',
          },
          children: [],
        },
        // {
        //   path: 'ip-mgr',
        //   name: 'ip-mgr',
        //   meta: {
        //     title: 'IP地址段管理',
        //   },
        //   children: [
        //     {
        //       path: 'ip-segment-mgr',
        //       name: 'IpSegmentMgr',
        //       component: () => import('@/views/nrm/res-manage/ip-segment-mgr/ai-dialog-index.vue'),
        //       meta: {
        //         title: 'IP地址规划段导入',
        //       },
        //       children: [],
        //     },
        //     {
        //       path: 'ip-assign',
        //       name: 'ip-assign',
        //       component: () => import('@/views/nrm/res-manage/ip-assign/ai-dialog-index.vue'),
        //       meta: {
        //         title: 'IP地址拆分导入',
        //       },
        //       children: [],
        //     },
        //     {
        //       path: 'ip-split',
        //       name: 'ip-split',
        //       component: () => import('@/views/nrm/res-manage/ip-mgr/ai-dialog-index.vue'),
        //       meta: {
        //         title: 'IP地址查询占用修改',
        //       },
        //       children: [],
        //     },
        //   ],
        // },
        {
          path: 'res-ip-used',
          name: 'res-ip-used',
          component: () => import('@/views/nrm/res-manage/res-ip-used/index.vue'),
          meta: {
            title: '资源系统分配的IP',
            ignoreKeepAlive: false,
          },
          children: [],
        },
        // {
        //   path: 'device-ip-mgr',
        //   name: 'device-ip-mgr',
        //   meta: {
        //     title: '业务及设备IP地址管理',
        //     ignoreKeepAlive: false,
        //   },
        //   children: [
        //     {
        //       path: 'res-ip-used',
        //       name: 'res-ip-used',
        //       component: () => import('@/views/nrm/res-manage/res-ip-used/ai-dialog-index.vue'),
        //       meta: {
        //         title: '资源系统分配的IP',
        //       },
        //       children: [],
        //     },
        //   ],
        // },
      ],
    },

    {
      path: 'station',
      name: 'station',
      component: () => import('@/views/nrm/res-manage/station/index.vue'),
      meta: {
        title: '局站信息查询',
        ignoreKeepAlive: false,
        hideMenu: true,
      },
      children: [],
    },

    {
      path: 'vlan_manage',
      name: 'vlan_manage',
      component: () => import('@/views/nrm/res-manage/station/vlan_manage.vue'),
      meta: {
        title: 'VLAN管理查询',
        ignoreKeepAlive: false,
        hideMenu: true,
      },
      children: [],
    },
    {
      path: 'vlan_group_manage',
      name: 'vlan_group_manage',
      component: () => import('@/views/nrm/res-manage/station/vlan_group_manage.vue'),
      meta: {
        title: 'VLAN组管理查询',
        ignoreKeepAlive: false,
        hideMenu: true,
      },
      children: [],
    },
    {
      path: 'vlan_vpn_manage',
      name: 'vlan_vpn_manage',
      component: () => import('@/views/nrm/res-manage/station/vlan_vpn_manage.vue'),
      meta: {
        title: '新老城边界VLAN和VPN管理',
        ignoreKeepAlive: false,
        hideMenu: true,
      },
      children: [],
    },
    {
      path: 'two_lines',
      name: 'two_lines',
      meta: {
        title: '城域网两线',
        ignoreKeepAlive: false,
      },
      children: [
        {
          path: 'check_right',
          name: 'check_right',
          component: () => import('@/views/nrm/res-manage/station/device_index.vue'),
          meta: {
            title: '设备信息查询',
            ignoreKeepAlive: false,
          },
          children: [],
        },
        {
          path: 'vpn_params',
          name: 'vpn_params',
          component: () => import('@/views/nrm/res-manage/vpn-instance/vpn_index.vue'),
          meta: {
            title: 'VPN参数查询',
            ignoreKeepAlive: false,
            roles: [RoleEnum.VpnRole],
          },
          children: [],
        },
        {
          path: 'vpn_user',
          name: 'vpn_user',
          component: () => import('@/views/nrm/res-manage/vpn-user/vpn_users.vue'),
          meta: {
            title: '专线用户资料',
            ignoreKeepAlive: false,
            roles: [RoleEnum.Duplet],
          },
          children: [],
        },
        {
          path: 'new_85',
          name: 'new_85',
          component: () => import('@/views/nrm/res-manage/new85/new85_index.vue'),
          meta: {
            title: '新85表查询',
            ignoreKeepAlive: false,
            roles: [RoleEnum.Duplet],
          },
          children: [],
        },
        {
          path: 'old_85',
          name: 'old_85',
          component: () => import('@/views/nrm/res-manage/old85/old85_index.vue'),
          meta: {
            title: '老85表查询',
            ignoreKeepAlive: false,
            roles: [RoleEnum.Duplet],
          },
          children: [],
        },
        {
          path: 'relay_ip',
          name: 'relay_ip',
          component: () => import('@/views/nrm/res-manage/relay-ip-assign/indexs.vue'),
          meta: {
            title: '互联IP分配',
            ignoreKeepAlive: false,
          },
          children: [],
        },
        {
          path: 'vrf',
          name: 'vrf',
          component: () => import('@/views/nrm/res-manage/vrf/vrf_index.vue'),
          meta: {
            title: 'VRF查询',
            ignoreKeepAlive: false,
            roles: [RoleEnum.Duplet],
          },
          children: [],
        },
        {
          path: 'vpnUserProfile',
          name: 'vpnUserProfile',
          component: () => import('@/views/nrm/res-manage/vpnUserProfile/vpnUserProfile.vue'),
          meta: {
            title: '二层vpn用户资料',
            ignoreKeepAlive: false,
            roles: [RoleEnum.Duplet],
          },
          children: [],
        },
      ],
    },
    {
      path: 'ipran_line_infor',
      name: 'ipran_line_infor',
      component: () => import('@/views/nrm/res-manage/ipran-line/ipran_line_infor.vue'),
      meta: {
        title: 'IPRAN专线资料',
        ignoreKeepAlive: false,
        // hideMenu: true,
      },
      children: [],
    },
    {
      path: 'ipran_insert_topo',
      name: 'ipran_insert_topo',
      component: () => import('@/views/nrm/res-manage/ipran-line/ipran_insert_topo.vue'),
      meta: {
        title: 'IPRAN接入TOPO信息',
        ignoreKeepAlive: false,
        // hideMenu: true,
      },
      children: [],
    },
    {
      path: 'otn-check-res',
      name: 'otn-check-res',
      component: () => import('@/views/nrm/res-app/otn-check/otn-check-res.vue'),
      meta: {
        title: 'OTN稽核结果',
        ignoreKeepAlive: false,
      },
      children: [],
    },
    {
      path: 'idc-audit',
      name: 'idc-audit',
      component: () => import('@/views/nrm/res-app/idc-audit/App.vue'),
      meta: {
        title: 'IDC智审',
        ignoreKeepAlive: false,
      },
      children: [],
    },
    {
      path: 'design',
      name: 'design',
      meta: {
        title: '知识图谱',
        ignoreKeepAlive: false,
      },
      redirect: '/nrm/res-manage/design/scene-design',
      children: [
        {
          path: 'scene-design',
          name: 'scene-design-list',
          component: () => import('@/views/nrm/res-app/graph/design/scene-design/index.vue'),
          meta: {
            title: '场景列表',
            ignoreKeepAlive: false,
            hideMenu: true,
          },
          children: [],
        },
        {
          path: 'scene-design/:id/edit',
          name: 'scene-design',
          component: () => import('@/views/nrm/res-app/graph/design/scene-design/design.vue'),
          meta: {
            title: '场景',
            ignoreKeepAlive: false,
            hideMenu: true,
          },
        },
      ],
    },
    {
      path: 'idc-monitor',
      name: 'idc-monitor',
      component: () => import('@/views/nrm/res-app/idc-monitor/index.vue'),
      meta: {
        title: 'IDC集合监控运营',
        ignoreKeepAlive: false,
      },
      children: [],
    },
    {
      path: 'database-monitor',
      name: 'database-monitor',
      component: () => import('@/views/nrm/res-manage/database-monitor/index.vue'),
      meta: {
        title: '数据库监控',
        ignoreKeepAlive: false,
      },
      children: [],
    },
  ],
};

export default develop;
