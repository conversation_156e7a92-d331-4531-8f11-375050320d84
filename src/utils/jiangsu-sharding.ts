/**
 * 江苏省十三地市分库查询工具
 * 用于双路由运营数据分析的分库分表查询
 */

// 江苏十三地市配置
export interface JiangsuCity {
  code: string;
  name: string;
  shardingCode: string;
  region?: string; // 区域分组：苏南、苏中、苏北
}

export const JIANGSU_CITIES: JiangsuCity[] = [
  // 苏南地区
  { code: 'nj', name: '南京市', shardingCode: 'ds_bc_o3_nj', region: '苏南' },
  { code: 'wx', name: '无锡市', shardingCode: 'ds_bc_o3_wx', region: '苏南' },
  { code: 'cz', name: '常州市', shardingCode: 'ds_bc_o3_cz', region: '苏南' },
  { code: 'sz', name: '苏州市', shardingCode: 'ds_bc_o3_sz', region: '苏南' },
  { code: 'zj', name: '镇江市', shardingCode: 'ds_bc_o3_zj', region: '苏南' },
  
  // 苏中地区
  { code: 'nt', name: '南通市', shardingCode: 'ds_bc_o3_nt', region: '苏中' },
  { code: 'yz', name: '扬州市', shardingCode: 'ds_bc_o3_yz', region: '苏中' },
  { code: 'tz', name: '泰州市', shardingCode: 'ds_bc_o3_tz', region: '苏中' },
  
  // 苏北地区
  { code: 'xz', name: '徐州市', shardingCode: 'ds_bc_o3_xz', region: '苏北' },
  { code: 'lyg', name: '连云港市', shardingCode: 'ds_bc_o3_lyg', region: '苏北' },
  { code: 'ha', name: '淮安市', shardingCode: 'ds_bc_o3_ha', region: '苏北' },
  { code: 'yc', name: '盐城市', shardingCode: 'ds_bc_o3_yc', region: '苏北' },
  { code: 'sq', name: '宿迁市', shardingCode: 'ds_bc_o3_sq', region: '苏北' },
];

// 地市代码到名称的映射
export const CITY_NAME_MAP = JIANGSU_CITIES.reduce((map, city) => {
  map[city.code] = city.name;
  return map;
}, {} as Record<string, string>);

// 地市代码到分库代码的映射
export const SHARDING_CODE_MAP = JIANGSU_CITIES.reduce((map, city) => {
  map[city.code] = city.shardingCode;
  return map;
}, {} as Record<string, string>);

// 区域分组
export const REGION_GROUPS = {
  苏南: JIANGSU_CITIES.filter(city => city.region === '苏南'),
  苏中: JIANGSU_CITIES.filter(city => city.region === '苏中'),
  苏北: JIANGSU_CITIES.filter(city => city.region === '苏北'),
};

/**
 * 获取地市信息
 * @param cityCode 地市代码
 * @returns 地市信息
 */
export function getCityInfo(cityCode: string): JiangsuCity | undefined {
  return JIANGSU_CITIES.find(city => city.code === cityCode);
}

/**
 * 获取分库代码
 * @param cityCode 地市代码
 * @returns 分库代码
 */
export function getShardingCode(cityCode: string): string {
  return SHARDING_CODE_MAP[cityCode] || 'ds_bc_o3_default';
}

/**
 * 获取地市名称
 * @param cityCode 地市代码
 * @returns 地市名称
 */
export function getCityName(cityCode: string): string {
  return CITY_NAME_MAP[cityCode] || '未知地市';
}

/**
 * 验证地市代码是否有效
 * @param cityCode 地市代码
 * @returns 是否有效
 */
export function isValidCityCode(cityCode: string): boolean {
  return JIANGSU_CITIES.some(city => city.code === cityCode);
}

/**
 * 获取所有地市代码
 * @returns 地市代码数组
 */
export function getAllCityCodes(): string[] {
  return JIANGSU_CITIES.map(city => city.code);
}

/**
 * 获取所有分库代码
 * @returns 分库代码数组
 */
export function getAllShardingCodes(): string[] {
  return JIANGSU_CITIES.map(city => city.shardingCode);
}

/**
 * 根据区域获取地市列表
 * @param region 区域名称
 * @returns 地市列表
 */
export function getCitiesByRegion(region: '苏南' | '苏中' | '苏北'): JiangsuCity[] {
  return REGION_GROUPS[region] || [];
}

/**
 * 分库查询参数接口
 * 注意：areaCode在这里是用于前端逻辑的地市代码，
 * 实际传递给后端的是shardingCode，用于确定查询哪个分库
 */
export interface ShardingQueryParams {
  startDate?: string;
  endDate?: string;
  metricType?: string;
  pageSize?: number;
  currentPage?: number;
  objectId?: string;
  groupId?: string;
  objectType?: string;
  historyLimit?: number;
  [key: string]: any;
}

/**
 * 分库查询结果接口
 */
export interface ShardingQueryResult<T = any> {
  success: boolean;
  message: string;
  data: T;
  shardingCode: string;
  cityCode: string;
  cityName: string;
  queryTime: number;
}

/**
 * 执行单个分库查询
 * @param cityCode 地市代码
 * @param queryParams 查询参数
 * @param apiFunction API函数
 * @returns 查询结果
 */
export async function executeShardingQuery<T>(
  cityCode: string,
  queryParams: ShardingQueryParams,
  apiFunction: (shardingCode: string, params: ShardingQueryParams) => Promise<T>
): Promise<ShardingQueryResult<T>> {
  const startTime = Date.now();
  const cityInfo = getCityInfo(cityCode);
  
  if (!cityInfo) {
    throw new Error(`无效的地市代码: ${cityCode}`);
  }
  
  try {
    // 注意：这里传递shardingCode给后端，用于确定查询哪个分库
    // 后端SQL层面会查询该分库下的所有数据，不需要再按areaCode筛选
    const result = await apiFunction(cityInfo.shardingCode, {
      ...queryParams,
      // 不传递areaCode，因为shardingCode已经确定了查询的分库
    });
    
    return {
      success: true,
      message: '查询成功',
      data: result,
      shardingCode: cityInfo.shardingCode,
      cityCode: cityCode,
      cityName: cityInfo.name,
      queryTime: Date.now() - startTime,
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : '查询失败',
      data: null as T,
      shardingCode: cityInfo.shardingCode,
      cityCode: cityCode,
      cityName: cityInfo.name,
      queryTime: Date.now() - startTime,
    };
  }
}

/**
 * 执行多个分库并行查询
 * @param cityCodes 地市代码数组，为空则查询所有地市
 * @param queryParams 查询参数
 * @param apiFunction API函数
 * @returns 查询结果数组
 */
export async function executeParallelShardingQuery<T>(
  cityCodes: string[] = [],
  queryParams: ShardingQueryParams,
  apiFunction: (shardingCode: string, params: ShardingQueryParams) => Promise<T>
): Promise<ShardingQueryResult<T>[]> {
  const targetCities = cityCodes.length > 0 ? cityCodes : getAllCityCodes();
  
  const queryPromises = targetCities.map(cityCode =>
    executeShardingQuery(cityCode, queryParams, apiFunction)
  );
  
  return Promise.all(queryPromises);
}

/**
 * 聚合多个分库查询结果
 * @param results 查询结果数组
 * @param aggregateFunction 聚合函数
 * @returns 聚合结果
 */
export function aggregateShardingResults<T, R>(
  results: ShardingQueryResult<T>[],
  aggregateFunction: (data: T[]) => R
): {
  success: boolean;
  data: R;
  totalQueryTime: number;
  successCount: number;
  failureCount: number;
  details: ShardingQueryResult<T>[];
} {
  const successResults = results.filter(result => result.success);
  const failureResults = results.filter(result => !result.success);
  
  const aggregatedData = aggregateFunction(successResults.map(result => result.data));
  
  return {
    success: successResults.length > 0,
    data: aggregatedData,
    totalQueryTime: results.reduce((sum, result) => sum + result.queryTime, 0),
    successCount: successResults.length,
    failureCount: failureResults.length,
    details: results,
  };
}

/**
 * 生成分库查询日志
 * @param results 查询结果
 * @returns 日志信息
 */
export function generateShardingQueryLog(results: ShardingQueryResult<any>[]): string {
  const logs = results.map(result => {
    const status = result.success ? '✓' : '✗';
    return `${status} ${result.cityName}(${result.shardingCode}): ${result.queryTime}ms`;
  });
  
  const totalTime = results.reduce((sum, result) => sum + result.queryTime, 0);
  const successCount = results.filter(result => result.success).length;
  
  return [
    `分库查询完成: ${successCount}/${results.length} 成功, 总耗时: ${totalTime}ms`,
    ...logs
  ].join('\n');
}
