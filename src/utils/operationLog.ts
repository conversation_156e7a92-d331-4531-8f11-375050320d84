/**
 * 双路由管理操作日志记录工具
 * <AUTHOR>
 * @date 2024-12-26
 */

import { useUserStore } from '@/store/modules/user';
import { defHttp } from '@/utils/http/axios';
import { useInfo } from '@/hooks/web/useRestAPI';

// 操作类型枚举
export enum OperationType {
  VIEW = 'view',                    // 查看详情
  CREATE = 'create',                // 新增
  UPDATE = 'update',                // 修改
  DELETE = 'delete',                // 删除
  RISK_DETECT = 'risk_detect',      // 风险检测
  EXPORT = 'export',                // 导出
  IMPORT = 'import',                // 导入
  IGNORE_RISK = 'ignore_risk',      // 忽略风险
  RESTORE_RISK = 'restore_risk',    // 恢复风险
  QUERY = 'query',                  // 查询
  STATISTICS = 'statistics'         // 统计分析
}

// 对象类型枚举
export enum ObjectType {
  OPT_GROUP = 'opt_group',          // 光路组
  OPT_ELEMENT = 'opt_element',      // 光路元素
  RISK_CHECK = 'risk_check',        // 风险检测记录
  BATCH_OPERATION = 'batch_operation' // 批量操作
}

// 日志记录参数接口
export interface OperationLogParams {
  operationType: OperationType;
  objectType: ObjectType;
  objectId?: number;
  objectCode?: string;
  objectName?: string;
  operationDescription?: string;
  beforeData?: any;
  afterData?: any;
  areaCode?: string;
  areaName?: string;
  speciality?: string;
  operatorId?: string;
  operatorName?: string;
  requestParams?: any;
  remark?: string;
}

// 简化的日志记录参数
export interface SimpleLogParams {
  operationType: OperationType;
  objectType: ObjectType;
  objectId?: number;
  operationDescription: string;
  areaCode?: string;
  objectCode?: string;
  objectName?: string;
}



/**
 * 双路由管理操作日志记录类
 */
export class DualRouteOperationLogger {
  private userStore = useUserStore();
  private logQueue: any[] = []; // 日志队列
  private isProcessing = false; // 是否正在处理队列
  private maxRetries = 3; // 最大重试次数
  private retryDelay = 1000; // 重试延迟(ms)


  /**
   * 记录完整的操作日志
   */
  async recordOperationLog(params: OperationLogParams): Promise<void> {
    const logData = {
      operation_type: params.operationType,
      object_type: params.objectType,
      object_id: params.objectId,
      object_code: params.objectCode,
      object_name: params.objectName,
      operation_description: params.operationDescription,
      before_data: params.beforeData,
      after_data: params.afterData,
      area_code: params.areaCode || this.userStore.getAreaCode,
      area_name: params.areaName || this.userStore.getAreaName,
      speciality: params.speciality,
      operator_id: params.operatorId || this.userStore.getUserInfo?.userId,
      operator_name: params.operatorName || this.userStore.getUserInfo?.username,
      request_params: params.requestParams,
      remark: params.remark
    };

    await this.sendLogWithRetry(logData);
  }

  /**
   * 带重试机制的日志发送
   */
  private async sendLogWithRetry(logData: any, retryCount = 0): Promise<void> {
    try {
      // 记录日志接口使用 @RequestParam，需要通过URL参数传递
      const params = new URLSearchParams({
        operationType: logData.operation_type,
        objectType: logData.object_type,
        operationDescription: logData.operation_description,
        areaCode: logData.area_code,
        operatorId: logData.operator_id,
        operatorName: logData.operator_name
      });

      if (logData.object_id) {
        params.append('objectId', logData.object_id.toString());
      }

      await defHttp.post({
        url: `/graph-rest-api/api/dual-route-log/record?${params.toString()}`,
        timeout: 5000
      }, { isTransformResponse: false });
    } catch (error) {
      console.warn(`记录操作日志失败 (尝试 ${retryCount + 1}/${this.maxRetries}):`, error);

      if (retryCount < this.maxRetries - 1) {
        // 延迟后重试
        await this.delay(this.retryDelay * (retryCount + 1));
        return this.sendLogWithRetry(logData, retryCount + 1);
      } else {
        // 重试失败，加入队列稍后处理
        this.addToQueue(logData);
      }
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 添加到队列
   */
  private addToQueue(logData: any): void {
    this.logQueue.push({
      ...logData,
      timestamp: Date.now(),
      retryCount: 0
    });

    // 限制队列大小
    if (this.logQueue.length > 100) {
      this.logQueue.shift(); // 移除最旧的日志
    }

    // 启动队列处理
    this.processQueue();
  }

  /**
   * 处理队列中的日志
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.logQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.logQueue.length > 0) {
      const logItem = this.logQueue.shift();
      if (!logItem) continue;

      try {
        await defHttp.post({
          url: '/api/dual-route-log/record',
          data: logItem,
          timeout: 5000
        });
      } catch (error) {
        console.warn('队列日志发送失败:', error);

        // 如果重试次数未达到上限，重新加入队列
        if (logItem.retryCount < this.maxRetries) {
          logItem.retryCount++;
          this.logQueue.push(logItem);
        }
      }

      // 处理间隔
      await this.delay(100);
    }

    this.isProcessing = false;
  }

  /**
   * 记录简化的操作日志
   */
  async recordSimpleLog(params: SimpleLogParams): Promise<void> {
    await this.recordOperationLog({
      operationType: params.operationType,
      objectType: params.objectType,
      objectId: params.objectId,
      objectCode: params.objectCode,
      objectName: params.objectName,
      operationDescription: params.operationDescription,
      areaCode: params.areaCode
    });
  }







  /**
   * 查询操作日志
   */
  async queryOperationLog(queryParams: any): Promise<any> {
    try {
      console.log('🔧 [dualRouteLogger] 开始查询操作日志');
      console.log('🔧 [dualRouteLogger] 原始查询参数:', queryParams);

      const shardingCode = this.getShardingCodeByAreaCode(queryParams.area_code);
      const pageSize = queryParams.pageSize || 10;
      const currentPage = queryParams.currentPage || 1;

      console.log('🔧 [dualRouteLogger] 处理后的参数:', {
        shardingCode,
        pageSize,
        currentPage,
        area_code: queryParams.area_code
      });

      // 分页和分库参数作为URL参数，其他查询条件在请求体中
      const urlParams = new URLSearchParams({
        pageSize: pageSize.toString(),
        currentPage: currentPage.toString(),
        shardingCode: shardingCode
      });

      const fullUrl = `/api/dual-route-log/query?${urlParams.toString()}`;
      console.log('🔧 [dualRouteLogger] 请求URL:', fullUrl);
      console.log('🔧 [dualRouteLogger] 请求体数据:', queryParams);

      // 使用 useInfo hook 和 doCreateNew 方式调用
      const queryService = useInfo({ rootPath: '/graph-rest-api' });
      queryService.info.value = queryParams;
      const result = await queryService.doCreateNew(fullUrl);

      console.log('🔧 [dualRouteLogger] API响应:', result);
      console.log('🔧 [dualRouteLogger] 响应类型:', typeof result);
      console.log('🔧 [dualRouteLogger] 响应结构:', {
        success: result?.success,
        dataLength: result?.data?.length,
        pageInfo: result?.pageInfo,
        message: result?.message
      });

      return result;
    } catch (error) {
      console.error('❌ [dualRouteLogger] 查询操作日志失败:', error);
      console.error('❌ [dualRouteLogger] 错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        response: error.response
      });
      return { success: false, data: [], pageInfo: { totalCount: 0 }, message: error.message };
    }
  }

  /**
   * 查询操作日志统计信息
   */
  async queryOperationLogStatistics(queryParams: any): Promise<any[]> {
    try {
      console.log('🔧 [dualRouteLogger] 开始查询操作日志统计');
      console.log('🔧 [dualRouteLogger] 统计查询参数:', queryParams);

      const shardingCode = this.getShardingCodeByAreaCode(queryParams.area_code);

      // shardingCode作为URL参数，其他查询条件在请求体中
      const urlParams = new URLSearchParams({
        shardingCode: shardingCode
      });

      const fullUrl = `/api/dual-route-log/statistics?${urlParams.toString()}`;
      console.log('🔧 [dualRouteLogger] 统计处理后的参数:', {
        shardingCode,
        area_code: queryParams.area_code
      });

      console.log('🔧 [dualRouteLogger] 统计请求URL:', fullUrl);
      console.log('🔧 [dualRouteLogger] 统计请求体数据:', queryParams);

      // 使用 useInfo hook 和 doCreateNew 方式调用
      const statisticsService = useInfo({ rootPath: '/graph-rest-api' });
      statisticsService.info.value = queryParams;
      const result = await statisticsService.doCreateNew(fullUrl);

      console.log('🔧 [dualRouteLogger] 统计API响应:', result);
      console.log('🔧 [dualRouteLogger] 统计响应类型:', typeof result);
      console.log('🔧 [dualRouteLogger] 统计响应结构:', {
        success: result?.success,
        dataLength: result?.data?.length,
        isArray: Array.isArray(result),
        message: result?.message
      });

      // 如果响应是成功的，返回数据数组
      if (result?.success && Array.isArray(result.data)) {
        console.log('✅ [dualRouteLogger] 统计数据解析成功，数据条数:', result.data.length);
        return result.data;
      } else if (Array.isArray(result)) {
        console.log('✅ [dualRouteLogger] 直接返回数组数据，数据条数:', result.length);
        return result;
      } else {
        console.warn('⚠️ [dualRouteLogger] 统计数据格式不正确:', result);
        return [];
      }
    } catch (error) {
      console.error('❌ [dualRouteLogger] 查询操作日志统计失败:', error);
      console.error('❌ [dualRouteLogger] 统计错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        response: error.response
      });
      return [];
    }
  }

  /**
   * 查询操作日志详情
   */
  async queryOperationLogDetail(logId: number, areaCode?: string): Promise<any> {
    try {
      console.log('🔍 [dualRouteLogger] 开始查询日志详情');
      console.log('🔍 [dualRouteLogger] 输入参数:', {
        logId: logId,
        areaCode: areaCode,
        userAreaCode: this.userStore.getAreaCode
      });

      const finalAreaCode = areaCode || this.userStore.getAreaCode;
      const shardingCode = this.getShardingCodeByAreaCode(finalAreaCode);

      console.log('🔍 [dualRouteLogger] 分库编码映射:', {
        finalAreaCode: finalAreaCode,
        shardingCode: shardingCode
      });

      // 构建请求参数，统一使用doCreateNew方式
      const requestParams = {
        logId: logId,
        shardingCode: shardingCode
      };

      console.log('🔍 [dualRouteLogger] 详情请求参数:', requestParams);

      // 使用统一的doCreateNew方式调用
      const detailService = useInfo({ rootPath: '/graph-rest-api' });
      detailService.info.value = requestParams;
      const result = await detailService.doCreateNew('/api/dual-route-log/detail');

      console.log('🔍 [dualRouteLogger] 详情查询响应:', result);
      console.log('🔍 [dualRouteLogger] 响应数据类型:', typeof result);
      console.log('🔍 [dualRouteLogger] 响应是否为空:', !result || Object.keys(result).length === 0);

      // 处理标准响应格式
      if (result && typeof result === 'object') {
        console.log('🔍 [dualRouteLogger] 响应结构分析:', {
          success: result.success,
          message: result.message,
          hasData: !!result.data,
          dataType: result.data ? typeof result.data : 'null'
        });

        if (result.success === true && result.data) {
          console.log('✅ [dualRouteLogger] 详情查询成功，返回数据:', result.data);
          return result.data;
        } else {
          console.warn('⚠️ [dualRouteLogger] 详情查询失败:', result.message);
          return {};
        }
      } else {
        console.warn('⚠️ [dualRouteLogger] 响应格式不正确:', result);
        return {};
      }
    } catch (error) {
      console.error('❌ [dualRouteLogger] 查询操作日志详情失败:', error);
      console.error('❌ [dualRouteLogger] 错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      return {};
    }
  }

  /**
   * 批量记录操作日志
   */
  async batchRecordOperationLog(logList: OperationLogParams[]): Promise<void> {
    if (!logList || logList.length === 0) {
      return;
    }

    const batchData = logList.map(params => ({
      operation_type: params.operationType,
      object_type: params.objectType,
      object_id: params.objectId,
      object_code: params.objectCode,
      object_name: params.objectName,
      operation_description: params.operationDescription,
      before_data: params.beforeData,
      after_data: params.afterData,
      area_code: params.areaCode || this.userStore.getAreaCode,
      area_name: params.areaName || this.userStore.getAreaName,
      speciality: params.speciality,
      operator_id: params.operatorId || this.userStore.getUserInfo?.userId,
      operator_name: params.operatorName || this.userStore.getUserInfo?.username,
      request_params: params.requestParams,
      remark: params.remark
    }));

    try {
      await defHttp.post({
        url: '/api/dual-route-log/batch-record',
        data: { list: batchData },
        timeout: 10000 // 批量操作延长超时时间
      });
    } catch (error) {
      console.warn('批量记录操作日志失败:', error);
      // 批量失败时，尝试逐个记录
      for (const logData of batchData) {
        this.addToQueue(logData);
      }
      this.processQueue();
    }
  }

  /**
   * 记录批量操作日志
   */
  async recordBatchOperationLog(
    operationType: OperationType,
    objectType: ObjectType,
    objectIds: number[],
    operationDescription: string,
    areaCode?: string
  ): Promise<void> {
    const batchLogData = {
      operation_type: operationType,
      object_type: 'batch_operation',
      object_id: null,
      object_code: null,
      object_name: `批量${this.getOperationTypeName(operationType)}`,
      operation_description: `${operationDescription}，影响对象数量：${objectIds.length}`,
      before_data: null,
      after_data: { affected_object_ids: objectIds, affected_count: objectIds.length },
      area_code: areaCode || this.userStore.getAreaCode,
      area_name: this.userStore.getAreaName,
      speciality: null,
      operator_id: this.userStore.getUserInfo?.userId,
      operator_name: this.userStore.getUserInfo?.username,
      request_params: { object_type: objectType, object_ids: objectIds },
      remark: `批量操作：${objectType}`
    };

    await this.sendLogWithRetry(batchLogData);
  }

  /**
   * 获取操作类型中文名称
   */
  private getOperationTypeName(operationType: OperationType): string {
    const nameMap = {
      [OperationType.VIEW]: '查看',
      [OperationType.CREATE]: '新增',
      [OperationType.UPDATE]: '修改',
      [OperationType.DELETE]: '删除',
      [OperationType.RISK_DETECT]: '风险检测',
      [OperationType.EXPORT]: '导出',
      [OperationType.IMPORT]: '导入',
      [OperationType.IGNORE_RISK]: '忽略风险',
      [OperationType.RESTORE_RISK]: '恢复风险',
      [OperationType.QUERY]: '查询',
      [OperationType.STATISTICS]: '统计分析'
    };
    return nameMap[operationType] || operationType;
  }



  /**
   * 获取队列状态
   */
  getQueueStatus(): { queueLength: number; isProcessing: boolean } {
    return {
      queueLength: this.logQueue.length,
      isProcessing: this.isProcessing
    };
  }

  /**
   * 根据地市编码获取分库编码
   */
  private getShardingCodeByAreaCode(areaCode?: string): string {
    const code = areaCode || this.userStore.getAreaCode;

    console.log('🔧 [dualRouteLogger] 获取分库编码');
    console.log('🔧 [dualRouteLogger] 输入地市编码:', areaCode);
    console.log('🔧 [dualRouteLogger] 用户地市编码:', this.userStore.getAreaCode);
    console.log('🔧 [dualRouteLogger] 最终使用编码:', code);

    // 地市编码到分库编码的映射
    const areaCodeMap: Record<string, string> = {
      'wx': 'ds_bc_o3_wx',    // 无锡
      'nj': 'ds_bc_o3_nj',    // 南京
      'sz': 'ds_bc_o3_sz',    // 苏州
      'xz': 'ds_bc_o3_xz',    // 徐州
      'cz': 'ds_bc_o3_cz',    // 常州
      'nt': 'ds_bc_o3_nt',    // 南通
      'lyg': 'ds_bc_o3_lyg',  // 连云港
      'ha': 'ds_bc_o3_ha',    // 淮安
      'yc': 'ds_bc_o3_yc',    // 盐城
      'yz': 'ds_bc_o3_yz',    // 扬州
      'zj': 'ds_bc_o3_zj',    // 镇江
      'tz': 'ds_bc_o3_tz',    // 泰州
      'sq': 'ds_bc_o3_sq'     // 宿迁
    };

    const shardingCode = areaCodeMap[code] || 'ds_bc_o3_wx'; // 默认无锡

    console.log('🔧 [dualRouteLogger] 映射结果:', {
      inputCode: code,
      shardingCode: shardingCode,
      isDefault: !areaCodeMap[code]
    });

    return shardingCode;
  }
}

// 创建全局实例
export const dualRouteLogger = new DualRouteOperationLogger();

// 便捷方法
export const recordSimpleLog = (params: SimpleLogParams) => dualRouteLogger.recordSimpleLog(params);

// 批量操作便捷方法
export const recordBatchOperationLog = (
  operationType: OperationType,
  objectType: ObjectType,
  objectIds: number[],
  operationDescription: string,
  areaCode?: string
) => dualRouteLogger.recordBatchOperationLog(operationType, objectType, objectIds, operationDescription, areaCode);

export const batchRecordOperationLog = (logList: OperationLogParams[]) => dualRouteLogger.batchRecordOperationLog(logList);

// 队列状态查询
export const getLogQueueStatus = () => dualRouteLogger.getQueueStatus();

export default dualRouteLogger;
