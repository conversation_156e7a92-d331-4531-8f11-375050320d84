<template>
  <div class="resource-warning">
    <h1></h1>
    <div class="warning-cards">
      <div 
        class="warning-card"
        @click="goToDetail('fiber-cable-health')"
      >
        <h2>光缆占用健康度预警</h2>
      </div>
      
      <div 
        class="warning-card"
        @click="goToDetail('external-force-point')"
      >
        <h2>光缆外力点隐患预警</h2>
      </div>
      
      <div 
        class="warning-card"
        @click="goToDetail('splitter-port')"
      >
        <h2>分光器端口预警</h2>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const goToDetail = (type) => {
  router.push({
    path: `/nrm/res-app/test-fun/resource-warning/${type}`
  });
};
</script>

<style scoped>
.resource-warning {
  padding: 24px;
}

.warning-cards {
  display: flex;
  gap: 24px;
  margin-top: 24px;
}

.warning-card {
  flex: 1;
  min-width: 200px;
  background: #f8f8f8;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.warning-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
