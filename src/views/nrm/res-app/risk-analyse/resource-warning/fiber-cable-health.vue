<template>
  <div class="warning-detail">
    <h1>光缆占用健康度预警</h1>
    <!-- 这里可以添加具体的预警内容 -->
    <div class="back-btn" @click="goBack">
      返回
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const goBack = () => {
  router.back();
};
</script>

<style scoped>
.warning-detail {
  padding: 24px;
}

.back-btn {
  margin-top: 20px;
  padding: 8px 16px;
  background: #f0f0f0;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
}

.back-btn:hover {
  background: #e0e0e0;
}
</style>