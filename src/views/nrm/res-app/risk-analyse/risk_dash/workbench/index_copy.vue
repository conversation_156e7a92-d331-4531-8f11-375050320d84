<template>
  <div class="home-container">
    <!-- 主内容区域 -->
    <main class="main-content">
      <div class="content-wrapper">
        <div class="welcome-section">
          <div class="title-container">
            <h1>双路由隐患整治系统</h1>
            <div class="title-decoration"></div>
          </div>
          <p>智能化双路由隐患检测与整治管理平台</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-layout">
          <!-- 左侧辅助功能区域 -->
          <div class="sidebar-functions">
            <!-- 保护组相关功能 -->
            <div class="function-group">
              <h3 class="group-title">
                <svg viewBox="0 0 24 24" class="group-icon" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path>
                </svg>
                保护组管理
              </h3>
              <div class="function-list">
                <a v-for="item in protectionGroupFunctions" :key="item.title"
                   href="javascript:void(0)"
                   class="function-item"
                   @click="handleClick(item)">
                  <span class="function-name">{{ item.title }}</span>
                  <svg viewBox="0 0 24 24" class="function-arrow" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M5 12h14"></path>
                    <path d="M12 5l7 7-7 7"></path>
                  </svg>
                </a>
              </div>
            </div>

            <!-- 系统流程功能 -->
            <div class="function-group">
              <h3 class="group-title">
                <svg viewBox="0 0 24 24" class="group-icon" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M18 21H6a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2z"></path>
                  <path d="M8 10h8"></path>
                  <path d="M8 14h4"></path>
                </svg>
                系统流程
              </h3>
              <div class="function-list">
                <a v-for="item in processModules" :key="item.title"
                   href="javascript:void(0)"
                   class="function-item"
                   @click="handleClick(item)">
                  <span class="function-name">{{ item.title }}</span>
                  <svg viewBox="0 0 24 24" class="function-arrow" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M5 12h14"></path>
                    <path d="M12 5l7 7-7 7"></path>
                  </svg>
                </a>
              </div>
            </div>

            <!-- 资源视图功能 -->
            <div class="function-group">
              <h3 class="group-title">
                <svg viewBox="0 0 24 24" class="group-icon" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 2v6l3 3 3-3V2"></path>
                  <path d="M5 10H2a10 10 0 0 0 10 10c5.5 0 10-4.5 10-10h-3"></path>
                </svg>
                资源视图
              </h3>
              <div class="function-list">
                <a v-for="item in networkModules" :key="item.title"
                   href="javascript:void(0)"
                   class="function-item"
                   @click="handleClick(item)">
                  <span class="function-name">{{ item.title }}</span>
                  <svg viewBox="0 0 24 24" class="function-arrow" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M5 12h14"></path>
                    <path d="M12 5l7 7-7 7"></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>

          <!-- 右侧核心功能区域 -->
          <div class="main-functions">
            <!-- 核心功能：光路保护组管理 -->
            <div class="hero-card" @click="handleClick(coreFunction)">
              <div class="hero-header">
                <div class="hero-icon">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path>
                  </svg>
                </div>
                <div class="hero-content">
                  <h2>光路保护组管理</h2>
                  <p>双路由光路保护组的核心管理功能，支持一键检测、智能分析和风险评估</p>
                </div>
                <div class="hero-arrow">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M5 12h14"></path>
                    <path d="M12 5l7 7-7 7"></path>
                  </svg>
                </div>
              </div>
              <div class="hero-stats">
                <div class="stat-item">
                  <div class="stat-number">{{ stats.totalGroups }}</div>
                  <div class="stat-label">保护组总数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.riskGroups }}</div>
                  <div class="stat-label">风险保护组</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.normalGroups }}</div>
                  <div class="stat-label">正常保护组</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.lastCheckTime }}</div>
                  <div class="stat-label">最近检测</div>
                </div>
              </div>
            </div>

            <!-- 重要功能区域 -->
            <div class="important-functions">
              <div class="important-card" @click="handleClick(importantFunctions[0])">
                <div class="card-header">
                  <div class="card-icon hazard-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                      <polyline points="14 2 14 8 20 8"></polyline>
                      <line x1="16" y1="13" x2="8" y2="13"></line>
                      <line x1="16" y1="17" x2="8" y2="17"></line>
                    </svg>
                  </div>
                  <div class="card-content">
                    <h3>隐患整改管理</h3>
                    <p>双路由隐患整改工作流程管理，从发现到解决的全流程跟踪</p>
                  </div>
                </div>
                <div class="card-stats">
                  <div class="mini-stat">
                    <span class="mini-number">{{ hazardStats.pending }}</span>
                    <span class="mini-label">待整改</span>
                  </div>
                  <div class="mini-stat">
                    <span class="mini-number">{{ hazardStats.processing }}</span>
                    <span class="mini-label">整改中</span>
                  </div>
                </div>
              </div>

              <div class="important-card" @click="handleClick(importantFunctions[1])">
                <div class="card-header">
                  <div class="card-icon analytics-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                    </svg>
                  </div>
                  <div class="card-content">
                    <h3>双路由运营数据分析</h3>
                    <p>双路由光路检测运营统计与趋势分析，支持多维度数据展示</p>
                  </div>
                </div>
                <div class="card-stats">
                  <div class="mini-stat">
                    <span class="mini-number">{{ analyticsStats.totalChecks }}</span>
                    <span class="mini-label">总检测次数</span>
                  </div>
                  <div class="mini-stat">
                    <span class="mini-number">{{ analyticsStats.successRate }}%</span>
                    <span class="mini-label">检测成功率</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 页脚空间 -->
        <div class="footer-space"></div>
      </div>
    </main>

    <!-- 背景装饰元素 -->
    <div class="tech-grid"></div>
    <div class="tech-circles"></div>
  </div>
</template>

<script setup>
import { deviceHazardModules as deviceHazardData, businessHazardModules as businessHazardData, groupItems1, groupItems2 } from '@/views/nrm/res-app/risk-analyse/risk_dash/workbench/components/data.ts';
import { usePermission } from '@/hooks/web/usePermission';
import { useGo } from '@/hooks/web/usePage';
import { notification } from 'ant-design-vue';
import { CloseCircleOutlined } from '@ant-design/icons-vue';
import { h, defineOptions, ref, onMounted } from 'vue';

defineOptions({
  name: 'RiskManageWorkbench'
});

const go = useGo();
const { hasPermission } = usePermission();

// 统计数据
const stats = ref({
  totalGroups: '1,247',
  riskGroups: '23',
  normalGroups: '1,224',
  lastCheckTime: '2小时前'
});

const hazardStats = ref({
  pending: '8',
  processing: '15'
});

const analyticsStats = ref({
  totalChecks: '3,456',
  successRate: '98.5'
});

// 核心功能
const coreFunction = {
  title: '光路保护组管理',
  path: '/nrm/res-app/risk-manage/opt_road_group_management',
  roles: ['隐患排查管理角色', '系统管理角色', 'admin']
};

// 重要功能
const importantFunctions = [
  {
    title: '隐患整改管理',
    path: '/nrm/res-app/risk-manage/hazard-remediation-list',
    roles: ['隐患排查管理角色', '系统管理角色', 'admin']
  },
  {
    title: '双路由运营数据分析',
    path: '/nrm/res-app/risk-manage/dual_route_operations_analytics',
    roles: ['隐患排查管理角色', '系统管理角色', 'admin']
  }
];

// 保护组相关功能
const protectionGroupFunctions = [
  {
    title: '设备保护组管理',
    path: '/nrm/dualrouting/protection-scenarios',
    roles: ['隐患排查管理角色', '系统管理角色', 'admin']
  },
  {
    title: '电路保护组管理',
    path: '/nrm/res-app/risk-manage/circuit_pair_management',
    roles: ['隐患排查管理角色', '系统管理角色', 'admin']
  },
  {
    title: '生命线业务隐患分析',
    path: '/nrm/res-app/risk-manage/life_circuit_risk_analyze',
    roles: ['电路保护组组管理', 'admin']
  },
  {
    title: '生命线管理',
    path: '/nrm/res-app/risk-manage/lifeline-management',
    roles: ['隐患排查管理角色', '系统管理角色', 'admin']
  },
  {
    title: '历史版本清单',
    path: '/nrm/dualrouting/history-version-list',
    roles: ['隐患排查管理角色', '系统管理角色', 'admin']
  }
];

// 处理点击事件
const handleClick = (item) => {
  console.log('item.roles角色权限', item.roles);

  if (hasPermission(item.roles)) {
    go(item.path);
  } else {
    // 显示权限不足的通知
    notification.open({
      message: '权限不足',
      description: `您没有访问「${item.title}」模块的权限，请联系管理员获取权限。`,
      duration: 4,
      icon: () => h(CloseCircleOutlined, { style: 'color: red' }),
    });
  }
};

// 模拟数据加载
onMounted(() => {
  // 这里可以添加实际的数据加载逻辑
  console.log('工作台页面已加载');
});

// 转换数据格式 - 系统流程模块
const processModules = groupItems1;

// 转换数据格式 - 资源视图模块
const networkModules = groupItems2;
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8faff 0%, #e6f1ff 100%);
  background-attachment: fixed;
  position: relative;
  color: #1e3a8a;
  overflow-x: hidden;
}

/* 确保背景覆盖整个页面高度 */
.home-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    /* 纹理层 */
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    /* 噪点纹理 */
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.02) 2px,
      rgba(255, 255, 255, 0.02) 4px
    ),
    /* 主背景 */
    linear-gradient(135deg, #f8faff 0%, #e6f1ff 100%);
  z-index: -2;
}

/* 技术风格网格背景 */
.tech-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    /* 细网格 */
    linear-gradient(rgba(30, 58, 138, 0.06) 1px, transparent 1px),
    linear-gradient(90deg, rgba(30, 58, 138, 0.06) 1px, transparent 1px),
    /* 粗网格 */
    linear-gradient(rgba(30, 58, 138, 0.12) 1px, transparent 1px),
    linear-gradient(90deg, rgba(30, 58, 138, 0.12) 1px, transparent 1px),
    /* 点状纹理 */
    radial-gradient(circle, rgba(30, 58, 138, 0.08) 1px, transparent 1px);
  background-size:
    20px 20px,
    20px 20px,
    80px 80px,
    80px 80px,
    10px 10px;
  background-position:
    0 0,
    0 0,
    0 0,
    0 0,
    5px 5px;
  z-index: -1;
  pointer-events: none;
  animation: gridFloat 30s ease-in-out infinite;
}

/* 技术风格圆形装饰 */
.tech-circles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  pointer-events: none;
}

.tech-circles::before {
  content: "";
  position: absolute;
  width: 600px;
  height: 600px;
  border-radius: 50%;
  background:
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 70% 70%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle, rgba(59, 130, 246, 0.06) 0%, rgba(59, 130, 246, 0) 70%);
  top: -200px;
  right: -100px;
  filter: blur(20px);
  animation: float 25s ease-in-out infinite;
}

.tech-circles::after {
  content: "";
  position: absolute;
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background:
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.08) 0%, transparent 25%),
    radial-gradient(circle at 60% 40%, rgba(255, 215, 0, 0.06) 0%, transparent 40%),
    radial-gradient(circle, rgba(255, 215, 0, 0.08) 0%, rgba(255, 215, 0, 0) 70%);
  bottom: -100px;
  left: -100px;
  filter: blur(20px);
  animation: float 20s ease-in-out infinite reverse;
}

.main-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  width: 100%;

}

/* 隐藏滚动条（可选） */
  .content-wrapper::-webkit-scrollbar {
  display: none;
}

.content-wrapper {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 4rem 2rem;
  flex: 1;
}

.welcome-section {
  text-align: center;
  margin-bottom: 4rem;
}

.title-container {
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.welcome-section h1 {
  font-size: 2.75rem;
  font-weight: 800;
  margin-bottom: 0;
  position: relative;
  z-index: 1;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 60%, #ffd700 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(30, 58, 138, 0.2);
}

.title-decoration {
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #ffd700 100%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.welcome-section p {
  font-size: 1.25rem;
  color: #4b5563;
  margin-top: 1rem;
  font-weight: 500;
}

/* 主布局 */
.main-layout {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

/* 左侧辅助功能区域 */
.sidebar-functions {
  width: 320px;
  flex-shrink: 0;
}

.function-group {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 24px rgba(30, 58, 138, 0.08);
  border: 1px solid rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(20px);
}

.group-title {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid rgba(59, 130, 246, 0.1);
}

.group-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
  color: #3b82f6;
}

.function-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.function-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 0.5rem;
  text-decoration: none;
  color: #374151;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.function-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  color: #1e3a8a;
  transform: translateX(4px);
}

.function-name {
  flex: 1;
}

.function-arrow {
  width: 1rem;
  height: 1rem;
  opacity: 0;
  transition: all 0.3s ease;
}

.function-item:hover .function-arrow {
  opacity: 1;
  color: #3b82f6;
}

/* 右侧主功能区域 */
.main-functions {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 核心功能卡片 */
.hero-card {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(30, 58, 138, 0.12), 0 8px 24px rgba(59, 130, 246, 0.08);
  border: 2px solid rgba(59, 130, 246, 0.2);
  cursor: pointer;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.hero-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #ffd700 100%);
}

.hero-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 32px 64px rgba(30, 58, 138, 0.2), 0 16px 32px rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.4);
}

.hero-header {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  margin-bottom: 2rem;
}

.hero-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
  flex-shrink: 0;
}

.hero-icon svg {
  width: 2rem;
  height: 2rem;
}

.hero-content {
  flex: 1;
}

.hero-content h2 {
  font-size: 1.75rem;
  font-weight: 800;
  color: #1e3a8a;
  margin-bottom: 0.75rem;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-content p {
  font-size: 1.1rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.hero-arrow {
  color: #3b82f6;
  transition: all 0.3s ease;
}

.hero-card:hover .hero-arrow {
  color: #ffd700;
  transform: translateX(8px);
}

.hero-arrow svg {
  width: 1.5rem;
  height: 1.5rem;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 0.75rem;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1e3a8a;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* 重要功能区域 */
.important-functions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.important-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 12px 32px rgba(30, 58, 138, 0.1);
  border: 1px solid rgba(30, 58, 138, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.important-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #f39c12 0%, #e74c3c 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.important-card:first-child::before {
  background: linear-gradient(90deg, #f39c12 0%, #e67e22 100%);
}

.important-card:last-child::before {
  background: linear-gradient(90deg, #8b5cf6 0%, #7c3aed 100%);
}

.important-card:hover::before {
  opacity: 1;
}

.important-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(30, 58, 138, 0.15), 0 8px 24px rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.card-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.hazard-icon {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.analytics-icon {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.card-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.card-content h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 0.75rem;
}

.card-content p {
  font-size: 0.95rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

.card-stats {
  display: flex;
  gap: 1rem;
}

.mini-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 0.5rem;
  flex: 1;
}

.mini-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 0.25rem;
}

.mini-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* 添加底部空间 */
.footer-space {
  height: 6rem;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .main-layout {
    flex-direction: column;
    gap: 2rem;
  }

  .sidebar-functions {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .function-group {
    margin-bottom: 0;
  }

  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .content-wrapper {
    padding: 3rem 1.5rem;
  }

  .main-layout {
    gap: 1.5rem;
  }

  .sidebar-functions {
    grid-template-columns: 1fr;
  }

  .important-functions {
    grid-template-columns: 1fr;
  }

  .hero-card {
    padding: 2rem;
  }

  .hero-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .hero-content h2 {
    font-size: 1.5rem;
  }

  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .welcome-section h1 {
    font-size: 2.25rem;
  }

  .welcome-section p {
    font-size: 1.125rem;
  }
}

@media (max-width: 640px) {
  .content-wrapper {
    padding: 2rem 1rem;
  }

  .welcome-section {
    margin-bottom: 3rem;
  }

  .welcome-section h1 {
    font-size: 1.75rem;
  }

  .hero-card {
    padding: 1.5rem;
  }

  .hero-header {
    gap: 1rem;
  }

  .hero-icon {
    width: 3rem;
    height: 3rem;
  }

  .hero-icon svg {
    width: 1.5rem;
    height: 1.5rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .important-card {
    padding: 1.5rem;
  }

  .card-header {
    gap: 1rem;
  }

  .card-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .function-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }
}

/* 确保页面背景在所有情况下都能覆盖 */
.home-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 100%;
  background: linear-gradient(135deg, #f8faff 0%, #e6f1ff 100%);
  z-index: -3;
  pointer-events: none;
}

/* 为超长内容提供额外的背景保护 */
body {
  background:
    /* 纹理层 */
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
    /* 主背景 */
    linear-gradient(135deg, #f8faff 0%, #e6f1ff 100%);
  background-attachment: fixed;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes gridFloat {
  0%, 100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(2px, -2px);
  }
  50% {
    transform: translate(-1px, 1px);
  }
  75% {
    transform: translate(1px, 2px);
  }
}

/* 额外的纹理层 */
.home-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 100%;
  background:
    /* 微妙的噪点 */
    repeating-conic-gradient(
      from 0deg at 50% 50%,
      transparent 0deg,
      rgba(255, 255, 255, 0.01) 1deg,
      transparent 2deg
    ),
    /* 织物纹理 */
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 1px,
      rgba(30, 58, 138, 0.02) 1px,
      rgba(30, 58, 138, 0.02) 2px
    ),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 1px,
      rgba(30, 58, 138, 0.02) 1px,
      rgba(30, 58, 138, 0.02) 2px
    );
  z-index: -3;
  pointer-events: none;
}
</style>