<template>
  <div class="hazard-remediation-list-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">隐患整改管理</h1>
          <p class="page-description">管理双路由隐患整改工作流程</p>
        </div>
        <div class="action-section">
          <a-button type="primary" size="large" @click="handleCreateRemediation">
            <template #icon>
              <PlusOutlined />
            </template>
            发起整改
          </a-button>
        </div>
      </div>
    </div>

    <!-- 优化的查询条件 -->
    <div class="search-section">
      <a-card title="筛选条件">
        <div class="search-form-container">
          <a-row :gutter="[24, 16]">
            <!-- 第一行：基础筛选条件 -->
            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">地市</label>
                <a-select
                  v-model:value="searchForm.cityCode"
                  placeholder="请选择地市"
                  allowClear
                  :disabled="cityUnchangable"
                  class="form-control"
                  @change="handleSearch"
                >
                  <a-select-option value="XZ">徐州</a-select-option>
                  <a-select-option value="NJ">南京</a-select-option>
                  <a-select-option value="SZ">苏州</a-select-option>
                  <a-select-option value="NT">南通</a-select-option>
                  <a-select-option value="CZ">常州</a-select-option>
                  <a-select-option value="WX">无锡</a-select-option>
                  <a-select-option value="YZ">扬州</a-select-option>
                  <a-select-option value="ZJ">镇江</a-select-option>
                  <a-select-option value="TY">泰州</a-select-option>
                  <a-select-option value="HA">淮安</a-select-option>
                  <a-select-option value="YC">盐城</a-select-option>
                  <a-select-option value="LYGD">连云港</a-select-option>
                  <a-select-option value="SQ">宿迁</a-select-option>
                </a-select>
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">隐患类型</label>
                <a-select
                  v-model:value="searchForm.hazardType"
                  placeholder="请选择隐患类型"
                  allowClear
                  class="form-control"
                  @change="handleSearch"
                >
                  <a-select-option value="noOpticalPath">无光路</a-select-option>
                  <a-select-option value="singleOpticalPath">单光路</a-select-option>
                  <a-select-option value="singleRoute">单路由</a-select-option>
                  <a-select-option value="missingPipeline">缺管道</a-select-option>
                  <a-select-option value="samePipeline">同管道</a-select-option>
                  <a-select-option value="sameOpticalCable">同光缆</a-select-option>
                  <a-select-option value="intraOfficeOpticalPath">局内光路</a-select-option>
                  <a-select-option value="sameOfficeWell">同局前井</a-select-option>
                </a-select>
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">隐患等级</label>
                <a-select
                  v-model:value="searchForm.hazardLevel"
                  placeholder="请选择隐患等级"
                  allowClear
                  class="form-control"
                  @change="handleSearch"
                >
                  <a-select-option value="high">高风险</a-select-option>
                  <a-select-option value="medium">中风险</a-select-option>
                  <a-select-option value="low">低风险</a-select-option>
                </a-select>
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">状态</label>
                <a-select
                  v-model:value="searchForm.status"
                  placeholder="请选择状态"
                  allowClear
                  class="form-control"
                  @change="handleSearch"
                >
                  <a-select-option value="draft">草稿</a-select-option>
                  <a-select-option value="inProgress">整改中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                </a-select>
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">隐患来源</label>
                <a-select
                  v-model:value="searchForm.hazardSource"
                  placeholder="请选择隐患来源"
                  allowClear
                  class="form-control"
                  @change="handleSearch"
                >
                  <a-select-option value="selfDiscovered">自行发现</a-select-option>
                  <a-select-option value="businessGroup">业务保护组</a-select-option>
                  <a-select-option value="deviceGroup">设备保护组</a-select-option>
                </a-select>
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">是否生命线</label>
                <a-select
                  v-model:value="searchForm.isLifeline"
                  placeholder="请选择是否生命线"
                  allowClear
                  class="form-control"
                  @change="handleSearch"
                >
                  <a-select-option value="0">是</a-select-option>
                  <a-select-option value="1">否</a-select-option>
                </a-select>
              </div>
            </a-col>

            <!-- 第二行：详细筛选条件 -->
            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">责任人</label>
                <a-input
                  v-model:value="searchForm.responsiblePerson"
                  placeholder="请输入责任人"
                  class="form-control"
                />
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">整改人</label>
                <a-input
                  v-model:value="searchForm.remediationPerson"
                  placeholder="请输入整改人"
                  class="form-control"
                />
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">发现人</label>
                <a-input
                  v-model:value="searchForm.discoveredBy"
                  placeholder="请输入发现人"
                  class="form-control"
                />
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">客户名称</label>
                <a-input
                  v-model:value="searchForm.customerName"
                  placeholder="请输入客户名称"
                  class="form-control"
                />
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">电路编码</label>
                <a-input
                  v-model:value="searchForm.circuitCode"
                  placeholder="请输入电路编码"
                  class="form-control"
                />
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">光路组编码</label>
                <a-input
                  v-model:value="searchForm.opticalPathGroupCode"
                  placeholder="请输入光路组编码"
                  class="form-control"
                />
              </div>
            </a-col>

            <!-- 第三行：高级筛选条件 -->
            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">保护组场景</label>
                <a-select
                  v-model:value="searchForm.protectionScenario"
                  placeholder="请选择保护组场景"
                  allowClear
                  class="form-control"
                  @change="handleSearch"
                >
                  <a-select-option value="singleDeviceDualUplink">单设备双上联</a-select-option>
                  <a-select-option value="devicePairDualRoute">设备对双路由</a-select-option>
                  <a-select-option value="devicePairTripleRoute">设备对三路由</a-select-option>
                  <a-select-option value="aRing">A环</a-select-option>
                  <a-select-option value="other">其他场景</a-select-option>
                </a-select>
              </div>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="form-item-wrapper">
                <label class="form-label">设备类型</label>
                <a-select
                  v-model:value="searchForm.deviceType"
                  placeholder="请选择设备类型"
                  allowClear
                  class="form-control"
                  @change="handleSearch"
                >
                  <a-select-option value="broadbandAccess">宽带接入设备</a-select-option>
                  <a-select-option value="newCity">新城</a-select-option>
                  <a-select-option value="dslam">DSLAM设备</a-select-option>
                  <a-select-option value="olt">OLT设备</a-select-option>
                  <a-select-option value="ag">AG设备</a-select-option>
                  <a-select-option value="er">ER设备</a-select-option>
                  <a-select-option value="sr">SR设备</a-select-option>
                  <a-select-option value="dsw">DSW设备</a-select-option>
                  <a-select-option value="idcSwitch">IDC交换机</a-select-option>
                  <a-select-option value="bsc">BSC</a-select-option>
                  <a-select-option value="ceB">CE B设备</a-select-option>
                  <a-select-option value="itv">ITV设备</a-select-option>
                  <a-select-option value="cn2">CN2</a-select-option>
                  <a-select-option value="sdh">SDH</a-select-option>
                  <a-select-option value="dcsw">DCSW设备</a-select-option>
                  <a-select-option value="oltVip">OLT大客户设备</a-select-option>
                  <a-select-option value="other">其他设备</a-select-option>
                </a-select>
              </div>
            </a-col>

            <!-- 操作按钮 -->
            <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <div class="search-actions">
                <a-space size="large">
                  <a-button type="primary" size="large" @click="handleSearch" class="search-btn">
                    <template #icon>
                      <SearchOutlined />
                    </template>
                    查询
                  </a-button>
                  <a-button size="large" @click="handleReset" class="reset-btn">
                    <template #icon>
                      <ReloadOutlined />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-card>
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: pagination.showSizeChanger,
            showQuickJumper: pagination.showQuickJumper,
            showTotal: (total: number) => `共 ${total} 条`
          }"
          @change="handleTableChange"
          row-key="id"
          :scroll="{ x: 1500 }"
          size="middle"

          
        >
          <!-- 隐患等级 -->
          <template #hazardLevel="{ record }">
            <a-tag :color="getHazardLevelColor(record.hazardLevel)">
              {{ getHazardLevelText(record.hazardLevel) }}
            </a-tag>
          </template>

          <!-- 隐患来源 -->
          <template #hazardSource="{ record }">
            <a-tag :color="getHazardSourceColor(record.hazardSource)">
              {{ getHazardSourceText(record.hazardSource) }}
            </a-tag>
          </template>

          <!-- 是否生命线 -->
          <template #isLifeline="{ record }">
            <a-tag
              v-if="record.isLifeline === '0'"
              color="red"
              style="font-weight: bold;"
            >
              是
            </a-tag>
            <span v-else-if="record.isLifeline === '1'" style="color: #666;">否</span>
            <span v-else style="color: #ccc;">-</span>
          </template>

          <!-- 状态 -->
          <template #status="{ record }">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 保护组场景 -->
          <template #protectionScenario="{ record }">
            <span v-if="record.protectionScenario">
              {{ getProtectionScenarioName(record.protectionScenario) }}
            </span>
            <span v-else style="color: #ccc;">-</span>
          </template>

          <!-- 设备类型 -->
          <template #deviceType="{ record }">
            <span v-if="record.deviceType">
              {{ getDeviceTypeName(record.deviceType) }}
            </span>
            <span v-else style="color: #ccc;">-</span>
          </template>

          <!-- 操作 -->
          <template #action="{ record }">
            <a-space>
              <a-button
                type="link"
                size="small"
                @click="handleEdit(record)"
              >
                详情
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onActivated } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { useInfo } from '@/hooks/web/useRestAPI';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';

defineOptions({
  name: 'HazardRemediationList'
});

const router = useRouter();

// 初始化隐患整改查询服务
const hazardRemediationQueryService = useInfo({
  rootPath: '/graph-rest-api',
});

// 初始化隐患整改删除服务
const hazardRemediationDeleteService = useInfo({
  rootPath: '/graph-rest-api',
});

// 引入用户store
import { useUserStore } from '@/store/modules/user';

// 用户权限和地市信息
const userStore = useUserStore();
const cityUnchangable = ref(true); // 地市是否可切换
const userCityCode = ref(''); // 用户所属地市

// 搜索表单
const searchForm = reactive({
  cityCode: '',
  hazardType: '',
  hazardLevel: '',
  status: '',
  responsiblePerson: '',
  customerName: '',
  circuitCode: '',
  hazardSource: '',
  isLifeline: '',
  remediationPerson: '',
  protectionScenario: '', // 保护组场景
  deviceType: '', // 设备类型
  discoveredBy: '', // 发现人
  opticalPathGroupCode: '' // 光路组编码
});

// 初始化用户权限和地市信息
const initUserInfo = () => {
  console.log('userStore.getAreaCode:', userStore.getAreaCode);

  // 参照您提供的逻辑：js为省级用户，其他为地市用户
  if (userStore.getAreaCode == 'js') {
    // 省级用户：可以切换地市，默认不选择任何地市
    searchForm.cityCode = '';
    cityUnchangable.value = false; // js用户可以切换地市
    userCityCode.value = 'js';
    console.log('省级用户，可以切换地市');
  } else {
    // 地市用户：不可切换地市，默认选择用户所属地市
    // 将用户的地市代码转换为大写（与数据库保持一致）
    const userAreaCode = userStore.getAreaCode ? userStore.getAreaCode.toUpperCase() : 'XZ';
    searchForm.cityCode = userAreaCode;
    cityUnchangable.value = true; // 地市用户不可以切换地市
    userCityCode.value = userAreaCode;
    console.log('地市用户，不可切换地市，默认地市:', userAreaCode);
  }

  console.log('用户权限信息:', {
    areaCode: userStore.getAreaCode,
    cityUnchangable: cityUnchangable.value,
    userCityCode: userCityCode.value,
    searchFormCityCode: searchForm.cityCode
  });
};

// 表格数据
const dataSource = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 表格列定义
const columns = [
  {
    title: '整改标题',
    dataIndex: 'title',
    key: 'title',
    width: 180,
    ellipsis: true
  },
  {
    title: '地市',
    dataIndex: 'cityName',
    key: 'cityName',
    width: 70
  },
  {
    title: '隐患类型',
    dataIndex: 'hazardTypeName',
    key: 'hazardTypeName',
    width: 100
  },
  {
    title: '隐患等级',
    dataIndex: 'hazardLevel',
    key: 'hazardLevel',
    width: 90,
    slots: { customRender: 'hazardLevel' }
  },
  {
    title: '隐患来源',
    dataIndex: 'hazardSource',
    key: 'hazardSource',
    width: 100,
    slots: { customRender: 'hazardSource' }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 90,
    slots: { customRender: 'status' }
  },
  {
    title: '责任人',
    dataIndex: 'responsiblePerson',
    key: 'responsiblePerson',
    width: 80
  },
  {
    title: '整改人',
    dataIndex: 'remediationPerson',
    key: 'remediationPerson',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 120
  },
  {
    title: '预计完成时间',
    dataIndex: 'expectedCompletionDate',
    key: 'expectedCompletionDate',
    width: 100
  },
  {
    title: '实际完成时间',
    dataIndex: 'actualCompletionDate',
    key: 'actualCompletionDate',
    width: 100
  },
  // 设备保护组相关字段
  {
    title: '保护组场景',
    dataIndex: 'protectionScenario',
    key: 'protectionScenario',
    width: 120,
    ellipsis: true,
    slots: { customRender: 'protectionScenario' }
  },
  {
    title: '设备类型',
    dataIndex: 'deviceType',
    key: 'deviceType',
    width: 100,
    ellipsis: true,
    slots: { customRender: 'deviceType' }
  },
  // 自行发现相关字段
  {
    title: '发现人',
    dataIndex: 'discoveredBy',
    key: 'discoveredBy',
    width: 80
  },
  {
    title: '发现时间',
    dataIndex: 'discoveredDate',
    key: 'discoveredDate',
    width: 100
  },
  // 移到后面的字段
  {
    title: '是否生命线',
    dataIndex: 'isLifeline',
    key: 'isLifeline',
    width: 90,
    slots: { customRender: 'isLifeline' }
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 120,
    ellipsis: true
  },
  {
    title: '电路编码',
    dataIndex: 'circuitCode',
    key: 'circuitCode',
    width: 120,
    ellipsis: true
  },
  {
    title: '光路组编码',
    dataIndex: 'opticalPathGroupCode',
    key: 'opticalPathGroupCode',
    width: 120,
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
];

// 方法
const handleCreateRemediation = () => {
  router.push('/nrm/res-app/risk-manage/hazard-remediation-form');
};

const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });

  // 重置后重新设置用户地市信息
  if (cityUnchangable.value) {
    searchForm.cityCode = userCityCode.value;
  }

  pagination.current = 1;
  fetchData();
};

// 处理表格变化（分页、筛选、排序）
const handleTableChange = (paginationInfo: any, filters: any, sorter: any) => {
  console.log('表格变化 - 分页:', paginationInfo, '筛选:', filters, '排序:', sorter);
  handlePaginationChange(paginationInfo.current, paginationInfo.pageSize);
};

// 处理分页变化
const handlePaginationChange = (page: number, pageSize: number) => {
  console.log('分页变化 - 页码:', page, '每页大小:', pageSize);
  pagination.current = page;
  pagination.pageSize = pageSize;
  fetchData();
};



const handleEdit = (record: any) => {
  router.push({
    path: `/nrm/res-app/risk-manage/hazard-remediation-form`,
    query: {
      id: record.id,
      mode: 'view',
      cityCode: record.cityCode || 'xz'
    }
  });
};



const handleDelete = (record: any) => {
  // 在删除时进行权限校验
  const canDelete = canDeleteRecord(record);

  if (!canDelete) {
    message.error('权限不足：只有责任人或整改人可以删除工单');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除整改项目"${record.title}"吗？删除后无法恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 构建删除参数
        const deleteParams = {
          id: record.id,
          shardingCode: record.cityCode ? `ds_bc_o3_${record.cityCode.toLowerCase()}` : 'ds_bc_o3_xz'
        };

        console.log('删除参数:', deleteParams);

        // 设置删除服务的参数
        hazardRemediationDeleteService.info.value = deleteParams;

        // 调用后端删除API
        const response = await hazardRemediationDeleteService.doCreateNew('/api/hazard-remediation/delete');

        console.log('删除响应:', response);

        if (response && response.success) {
          message.success('删除成功');
          fetchData(); // 重新加载数据
        } else {
          message.error(response?.message || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败: ' + (error.message || '未知错误'));
      }
    }
  });
};

// 获取隐患等级颜色
const getHazardLevelColor = (level: string) => {
  const colorMap = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  };
  return colorMap[level] || 'default';
};

// 获取隐患等级文本
const getHazardLevelText = (level: string) => {
  const textMap = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  };
  return textMap[level] || level;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap = {
    draft: 'default',
    pending: 'orange',
    inProgress: 'blue',
    processing: 'blue',
    completed: 'green',
    cancelled: 'red'
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    pending: '待处理',
    inProgress: '整改中',
    processing: '整改中',
    completed: '已完成',
    cancelled: '已取消'
  };
  return textMap[status] || status;
};

// 判断是否可以删除记录
const canDeleteRecord = (record: any) => {
  // 获取当前用户信息
  const currentUser = userStore.getUserInfo?.realName || userStore.getUserInfo?.username || '';


  console.log('userStore.getUserInfo',userStore.getUserInfo);
  

  if (!currentUser) {
    return false; // 没有用户信息，不允许删除
  }

  // 获取记录的责任人和整改人
  const responsiblePerson = record.responsiblePerson || '';
  const remediationPerson = record.remediationPerson || '';

  // 检查当前用户是否是责任人或整改人
  const isResponsiblePerson = responsiblePerson && currentUser === responsiblePerson;
  const isRemediationPerson = remediationPerson && currentUser === remediationPerson;

  console.log('删除权限检查:', {
    recordId: record.id,
    currentUser,
    responsiblePerson,
    remediationPerson,
    isResponsiblePerson,
    isRemediationPerson,
    canDelete: isResponsiblePerson || isRemediationPerson
  });

  // 只有责任人或整改人可以删除记录
  return isResponsiblePerson || isRemediationPerson;
};

// 获取隐患来源颜色
const getHazardSourceColor = (source: string) => {
  const colorMap = {
    selfDiscovered: 'purple',
    businessGroup: 'blue',
    deviceGroup: 'green'
  };
  return colorMap[source] || 'default';
};

// 获取隐患来源文本
const getHazardSourceText = (source: string) => {
  const textMap = {
    selfDiscovered: '自行发现',
    businessGroup: '业务保护组',
    deviceGroup: '设备保护组'
  };
  return textMap[source] || source;
};

// 获取地市名称
const getCityName = (cityCode: string) => {
  const cityMap = {
    xz: '徐州',
    nj: '南京',
    sz: '苏州',
    nt: '南通',
    cz: '常州',
    wx: '无锡',
    yz: '扬州',
    zj: '镇江',
    ty: '泰州',
    ha: '淮安',
    yc: '盐城',
    lygd: '连云港',
    sq: '宿迁'
  };
  return cityMap[cityCode?.toLowerCase()] || cityCode;
};

// 获取隐患类型名称
const getHazardTypeName = (hazardType: string) => {
  const hazardTypeMap = {
    noOpticalPath: '无光路',
    singleOpticalPath: '单光路',
    singleRoute: '单路由',
    missingPipeline: '缺管道',
    samePipeline: '同管道',
    sameOpticalCable: '同光缆',
    intraOfficeOpticalPath: '局内光路',
    sameOfficeWell: '同局前井'
  };
  return hazardTypeMap[hazardType] || hazardType;
};

// 获取保护组场景名称
const getProtectionScenarioName = (scenario: string) => {
  const scenarioMap = {
    singleDeviceDualUplink: '单设备双上联',
    devicePairDualRoute: '设备对双路由',
    devicePairTripleRoute: '设备对三路由',
    aRing: 'A环',
    other: '其他场景'
  };
  return scenarioMap[scenario] || scenario;
};

// 获取设备类型名称
const getDeviceTypeName = (deviceType: string) => {
  const deviceTypeMap = {
    broadbandAccess: '宽带接入设备',
    newCity: '新城',
    dslam: 'DSLAM设备',
    olt: 'OLT设备',
    ag: 'AG设备',
    er: 'ER设备',
    sr: 'SR设备',
    dsw: 'DSW设备',
    idcSwitch: 'IDC交换机',
    bsc: 'BSC',
    ceB: 'CE B设备',
    itv: 'ITV设备',
    cn2: 'CN2',
    sdh: 'SDH',
    dcsw: 'DCSW设备',
    oltVip: 'OLT大客户设备',
    other: '其他设备'
  };
  return deviceTypeMap[deviceType] || deviceType;
};



// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '';
  try {
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return dateTime;
  }
};

// 格式化日期（只显示日期，不显示时间）
const formatDate = (dateTime: string) => {
  if (!dateTime) return '';
  try {
    const date = new Date(dateTime);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    return dateTime;
  }
};

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;

    // 构建查询参数
    const queryParams = {
      // 查询条件
      cityCode: searchForm.cityCode || undefined,
      hazardType: searchForm.hazardType || undefined,
      hazardLevel: searchForm.hazardLevel || undefined,
      status: searchForm.status || undefined,
      responsiblePerson: searchForm.responsiblePerson || undefined,
      customerName: searchForm.customerName || undefined,
      circuitCode: searchForm.circuitCode || undefined,
      hazardSource: searchForm.hazardSource || undefined,
      isLifeline: searchForm.isLifeline || undefined,
      remediationPerson: searchForm.remediationPerson || undefined,
      // 新增的搜索条件
      protectionScenario: searchForm.protectionScenario || undefined,
      deviceType: searchForm.deviceType || undefined,
      discoveredBy: searchForm.discoveredBy || undefined,
      opticalPathGroupCode: searchForm.opticalPathGroupCode || undefined,

      // 分页参数
      pageSize: pagination.pageSize,
      currentPage: pagination.current,

      // 分片代码（根据选择的地市生成，如果没有选择则使用默认）
      shardingCode: searchForm.cityCode ? `ds_bc_o3_${searchForm.cityCode.toLowerCase()}` : 'ds_bc_o3_xz'
    };

    console.log('查询参数:', queryParams);

    // 设置查询服务的参数
    hazardRemediationQueryService.info.value = queryParams;

    // 调用后端查询API
    const response = await hazardRemediationQueryService.doCreateNew('/api/hazard-remediation/query');

    console.log('查询响应:', response);

    // 根据实际返回值格式解析数据
    if (response && response.data) {
      // 处理返回的数据（数据在response.data中，不需要success字段）
      const data = response.data || [];

      // 数据转换和格式化
      const formattedData = data.map((item: any) => ({
        // 基本字段映射（后端返回的是下划线格式）
        id: item.id,
        title: item.title,
        cityCode: item.city_code,
        cityName: getCityName(item.city_code),
        hazardType: item.hazard_type,
        hazardTypeName: getHazardTypeName(item.hazard_type),
        hazardLevel: item.hazard_level,
        hazardSource: item.hazard_source,
        status: item.status,
        responsiblePerson: item.responsible_person,
        responsiblePersonContact: item.responsible_person_contact,
        remediationPerson: item.remediation_person,
        remediationPersonContact: item.remediation_person_contact,
        creator: item.creator,
        remediationDescription: item.remediation_description,

        // 时间字段格式化
        createTime: item.create_time ? formatDateTime(item.create_time) : '',
        updateTime: item.update_time ? formatDateTime(item.update_time) : '',
        expectedCompletionDate: item.expected_completion_date ? formatDate(item.expected_completion_date) : '',
        actualCompletionDate: item.actual_completion_date ? formatDate(item.actual_completion_date) : '',

        // 统计字段
        opticalPathCount: item.optical_path_count || 0,
        attachmentCount: item.attachment_count || 0,

        // 生命线业务信息
        isLifeline: item.is_lifeline,
        lifelineBusinessId: item.lifeline_business_id,
        lifelineBusinessName: item.lifeline_business_name,

        // 业务信息（业务保护组）
        circuitCode: item.circuit_code,
        circuitName: item.circuit_name,
        opticalPathGroupCode: item.optical_path_group_code,
        opticalPathGroupName: item.optical_path_group_name,

        // 客户信息（业务保护组）
        customerName: item.customer_name,
        customerAccount: item.customer_account,
        customerManager: item.customer_manager,
        customerManagerDepartment: item.customer_manager_department,
        customerLevel: item.customer_level,
        differentiatedServiceLevel: item.differentiated_service_level,

        // 保护组信息（设备保护组）
        protectionScenario: item.protection_scenario,
        customProtectionScenario: item.custom_protection_scenario,
        deviceType: item.device_type,
        customDeviceType: item.custom_device_type,
        selectedProtectionGroupId: item.selected_protection_group_id,

        // 自行发现相关字段
        selfDiscoveredDescription: item.self_discovered_description,
        discoveredBy: item.discovered_by,
        discoveredDate: item.discovered_date ? formatDate(item.discovered_date) : '',

        // 完成相关信息
        completionDescription: item.completion_description,
        userRemainingIssues: item.user_remaining_issues,
        exemptionReason: item.exemption_reason,
        completionOperator: item.completion_operator,
        completionTime: item.completion_time ? formatDateTime(item.completion_time) : '',

        // 检测关联信息
        latestDetectionId: item.latest_detection_id,
        initialDetectionId: item.initial_detection_id,
        completionDetectionId: item.completion_detection_id,
        detectionCount: item.detection_count || 0,
        hasRemainingIssues: item.has_remaining_issues
      }));

      dataSource.value = formattedData;

      // 设置分页信息（根据实际返回的pageInfo结构）
      if (response.pageInfo) {
        pagination.current = response.pageInfo.currentPage;
        pagination.pageSize = response.pageInfo.pageSize;
        pagination.total = response.pageInfo.totalCount;

        

        console.log('分页信息更新:', {
          currentPage: response.pageInfo.currentPage,
          pageSize: response.pageInfo.pageSize,
          totalCount: response.pageInfo.totalCount,
          totalPage: response.pageInfo.totalPage
        });
      } else {
        // 如果没有分页信息，使用数据长度作为总数
        pagination.total = formattedData.length;
        console.log('没有分页信息，使用数据长度:', pagination.total);
      }
    } else {
      console.warn('查询返回数据格式异常:', response);
      dataSource.value = [];
      pagination.total = 0;
      message.warning('查询返回数据格式异常');
    }

  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败: ' + (error.message || '未知错误'));
    dataSource.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 组件挂载
onMounted(() => {
  initUserInfo(); // 初始化用户权限和地市信息
  fetchData();
});

// 组件激活时刷新数据（从其他页面返回时）
onActivated(() => {
  console.log('隐患整改列表页面被激活，刷新数据');
  initUserInfo(); // 重新初始化用户信息
  fetchData();
});
</script>

<style lang="less" scoped>
// 列表页面容器 - 与表单页面保持一致的背景设计
.hazard-remediation-list-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.06) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

// 页面头部 - 玻璃拟态效果
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.15),
      0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .page-description {
    color: #6b7280;
    margin: 0;
    font-size: 15px;
  }
}

// 操作按钮样式
.action-section {
  .ant-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    height: 44px;
    padding: 0 24px;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    }

    &:hover::before {
      left: 100%;
    }

    .anticon {
      font-size: 16px;
    }
  }
}

// 优化的搜索区域样式
.search-section {
  margin-bottom: 24px;

  :deep(.ant-card) {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.12),
        0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .ant-card-head {
      background: transparent;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);

      .ant-card-head-title {
        font-weight: 600;
        font-size: 16px;
        color: #374151;
      }
    }

    .ant-card-body {
      padding: 32px;
    }
  }

  // 搜索表单容器
  .search-form-container {
    .form-item-wrapper {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .form-label {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0;
        line-height: 1.4;
      }

      .form-control {
        width: 100%;
        height: 40px;
        border-radius: 8px;
        border: 1px solid rgba(209, 213, 219, 0.8);
        transition: all 0.3s ease;
        font-size: 14px;

        &:hover {
          border-color: #3b82f6;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        &:focus,
        &.ant-select-focused {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        &:disabled {
          background-color: #f9fafb;
          color: #9ca3af;
          cursor: not-allowed;
        }
      }
    }

    // 操作按钮区域
    .search-actions {
      display: flex;
      justify-content: center;
      padding-top: 16px;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      margin-top: 24px;

      .search-btn {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        height: 44px;
        padding: 0 32px;
        border-radius: 12px;
        font-size: 15px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
          background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        }

        &:hover::before {
          left: 100%;
        }

        .anticon {
          font-size: 16px;
        }
      }

      .reset-btn {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(209, 213, 219, 0.8);
        height: 44px;
        padding: 0 32px;
        border-radius: 12px;
        font-size: 15px;
        font-weight: 600;
        color: #6b7280;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 1);
          border-color: rgba(156, 163, 175, 0.8);
          color: #374151;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .anticon {
          font-size: 16px;
        }
      }
    }
  }
}

// 表格区域卡片
.table-section {
  :deep(.ant-card) {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.12),
        0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // 表格样式优化
  :deep(.ant-table) {
    .ant-table-thead > tr > th {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      font-weight: 700;
      color: #374151;
      border-bottom: 2px solid #e5e7eb;
      font-size: 14px;
    }

    .ant-table-tbody > tr {
      transition: all 0.2s ease;

      &:hover {
        background: rgba(59, 130, 246, 0.04);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      font-size: 13px;
    }
  }

  // 分页样式
  :deep(.ant-pagination) {
    margin-top: 24px;
    text-align: center;

    .ant-pagination-item {
      border-radius: 8px;
      border: 1px solid rgba(209, 213, 219, 0.8);
      transition: all 0.3s ease;

      &:hover {
        border-color: #3b82f6;
        transform: translateY(-1px);
      }

      &.ant-pagination-item-active {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-color: #3b82f6;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      border-radius: 8px;
      border: 1px solid rgba(209, 213, 219, 0.8);
      transition: all 0.3s ease;

      &:hover {
        border-color: #3b82f6;
        transform: translateY(-1px);
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .hazard-remediation-list-container {
    padding: 16px;
  }

  .page-header {
    padding: 24px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .title-section {
    .page-title {
      font-size: 24px;
    }
  }

  .search-section {
    .search-form-container {
      .search-actions {
        .search-btn,
        .reset-btn {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }

  .action-section {
    .ant-btn-primary {
      width: 100%;
      max-width: 200px;
    }
  }
}

// 平板设备优化
@media (max-width: 1024px) and (min-width: 769px) {
  .search-section {
    .search-form-container {
      .search-actions {
        .search-btn,
        .reset-btn {
          min-width: 140px;
        }
      }
    }
  }
}
</style>
