<template>
  <div class="hazard-remediation-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">{{ isViewMode ? '隐患整改详情' : '隐患整改发起' }}</h1>
          <p class="page-description">{{ isViewMode ? '查看隐患整改相关信息' : '发起双路由隐患整改工作流程' }}</p>
        </div>
        <div class="action-section">
          <!-- 编辑/保存按钮 -->
          <a-button
            v-if="isEdit && isViewMode"
            type="primary"
            size="large"
            @click="handleEnterEditMode"
          >
            <template #icon>
              <EditOutlined />
            </template>
            编辑
          </a-button>

          <a-button
            v-if="isEdit && isEditMode"
            type="primary"
            size="large"
            @click="handleSaveChanges"
            :loading="submitting"
          >
            <template #icon>
              <SaveOutlined />
            </template>
            保存
          </a-button>



          <!-- 导出报告按钮 -->
          <a-button
            v-if="isEdit"
            size="large"
            @click="handleExportReport"
            :loading="exporting"
          >
            <template #icon>
              <ExportOutlined />
            </template>
            导出报告
          </a-button>

          <!-- <a-button @click="handleGoBack" size="large">
            <template #icon>
              <ArrowLeftOutlined />
            </template>
            返回列表
          </a-button> -->
        </div>
      </div>
    </div>

    <div class="form-container">
      <!-- 状态展示 -->
      <div class="form-section">
        <a-card class="status-card">
          <div class="status-container">
            <div class="status-header">
              <h3 class="status-title">隐患整改单状态</h3>
              <div class="status-info">
                <span class="status-label">当前状态：</span>
                <a-tag :color="getStatusColor(remediationForm.status)" class="status-tag">
                  {{ getStatusText(remediationForm.status) }}
                </a-tag>
              </div>
            </div>

            <div class="status-timeline">
              <a-steps :current="getCurrentStep()" size="small">
                <a-step title="草稿" description="整改单已创建，待进入整改阶段" />
                <a-step title="整改中" description="正在进行整改工作" />
                <a-step title="已完成" description="整改工作已完成" />
              </a-steps>
            </div>

            <div class="status-details" v-if="remediationForm.status !== 'draft'">
              <div class="status-detail-item">
                <span class="detail-label">创建时间：</span>
                <span class="detail-value">{{ remediationForm.createTime || '暂无' }}</span>
              </div>
              <div class="status-detail-item" v-if="remediationForm.status !== 'draft'">
                <span class="detail-label">最后更新：</span>
                <span class="detail-value">{{ remediationForm.updateTime || '暂无' }}</span>
              </div>
              <div class="status-detail-item" v-if="remediationForm.actualCompletionDate">
                <span class="detail-label">实际完成时间：</span>
                <span class="detail-value">{{ remediationForm.actualCompletionDate.format ? remediationForm.actualCompletionDate.format('YYYY/M/D HH:mm:ss') : formatDateTime(remediationForm.actualCompletionDate) }}</span>
              </div>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 基本信息与详细信息 -->
      <div class="form-section">
          <a-card title="基本信息与详细信息" class="form-card">
            <div class="form-grid">
              <!-- 基本信息部分 -->
              <div class="form-item">
                <label class="form-label required">地市</label>
                <a-select
                  v-model:value="remediationForm.cityCode"
                  placeholder="请选择地市"
                  @change="handleCityChange"
                  :disabled="isViewMode || cityUnchangable"
                >
                  <a-select-option value="XZ">徐州</a-select-option>
                  <a-select-option value="NJ">南京</a-select-option>
                  <a-select-option value="SZ">苏州</a-select-option>
                  <a-select-option value="NT">南通</a-select-option>
                  <a-select-option value="CZ">常州</a-select-option>
                  <a-select-option value="WX">无锡</a-select-option>
                  <a-select-option value="YZ">扬州</a-select-option>
                  <a-select-option value="ZJ">镇江</a-select-option>
                  <a-select-option value="TY">泰州</a-select-option>
                  <a-select-option value="HA">淮安</a-select-option>
                  <a-select-option value="YC">盐城</a-select-option>
                  <a-select-option value="LYGD">连云港</a-select-option>
                  <a-select-option value="SQ">宿迁</a-select-option>
                </a-select>
              </div>

              <div class="form-item">
                <label class="form-label required">区县</label>
                <a-input
                  v-model:value="remediationForm.district"
                  placeholder="请输入区县"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item">
                <label class="form-label required">专业</label>
                <a-select
                  v-model:value="remediationForm.speciality"
                  placeholder="请选择专业"
                  :disabled="isViewMode"
                >
                  <a-select-option value="传输">传输</a-select-option>
                  <a-select-option value="数据">数据</a-select-option>
                  <a-select-option value="IDC">IDC</a-select-option>
                  <a-select-option value="政企">政企</a-select-option>
                  <a-select-option value="接入">接入</a-select-option>                
                  <a-select-option value="无线">无线</a-select-option>

                </a-select>
              </div>

              <div class="form-item">
                <label class="form-label required">隐患类型</label>
                <a-select
                  v-model:value="remediationForm.hazardType"
                  placeholder="请选择隐患类型"
                  @change="handleHazardTypeChange"
                  :disabled="isViewMode"
                >
                  <a-select-option value="noOpticalPath">无光路</a-select-option>
                  <a-select-option value="singleOpticalPath">单光路</a-select-option>
                  <a-select-option value="singleRoute">单路由</a-select-option>
                  <a-select-option value="missingPipeline">缺管道</a-select-option>
                  <a-select-option value="samePipeline">同管道</a-select-option>
                  <a-select-option value="sameOpticalCable">同光缆</a-select-option>
                  <a-select-option value="intraOfficeOpticalPath">局内光路</a-select-option>
                  <a-select-option value="sameOfficeWell">同局前井</a-select-option>
                </a-select>
              </div>

              <div class="form-item">
                <label class="form-label required">隐患等级</label>
                <a-select
                  v-model:value="remediationForm.hazardLevel"
                  placeholder="请选择隐患等级"
                  :disabled="isViewMode"
                >
                  <a-select-option value="high">高风险</a-select-option>
                  <a-select-option value="medium">中风险</a-select-option>
                  <a-select-option value="low">低风险</a-select-option>
                </a-select>
              </div>

              <div class="form-item">
                <label class="form-label required">责任人</label>
                <a-input
                  v-model:value="remediationForm.responsiblePerson"
                  placeholder="请输入责任人姓名"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item">
                <label class="form-label">责任人联系方式</label>
                <a-input
                  v-model:value="remediationForm.responsiblePersonContact"
                  placeholder="请输入责任人联系方式"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item">
                <label class="form-label required">整改人</label>
                <a-input
                  v-model:value="remediationForm.remediationPerson"
                  placeholder="请输入整改人姓名"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item">
                <label class="form-label">整改人联系方式</label>
                <a-input
                  v-model:value="remediationForm.remediationPersonContact"
                  placeholder="请输入整改人联系方式"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item">
                <label class="form-label required">预计完成时间</label>
                <a-date-picker
                  v-model:value="remediationForm.expectedCompletionDate"
                  placeholder="请选择预计完成时间"
                  style="width: 100%"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item">
                <label class="form-label">
                  实际完成时间
                  <a-tooltip title="完成整改后会自动填写完成时间">
                    <QuestionCircleOutlined style="margin-left: 4px; color: #999;" />
                  </a-tooltip>
                </label>
                <a-date-picker
                  v-model:value="remediationForm.actualCompletionDate"
                  placeholder="完成整改后自动填写"
                  style="width: 100%"
                  disabled
                />
              </div>

              <!-- 详细信息部分 -->
              <div class="form-item full-width">
                <label class="form-label required">整改标题</label>
                <a-input
                  v-model:value="remediationForm.title"
                  placeholder="请输入整改标题"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item full-width">
                <label class="form-label required">整改描述及措施</label>
                <a-textarea
                  v-model:value="remediationForm.remediationDescription"
                  placeholder="请详细描述隐患情况、整改方案和具体措施"
                  :rows="5"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item full-width">
                <label class="form-label">整改方案附件</label>

                <!-- 暂存模式文件上传 -->
                <a-upload
                  v-model:file-list="remediationForm.attachments"
                  name="file"
                  list-type="text"
                  :multiple="true"
                  :before-upload="beforeUpload"
                  @remove="handleRemoveAttachment"
                  @download="handleDownloadAttachment"
                  :show-upload-list="{
                    showDownloadIcon: true,
                    showRemoveIcon: !isViewMode
                  }"
                  @change="handleAttachmentChange"
                  accept=".doc,.docx,.xls,.xlsx,.pdf,.jpg,.png"
                  :disabled="isViewMode"
                >
                  <a-button v-if="!isViewMode">
                    <template #icon>
                      <UploadOutlined />
                    </template>
                    上传整改相关附件
                  </a-button>
                </a-upload>


                <!-- 优化的文件状态提示 -->
                <div v-if="!isViewMode" class="upload-status-container">
                  <!-- 暂存文件提示 -->
                  <div v-if="stagedFiles.length > 0" class="staged-files-indicator">
                    <span class="staged-count">{{ stagedFiles.length }}</span>
                    <span class="staged-text">个文件待上传</span>
                  </div>

                  <!-- 简化的格式提示 -->
                  <div class="upload-formats-tip">
                    支持 DOC、XLS、PDF、图片等格式
                  </div>
                </div>
              </div>
            </div>
          </a-card>
      </div>



      <!-- 隐患来源选择 -->
      <div class="form-section">
        <a-card title="隐患来源" class="form-card">
          <div class="hazard-source-container">
            <div class="form-item hazard-source-item">
              <label class="form-label required hazard-source-label">请选择隐患来源</label>
              <a-select
                v-model:value="remediationForm.hazardSource"
                placeholder="请选择隐患来源"
                @change="handleHazardSourceChange"
                class="hazard-source-select"
                size="large"
                :disabled="isViewMode"
              >
                <a-select-option value="selfDiscovered">自行发现</a-select-option>
                <a-select-option value="businessGroup">业务保护组</a-select-option>
                <a-select-option value="deviceGroup">设备保护组</a-select-option>
              </a-select>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 自行发现录入 -->
      <div class="form-section" v-if="remediationForm.hazardSource === 'selfDiscovered'">
        <a-card title="自行发现录入" class="form-card">
          <div class="form-grid">
            <div class="form-item full-width">
              <label class="form-label">隐患发现说明</label>
              <a-textarea
                v-model:value="remediationForm.selfDiscoveredDescription"
                placeholder="请详细描述自行发现的隐患情况、发现过程等"
                :rows="3"
                :disabled="isViewMode"
              />
            </div>
            <div class="form-item">
              <label class="form-label">发现人</label>
              <a-input
                v-model:value="remediationForm.discoveredBy"
                placeholder="请输入发现人姓名"
                :disabled="isViewMode"
              />
            </div>
            <div class="form-item">
              <label class="form-label">发现时间</label>
              <a-date-picker
                v-model:value="remediationForm.discoveredDate"
                placeholder="请选择发现时间"
                style="width: 100%"
                :disabled="isViewMode"
              />
            </div>
          </div>
        </a-card>
      </div>

      <!-- 业务保护组信息 -->
      <div class="form-section" v-if="remediationForm.hazardSource === 'businessGroup'">
        <a-card title="业务保护组信息" class="form-card">
          <div class="form-grid">
            <div class="form-item">
              <label class="form-label">光路组编码</label>
              <a-input
                v-model:value="remediationForm.opticalPathGroupCode"
                placeholder="请输入光路组编码"
                :disabled="isViewMode"
              />
            </div>

            <div class="form-item">
              <label class="form-label">光路组名称</label>
              <a-input
                v-model:value="remediationForm.opticalPathGroupName"
                placeholder="请输入光路组名称"
                :disabled="isViewMode"
              />
            </div>

            <div class="form-item">
              <label class="form-label">是否生命线业务</label>
              <a-select
                v-model:value="remediationForm.isLifeline"
                placeholder="请选择是否生命线业务"
                @change="handleLifelineChange"
                :disabled="isViewMode"
              >
                <a-select-option value="0">是</a-select-option>
                <a-select-option value="1">否</a-select-option>
              </a-select>
            </div>

            <div class="form-item" v-if="remediationForm.isLifeline === '0'">
              <label class="form-label">生命线业务名称</label>
              <a-input
                v-model:value="remediationForm.lifelineBusinessName"
                placeholder="请输入生命线业务名称"
                :disabled="isViewMode"
              />
            </div>

            <div class="form-item">
              <label class="form-label">电路编码</label>
              <a-input
                v-model:value="remediationForm.circuitCode"
                placeholder="请输入电路编码"
                :disabled="isViewMode"
              />
            </div>

            <div class="form-item">
              <label class="form-label">电路名称</label>
              <a-input
                v-model:value="remediationForm.circuitName"
                placeholder="请输入电路名称"
                :disabled="isViewMode"
              />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 设备保护组信息 -->
      <div class="form-section" v-if="remediationForm.hazardSource === 'deviceGroup'">
        <a-card title="设备保护组信息" class="form-card">
          <div class="form-grid">
            <div class="form-item">
              <label class="form-label required">设备类型</label>
              <a-select
                v-model:value="remediationForm.deviceType"
                placeholder="请选择设备类型"
                @change="handleDeviceTypeChange"
                :disabled="isViewMode"
              >
                <a-select-option value="broadbandAccess">宽带接入设备</a-select-option>
                <a-select-option value="newCity">新城</a-select-option>
                <a-select-option value="dslam">DSLAM设备</a-select-option>
                <a-select-option value="olt">OLT设备</a-select-option>
                <a-select-option value="ag">AG设备</a-select-option>
                <a-select-option value="er">ER设备</a-select-option>
                <a-select-option value="sr">SR设备</a-select-option>
                <a-select-option value="dsw">DSW设备</a-select-option>
                <a-select-option value="idcSwitch">IDC交换机</a-select-option>
                <a-select-option value="bsc">BSC</a-select-option>
                <a-select-option value="ceB">CE B设备</a-select-option>
                <a-select-option value="itv">ITV设备</a-select-option>
                <a-select-option value="cn2">CN2</a-select-option>
                <a-select-option value="sdh">SDH</a-select-option>
                <a-select-option value="dcsw">DCSW设备</a-select-option>
                <a-select-option value="oltVip">OLT大客户设备</a-select-option>
                <a-select-option value="other">其他设备</a-select-option>
              </a-select>
            </div>

            <div class="form-item" v-if="remediationForm.deviceType === 'other'">
              <label class="form-label required">自定义设备类型</label>
              <a-input
                v-model:value="remediationForm.customDeviceType"
                placeholder="请输入设备类型"
                :disabled="isViewMode"
              />
            </div>

            <div class="form-item">
              <label class="form-label">保护组场景</label>
              <a-select
                v-model:value="remediationForm.protectionScenario"
                placeholder="请选择保护组场景（可选）"
                @change="handleProtectionScenarioChange"
                allowClear
                :disabled="isViewMode"
              >
                <a-select-option value="singleDeviceDualUplink">单设备双上联</a-select-option>
                <a-select-option value="devicePairDualRoute">设备对双路由</a-select-option>
                <a-select-option value="devicePairTripleRoute">设备对三路由</a-select-option>
                <a-select-option value="aRing">A环</a-select-option>
                <a-select-option value="other">其他场景</a-select-option>
              </a-select>
            </div>

            <div class="form-item" v-if="remediationForm.protectionScenario === 'other'">
              <label class="form-label required">自定义保护组场景</label>
              <a-input
                v-model:value="remediationForm.customProtectionScenario"
                placeholder="请输入保护组场景"
                :disabled="isViewMode"
              />
            </div>


          </div>
        </a-card>
      </div>

      <!-- 客户信息（仅业务保护组显示） -->
      <div class="form-section" v-if="remediationForm.hazardSource === 'businessGroup'">

          <a-card title="客户信息" class="form-card">
            <div class="form-grid">
              <div class="form-item">
                <label class="form-label">客户名称</label>
                <a-input
                  v-model:value="remediationForm.customerName"
                  placeholder="请输入客户名称"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item">
                <label class="form-label">客户账号</label>
                <a-input
                  v-model:value="remediationForm.customerAccount"
                  placeholder="请输入客户账号"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item">
                <label class="form-label">客户经理姓名</label>
                <a-input
                  v-model:value="remediationForm.customerManager"
                  placeholder="请输入客户经理姓名"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item">
                <label class="form-label">客户经理部门</label>
                <a-input
                  v-model:value="remediationForm.customerManagerDepartment"
                  placeholder="请输入客户经理部门"
                  :disabled="isViewMode"
                />
              </div>

              <div class="form-item">
                <label class="form-label">客户等级</label>
                <a-select
                  v-model:value="remediationForm.customerLevel"
                  placeholder="请选择客户等级"
                  @change="handleCustomerLevelChange"
                  :disabled="isViewMode"
                >
                  <a-select-option value="general">一般客户</a-select-option>
                  <a-select-option value="differentiated">差异化服务客户</a-select-option>
                </a-select>
              </div>

              <div class="form-item" v-if="remediationForm.customerLevel === 'differentiated'">
                <label class="form-label">差异化服务等级</label>
                <a-select
                  v-model:value="remediationForm.differentiatedServiceLevel"
                  placeholder="请选择差异化服务等级"
                  :disabled="isViewMode"
                >
                  <a-select-option value="2A">2A</a-select-option>
                  <a-select-option value="3A">3A</a-select-option>
                  <a-select-option value="4A">4A</a-select-option>
                  <a-select-option value="5A">5A</a-select-option>
                </a-select>
              </div>
            </div>
          </a-card>
      </div>

      <!-- 光路配置 -->
      <div class="form-section" v-if="remediationForm.hazardSource">
        <a-card title="相关光路" class="form-card">
          <div class="optical-paths-section">
            <!-- 添加光路按钮 -->
            <div class="add-path-section">
              <a-button
                v-if="!isViewMode"
                type="dashed"
                @click="handleAddOpticalPath"
                block
                size="large"
              >
                <template #icon>
                  <PlusOutlined />
                </template>
                添加光路编码
              </a-button>
            </div>

            <!-- 风险检测功能 -->
            <div v-if="opticalPaths.length > 0" class="risk-detection-section" style="margin-top: 16px;">
              <div class="risk-detection-buttons">
                <a-button
                  @click="performRiskDetection"
                  type="primary"
                  :loading="riskDetectionLoading"
                  :disabled="!canPerformRiskDetection"
                  style="margin-right: 8px;"
                >
                  <template #icon>
                    <ExperimentOutlined />
                  </template>
                  {{ remediationForm.status === 'inProgress' ? '整改中检测' : '风险检测' }}
                </a-button>

                <!-- 关联光路保护组按钮 -->
                <a-button
                  @click="handleAssociateProtectionGroups"
                  type="default"
                  :loading="associateLoading"
                  :disabled="!hasValidOpticalPaths"
                  style="margin-right: 8px;"
                  title="查询包含当前光路的保护组"
                >
                  <template #icon>
                    <LinkOutlined />
                  </template>
                  关联光路保护组
                </a-button>

                <a-button
                  v-if="riskDetectionResult"
                  @click="showRiskDetailModal = true"
                  type="default"
                >
                  <template #icon>
                    <EyeOutlined />
                  </template>
                  查看检测结果
                </a-button>
              </div>

              <!-- 风险检测结果展示 -->
              <div v-if="riskDetectionResult" style="margin-top: 12px;">
                <a-alert
                  :type="getRiskAlertType(riskDetectionResult.check_result)"
                  :message="getRiskResultMessage(riskDetectionResult.check_result)"
                  :description="riskDetectionResult.exception_info"
                  show-icon
                />
              </div>
            </div>

            <!-- 光路列表 -->
            <div v-if="opticalPaths.length > 0" class="optical-paths-list">
              <div
                v-for="(path, index) in opticalPaths"
                :key="index"
                class="optical-path-item"
              >
                <div class="path-header">
                  <span class="path-label">光路 {{ index + 1 }}</span>
                  <div class="path-actions">
                    <!-- 保存按钮（钩子图标） -->
                    <a-button
                      v-if="!isViewMode && !path.saved && path.code && path.code.trim()"
                      type="text"
                      @click="handleOpticalPathConfirm(index)"
                      :loading="path.saving"
                      class="save-button"
                      title="确认保存光路编码"
                      size="small"
                    >
                      <CheckOutlined style="color: #52c41a;" />
                    </a-button>
                    <!-- 已保存状态显示 -->
                    <span v-if="path.saved" class="saved-indicator" title="光路编码已保存">
                      <CheckCircleOutlined style="color: #52c41a;" />
                    </span>
                    <!-- 删除按钮 -->
                    <a-button
                      v-if="!isViewMode"
                      type="text"
                      danger
                      size="small"
                      @click="handleRemoveOpticalPath(index)"
                      :disabled="opticalPaths.length <= 1"
                      title="删除光路"
                    >
                      <template #icon>
                        <DeleteOutlined />
                      </template>
                    </a-button>
                  </div>
                </div>

                <div class="path-input-section">
                  <a-input
                    v-model:value="path.code"
                    placeholder="请输入光路编码（如：F1712080339）"
                    :disabled="isViewMode || (path.saved && path.code && path.code.trim() && validateOpticalPathCode(path.code))"
                  />
                </div>
              </div>
            </div>

            <!-- 查询按钮 -->
            <div class="query-actions" v-if="hasValidOpticalPaths">
              <a-button
                type="primary"
                @click="handleQueryOpticalPaths"
                :loading="queryLoading"
                block
              >
                <template #icon>
                  <SearchOutlined />
                </template>
                查询光路并生成地图
              </a-button>
            </div>
          </div>
        </a-card>
      </div>









      <!-- 地图显示区域 -->
      <div class="map-section" v-if="queryTriggered">
        <a-card title="光路地图" class="map-card">
          <div class="map-container">
            <div v-if="queryLoading" class="map-loading">
              <a-spin size="large">
                <template #indicator>
                  <LoadingOutlined style="font-size: 24px" spin />
                </template>
              </a-spin>
              <p>正在加载地图数据...</p>
            </div>

            <div v-else-if="mapDataLoaded" class="map-content">
              <OpticalPathMap
                ref="opticalPathMapRef"
                :optical-paths="validOpticalPathsForMap"
                :city-code="remediationForm.cityCode"
                @map-loaded="handleMapLoaded"
                @map-error="handleMapError"
              />
            </div>
          </div>
        </a-card>
      </div>

      <!-- 风险检测时间线 -->
      <a-card
        title="风险检测记录"
        class="form-card"
        v-if="isEdit && detectionTimeline.length > 0"
      >
        <a-timeline>
          <a-timeline-item
            v-for="item in detectionTimeline"
            :key="item.id"
            :color="getTimelineColor(item.timeline_type)"
          >
            <template #dot>
              <ClockCircleOutlined v-if="item.timeline_type === 'detection'" />
              <CheckCircleOutlined v-else-if="item.timeline_type === 'comparison'" />
              <ExclamationCircleOutlined v-else-if="item.timeline_type === 'exemption'" />
              <InfoCircleOutlined v-else />
            </template>
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="timeline-title">{{ item.event_description }}</span>
                <span class="timeline-time">{{ formatDateTime(item.event_time) }}</span>
              </div>
              <div class="timeline-details" v-if="item.event_data">
                <div v-if="item.timeline_type === 'detection'">
                  <a-tag :color="getRiskLevelColor(item.event_data.riskLevel)">
                    {{ item.event_data.checkResult }}
                  </a-tag>
                  <span v-if="item.event_data.exceptionInfo" class="detail-text exception-info">
                    异常信息: {{ item.event_data.exceptionInfo }}
                  </span>
                  <span class="detail-text">检测光路数量: {{ item.event_data.opticalPathCount }}</span>
                  <!-- 调试信息 -->
                  <div v-if="false" style="font-size: 10px; color: #999; margin-top: 4px;">
                    调试: {{ JSON.stringify(item.event_data) }}
                  </div>
                </div>
                <div v-else-if="item.timeline_type === 'comparison'">
                  <a-tag :color="getComparisonResultColor(item.event_data.comparisonResult)">
                    {{ getComparisonResultText(item.event_data.comparisonResult) }}
                  </a-tag>
                  <span class="detail-text">改善评分: {{ item.event_data.improvementScore }}分</span>
                </div>
              </div>
              <div class="timeline-operator">操作人: {{ item.operator }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-card>

      <!-- 优化的操作按钮区域 -->
      <div class="action-buttons-section">
        <div class="button-container">
          <!-- 草稿状态下的按钮布局 -->
          <template v-if="!isEdit && !isViewMode">
            <div class="button-group">
              <a-button class="action-btn secondary-btn" @click="handleCancel">
                <template #icon>
                  <ArrowLeftOutlined />
                </template>
                取消
              </a-button>
              <a-button
                class="action-btn draft-btn"
                @click="handleSaveDraft"
                :loading="savingDraft"
              >
                <template #icon>
                  <SaveOutlined />
                </template>
                保存草稿
              </a-button>
              <a-button
                class="action-btn primary-btn"
                @click="handleStartRemediation"
                :loading="submitting"
                :disabled="!canSubmit"
              >
                <template #icon>
                  <PlayCircleOutlined />
                </template>
                发起整改
              </a-button>
            </div>
          </template>

          <!-- 编辑已有记录的按钮布局（完成状态时不显示编辑按钮） -->
          <template v-else-if="isEdit && !isViewMode && remediationForm.status !== 'completed'">
            <div class="button-group">
              <a-button class="action-btn secondary-btn" @click="handleGoBack">
                <template #icon>
                  <ArrowLeftOutlined />
                </template>
                返回
              </a-button>
              <a-button
                class="action-btn primary-btn"
                @click="handleSaveChanges"
                :loading="submitting"
              >
                <template #icon>
                  <SaveOutlined />
                </template>
                保存修改
              </a-button>
            </div>
          </template>

          <!-- 已完成状态的按钮布局（只显示返回按钮） -->
          <template v-else-if="isEdit && remediationForm.status === 'completed'">
            <div class="button-group">
              <a-button class="action-btn secondary-btn" @click="handleGoBack">
                <template #icon>
                  <ArrowLeftOutlined />
                </template>
                返回
              </a-button>
            </div>
          </template>
        </div>
      </div>

      <!-- 状态操作按钮区域（放在表单最下面） -->
      <div class="status-action-section" v-if="isEdit">
        <a-card class="status-operations-card">
          <template #title>
            <div class="status-card-title">
              <span>状态操作</span>
              <a-tag :color="getStatusColor(remediationForm.status)" class="status-tag">
                {{ getStatusText(remediationForm.status) }}
              </a-tag>
            </div>
          </template>

          <div class="status-operations-content">
            <!-- 进入整改阶段按钮（草稿状态显示） -->
            <div v-if="remediationForm.status === 'draft'" class="operation-section">
              <div class="operation-buttons">
                <a-button
                  type="primary"
                  size="large"
                  @click="handleEnterRemediationPhase"
                  :loading="submitting"
                  class="primary-action-btn"
                >
                  <template #icon>
                    <PlayCircleOutlined />
                  </template>
                  进入整改阶段
                </a-button>
              </div>
            </div>

            <!-- 完成整改按钮（整改中状态显示） -->
            <div v-else-if="remediationForm.status === 'inProgress'" class="operation-section">
              <div class="operation-buttons">
                <a-button
                  type="primary"
                  size="large"
                  @click="handleCompleteRemediation"
                  :loading="completing"
                  class="success-action-btn"
                >
                  <template #icon>
                    <CheckOutlined />
                  </template>
                  完成整改
                </a-button>
              </div>
            </div>

            <!-- 已完成状态显示 -->
            <div v-else-if="remediationForm.status === 'completed'" class="operation-section completed-section">
              <div class="completion-indicator">
                <CheckCircleOutlined class="completion-icon" />
                <span class="completion-text">整改已完成</span>
              </div>
            </div>
          </div>
        </a-card>
      </div>
    </div>

    <!-- 风险检测结果详情弹窗 -->
    <a-modal
      v-model:open="showRiskDetailModal"
      title="风险检测详细结果"
      width="800px"
      :footer="null"
    >
      <div v-if="riskDetectionResult" class="risk-detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="检测结果">
            <a-tag :color="getRiskAlertType(riskDetectionResult.check_result) === 'error' ? 'red' :
                           getRiskAlertType(riskDetectionResult.check_result) === 'warning' ? 'orange' : 'green'">
              {{ getRiskResultMessage(riskDetectionResult.check_result) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="检测时间">
            {{ new Date().toLocaleString() }}
          </a-descriptions-item>
          <a-descriptions-item label="异常信息" :span="2">
            {{ riskDetectionResult.exception_info || '无异常信息' }}
          </a-descriptions-item>
          <a-descriptions-item label="路由数量" v-if="riskDetectionResult.route_num">
            {{ riskDetectionResult.route_num }}
          </a-descriptions-item>
          <a-descriptions-item label="检测光路" :span="2">
            <a-tag v-for="path in validOpticalPathsForMap" :key="path" color="blue" style="margin: 2px;">
              {{ path }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 如果有详细的检测数据，显示更多信息 -->
        <div v-if="riskDetectionResult.pipeSegments" style="margin-top: 16px;">
          <h4>管道段信息</h4>
          <a-table
            :dataSource="riskDetectionResult.pipeSegments"
            :columns="[
              { title: '管道段编码', dataIndex: 'code', key: 'code' },
              { title: '状态', dataIndex: 'status', key: 'status' },
              { title: '风险等级', dataIndex: 'riskLevel', key: 'riskLevel' }
            ]"
            size="small"
            :pagination="false"
          />
        </div>
      </div>
    </a-modal>

    <!-- 添加完成整改弹窗 -->
    <a-modal
      v-model:visible="showCompleteModal"
      title="完成整改确认"
      width="800px"
      :footer="null"
    >
      <a-spin :spinning="riskDetectionLoading">
        <div class="complete-modal-content">
          <!-- 风险检测选项 -->
          <div class="detection-option-section" style="margin-bottom: 20px;">
            <h3>风险检测</h3>
            <a-radio-group v-model:value="enableFinalDetection" @change="onDetectionOptionChange">
              <a-radio :value="true">执行整改后风险检测</a-radio>
              <a-radio :value="false">跳过风险检测，直接完成</a-radio>
            </a-radio-group>
            <div v-if="enableFinalDetection" style="margin-top: 10px;">
              <a-button
                type="primary"
                size="small"
                @click="performFinalRiskDetection"
                :loading="riskDetectionLoading"
                :disabled="!canPerformRiskDetection"
              >
                执行检测
              </a-button>
            </div>
          </div>

          <!-- 风险检测结果 -->
          <div v-if="enableFinalDetection && riskDetectionResult" class="detection-result-section" style="margin-bottom: 20px;">
            <h3>整改后风险检测结果</h3>
            <a-descriptions bordered size="small">
              <a-descriptions-item label="检测结果" :span="3">
                <a-tag :color="getRiskLevelColor(determineRiskLevel(riskDetectionResult?.check_result))">
                  {{ riskDetectionResult?.check_result || '未知' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="异常信息" :span="3">
                {{ riskDetectionResult?.exception_info || '无异常信息' }}
              </a-descriptions-item>
            </a-descriptions>
            <a-button type="link" @click="showRiskDetailModal = true">查看详细结果</a-button>
          </div>

          <!-- 整改时间线 -->
          <div class="timeline-section">
            <h3>整改历程</h3>
            <a-timeline>
              <a-timeline-item
                v-for="item in detectionTimeline"
                :key="item.id"
                :color="getTimelineColor(item.timeline_type)"
              >
                <template #dot>
                  <ClockCircleOutlined v-if="item.timeline_type === 'detection'" />
                  <CheckCircleOutlined v-if="item.timeline_type === 'completion'" />
                  <ExclamationCircleOutlined v-if="item.timeline_type === 'exemption'" />
                </template>
                <div class="timeline-item">
                  <div class="timeline-header">
                    <span class="timeline-title">{{ item.event_description }}</span>
                    <span class="timeline-time">{{ formatDateTime(item.event_time) }}</span>
                  </div>
                  <div class="timeline-content">
                    <p v-if="item.event_data?.check_result || item.event_data?.checkResult">
                      检测结果: <a-tag :color="getRiskLevelColor(item.event_data.risk_level || item.event_data.riskLevel)">
                        {{ item.event_data.check_result || item.event_data.checkResult }}
                      </a-tag>
                    </p>
                    <p v-if="item.event_data?.exception_info || item.event_data?.exceptionInfo" style="margin-top: 8px; color: #ff4d4f;">
                      异常信息: {{ item.event_data.exception_info || item.event_data.exceptionInfo }}
                    </p>
                    <p v-if="item.event_data?.opticalPathCount" style="margin-top: 4px; color: #666;">
                      检测光路数量: {{ item.event_data.opticalPathCount }}
                    </p>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </div>

          <!-- 完成整改表单 -->
          <div class="complete-form-section">
            <a-form layout="vertical">
              <a-form-item label="实际完成时间" required>
                <a-date-picker
                  v-model:value="completeForm.actualCompletionDate"
                  style="width: 100%"
                />
              </a-form-item>
              <a-form-item label="完成说明">
                <a-textarea
                  v-model:value="completeForm.completionNotes"
                  :rows="3"
                  placeholder="请输入整改完成情况说明"
                />
              </a-form-item>
              <a-form-item label="遗留问题" v-if="hasRiskIssues">
                <a-textarea
                  v-model:value="completeForm.remainingIssues"
                  :rows="2"
                  placeholder="请描述遗留问题"
                />
              </a-form-item>
              <a-form-item label="豁免理由" v-if="hasRiskIssues">
                <a-textarea
                  v-model:value="completeForm.exemptionReason"
                  :rows="2"
                  placeholder="如需申请豁免，请填写豁免理由"
                />
              </a-form-item>
            </a-form>
          </div>

          <!-- 操作按钮 -->
          <div class="modal-footer">
            <a-space>
              <a-button @click="showCompleteModal = false">取消</a-button>
              <a-button
                type="primary"
                :loading="completing"
                @click="performCompletion"
              >
                确认完成整改
              </a-button>
            </a-space>
          </div>
        </div>
      </a-spin>
    </a-modal>

    <!-- 光路保护组详情弹窗 -->
    <a-modal
      v-model:open="showProtectionGroupModal"
      title="关联的光路保护组"
      width="1200px"
      :footer="null"
      :destroy-on-close="true"
    >
      <div class="protection-groups-content">
        <a-alert
          message="提示"
          :description="`找到 ${associatedProtectionGroups.length} 个包含当前光路的保护组`"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />

        <div v-for="(group, index) in associatedProtectionGroups" :key="group.id" class="protection-group-card">
          <a-card :title="`保护组 ${index + 1}: ${group.name || group.code}`" size="small">
            <template #extra>
              <a-tag :color="getGroupStatusColor(group.result)">
                {{ group.result || '未检测' }}
              </a-tag>
            </template>

            <div class="group-info">
              <a-descriptions :column="2" size="small">
                <a-descriptions-item label="保护组编码">{{ group.code }}</a-descriptions-item>
                <a-descriptions-item label="保护组名称">{{ group.name || '-' }}</a-descriptions-item>
                <a-descriptions-item label="创建时间">{{ group.create_date || '-' }}</a-descriptions-item>
                <a-descriptions-item label="创建人">{{ group.create_op || '-' }}</a-descriptions-item>
                <a-descriptions-item label="匹配光路">
                  <a-tag color="blue">{{ group.matchedOpticalCode }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="检测结果">{{ group.result || '-' }}</a-descriptions-item>
              </a-descriptions>

              <!-- 操作按钮 -->
              <div class="group-actions" style="margin-top: 12px;">
                <a-button type="link" size="small" @click="viewGroupDetails(group)">
                  查看详情
                </a-button>
                <a-button type="link" size="small" @click="performGroupRiskCheck(group)">
                  风险检测
                </a-button>
              </div>
            </div>
          </a-card>
        </div>
      </div>
    </a-modal>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  EditOutlined,
  ExportOutlined,
  SearchOutlined,
  LoadingOutlined,
  UploadOutlined,
  ExperimentOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  PlayCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue';
import { useInfo, usePageQuery } from '@/hooks/web/useRestAPI';
import { useUserStoreWithOut } from '@/store/modules/user';
import { useMessage } from '@/hooks/web/useMessage';
import { useGo } from '@/hooks/web/usePage';
import { useTabs } from '@/hooks/web/useTabs';
import OpticalPathMap from '@/views/nrm/res-test/dual-routing/components/OpticalPathMap.vue';


defineOptions({
  name: 'HazardRemediationForm'
});

const router = useRouter();
const route = useRoute();
const userStore = useUserStoreWithOut();
const { createMessage } = useMessage();
const go = useGo();
const { closeCurrent } = useTabs();

// 获取页面模式（view/edit）
const pageMode = ref('view'); // 默认为查看模式
const isViewMode = computed(() => pageMode.value === 'view');
const isEditMode = computed(() => pageMode.value === 'edit');
const isEdit = computed(() => !!route.query.id);

// 整改单ID状态管理
const currentRemediationId = ref(route.query.id || '');

// 用户权限和地市信息
const cityUnchangable = ref(true); // 地市是否可切换

// 初始化用户权限和地市信息
const initUserInfo = () => {
  console.log('userStore.getAreaCode:', userStore.getAreaCode);

  // 参照您提供的逻辑：js为省级用户，其他为地市用户
  if (userStore.getAreaCode == 'js') {
    // 省级用户：可以切换地市，默认选择徐州
    if (!remediationForm.cityCode || remediationForm.cityCode === 'xz') {
      remediationForm.cityCode = 'XZ';
    }
    cityUnchangable.value = false; // js用户可以切换地市
    console.log('省级用户，可以切换地市');
  } else {
    // 地市用户：不可切换地市，默认选择用户所属地市
    // 将用户的地市代码转换为大写（与数据库保持一致）
    const userAreaCode = userStore.getAreaCode ? userStore.getAreaCode.toUpperCase() : 'XZ';
    remediationForm.cityCode = userAreaCode;
    cityUnchangable.value = true; // 地市用户不可以切换地市
    console.log('地市用户，不可切换地市，默认地市:', userAreaCode);
  }

  console.log('表单页面用户权限信息:', {
    areaCode: userStore.getAreaCode,
    cityUnchangable: cityUnchangable.value,
    formCityCode: remediationForm.cityCode
  });
};

// 文件暂存处理（替代即时上传）
const beforeUpload = (file: File) => {
  console.log('文件选择:', file.name, file.size);

  // 验证文件类型
  const allowedTypes = [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/pdf',
    'image/jpeg',
    'image/png'
  ];

  if (!allowedTypes.includes(file.type)) {
    message.error('不支持的文件类型，请选择 doc、docx、xls、xlsx、pdf、jpg、png 格式的文件');
    return false;
  }

  // 验证文件大小（限制为50MB）
  const maxSize = 50 * 1024 * 1024;
  if (file.size > maxSize) {
    message.error('文件大小不能超过50MB');
    return false;
  }

  // 将文件添加到暂存列表
  stagedFiles.value.push(file);

  message.success(`文件 ${file.name} 已暂存，将在保存时上传`);

  // 阻止自动上传，让 Upload 组件自己管理 file-list
  return false;
};

// 初始化隐患整改服务（参考getCblinfoService的定义方式）
const hazardRemediationService = useInfo({
  rootPath: '/graph-rest-api',
});

// 初始化风险分析服务
const riskAnalyzeService = useInfo({
  rootPath: '/graph-rest-api',
});

// 表单数据
const remediationForm = reactive({
  // ID信息
  id: '', // 整改单ID

  // 状态信息
  status: 'draft', // 状态：draft-草稿, processing-整改中, completed-已完成
  createTime: '', // 创建时间
  updateTime: '', // 最后更新时间

  // 基本信息
  cityCode: 'XZ', // 默认徐州（大写）
  district: '', // 区县
  speciality: '数据', // 专业
  hazardSource: '', // 隐患来源：selfDiscovered/businessGroup/deviceGroup
  hazardType: '',
  hazardLevel: '',
  title: '',
  remediationDescription: '', // 整改描述及措施（合并字段）
  expectedCompletionDate: null as Dayjs | null,
  actualCompletionDate: null as Dayjs | null, // 实际完成时间
  responsiblePerson: '',
  responsiblePersonContact: '', // 责任人联系方式
  remediationPerson: '', // 整改人
  remediationPersonContact: '', // 整改人联系方式

  // 保护组信息
  protectionScenario: '', // 保护组场景（设备保护组）
  customProtectionScenario: '', // 自定义保护组场景
  deviceType: '', // 设备类型（设备保护组）
  customDeviceType: '', // 自定义设备类型
  selectedProtectionGroupId: '', // 选择的保护组ID
  opticalPathGroupCode: '', // 光路组编码（业务保护组）
  opticalPathGroupName: '', // 光路组名称（业务保护组）

  // 生命线业务信息
  isLifeline: '', // 是否生命线业务：0-是，1-否
  lifelineBusinessId: '', // 生命线业务ID（保留用于兼容）
  lifelineBusinessName: '', // 生命线业务名称（手动填写）

  // 业务信息（业务保护组）
  circuitCode: '', // 电路编码
  circuitName: '', // 电路名称

  // 客户信息（业务保护组）
  customerName: '', // 客户名称
  customerAccount: '', // 客户账号
  customerManager: '', // 客户经理姓名
  customerManagerDepartment: '', // 客户经理部门
  customerLevel: '', // 客户等级
  differentiatedServiceLevel: '', // 差异化服务等级（2A-5A）

  // 附件信息
  attachments: [] as any[], // 整改方案附件

  // 自行发现相关字段
  selfDiscoveredDescription: '', // 自行发现说明
  discoveredBy: '', // 发现人
  discoveredDate: null as Dayjs | null // 发现时间
});

// 定义类型接口
interface OpticalPath {
  code: string;
  saved?: boolean; // 是否已保存到数据库
  saving?: boolean; // 是否正在保存
}

interface LifelineBusiness {
  id: string;
  name: string;
  account: string;
  customerName: string;
}

interface RiskDetectionResult {
  check_result: string;
  exception_info: string;
  route_num?: string;
  pipeSegments?: any[];
  [key: string]: any;
}




// 光路配置（初始为空数组，避免默认空光路）
const opticalPaths = ref<OpticalPath[]>([]);

// 光路保护组相关状态
const associateLoading = ref(false);
const associatedProtectionGroups = ref<any[]>([]);
const showProtectionGroupModal = ref(false);

// 生命线业务列表
const lifelineBusinessList = ref<LifelineBusiness[]>([]);



// 加载生命线业务列表
const loadLifelineBusinessList = async () => {
  try {
    // 这里应该调用实际的API获取生命线业务列表
    // 模拟数据
    lifelineBusinessList.value = [
      { id: '1', name: '江苏移动生命线业务', account: 'YD001', customerName: '江苏移动' },
      { id: '2', name: '江苏电信生命线业务', account: 'DX002', customerName: '江苏电信' },
      { id: '3', name: '江苏联通生命线业务', account: 'LT003', customerName: '江苏联通' },
      { id: '4', name: '政府专网生命线', account: 'GOV001', customerName: '江苏省政府' },
      { id: '5', name: '医疗系统生命线', account: 'MED001', customerName: '江苏省人民医院' }
    ];
  } catch (error) {
    console.error('加载生命线业务列表失败:', error);
    message.error('加载生命线业务列表失败');
  }
};

// 地图相关
const opticalPathMapRef = ref<any>(null);
const queryLoading = ref(false);
const queryTriggered = ref(false);
const mapDataLoaded = ref(false);

// 提交状态
const submitting = ref(false);
const completing = ref(false);
const exporting = ref(false);
const savingDraft = ref(false); // 保存草稿状态

// 文件暂存相关
const stagedFiles = ref<File[]>([]); // 暂存的文件列表
const uploadingFiles = ref(false); // 文件上传状态

// 风险检测相关状态
const riskDetectionLoading = ref(false);
const riskDetectionResult = ref<RiskDetectionResult | null>(null);
const showRiskDetailModal = ref(false);

// 风险检测时间线
const detectionTimeline = ref<any[]>([]);

// 完成整改弹窗相关
const showCompleteModal = ref(false);
const enableFinalDetection = ref(true); // 是否执行整改后风险检测
const completeForm = reactive({
  actualCompletionDate: dayjs(),
  completionNotes: '',
  remainingIssues: '',
  exemptionReason: ''
});
const latestDetectionResult = ref<any>(null);



// 计算属性
const hasValidOpticalPaths = computed(() => {
  return opticalPaths.value.some(path => path.code.trim() !== '');
});

const canSubmit = computed(() => {
  return remediationForm.cityCode &&
         remediationForm.hazardSource &&
         remediationForm.hazardType &&
         remediationForm.hazardLevel &&
         remediationForm.title &&
         remediationForm.remediationDescription &&
         remediationForm.expectedCompletionDate &&
         remediationForm.responsiblePerson &&
         remediationForm.remediationPerson &&
         hasValidOpticalPaths.value;
});

// 为地图组件提供的光路数据
const validOpticalPathsForMap = computed(() => {
  return opticalPaths.value
    .filter(path => path.code.trim() !== '')
    .map(path => path.code.trim());
});

// 风险检测相关计算属性
const canPerformRiskDetection = computed(() => {
  return hasValidOpticalPaths.value && !riskDetectionLoading.value;
});

// 判断是否有风险问题（用于显示遗留问题字段）
const hasRiskIssues = computed(() => {
  // 无论是否执行检测，都基于最新的检测结果判断
  let latestResult = riskDetectionResult.value;

  // 如果当前没有检测结果，尝试从时间线中获取最新的检测结果
  if (!latestResult && detectionTimeline.value.length > 0) {
    const latestDetection = detectionTimeline.value
      .filter(item => item.timeline_type === 'detection' && item.event_data)
      .sort((a, b) => new Date(b.event_time).getTime() - new Date(a.event_time).getTime())[0];

    if (latestDetection && latestDetection.event_data) {
      latestResult = {
        check_result: latestDetection.event_data.checkResult || latestDetection.event_data.check_result,
        exception_info: latestDetection.event_data.exceptionInfo || latestDetection.event_data.exception_info
      };
    }
  }

  if (!latestResult) {
    return false; // 没有任何检测结果，默认无问题
  }

  const checkResult = latestResult.check_result;
  return checkResult && checkResult !== '正常' && checkResult !== 'normal';
});

// 方法
const handleGoBack = async () => {
  await closeCurrent();
  go('/nrm/res-app/risk-manage/hazard-remediation-list');
};

// 取消操作（新建模式）
const handleCancel = async () => {
  // 清理暂存文件
  if (stagedFiles.value.length > 0) {
    console.log('清理暂存文件:', stagedFiles.value.map(f => f.name));
    stagedFiles.value = [];
    remediationForm.attachments = remediationForm.attachments.filter(file => !file.isStaged);
    message.info('已清理暂存文件');
  }

  // 如果已经创建了草稿记录，询问是否删除
  if (currentRemediationId.value) {
    Modal.confirm({
      title: '确认取消',
      content: '检测到已创建草稿记录，取消后将删除该草稿，是否继续？',
      onOk: async () => {
        try {
          // 删除草稿记录
          await deleteDraftRemediation();
          await closeCurrent();
          go('/nrm/res-app/risk-manage/hazard-remediation-list');
        } catch (error) {
          console.error('删除草稿失败:', error);
          message.error('删除草稿失败，但将返回列表页');
          await closeCurrent();
          go('/nrm/res-app/risk-manage/hazard-remediation-list');
        }
      },
      onCancel: () => {
        // 用户取消，不做任何操作
      }
    });
  } else {
    // 没有创建草稿，直接返回
    await closeCurrent();
    go('/nrm/res-app/risk-manage/hazard-remediation-list');
  }
};

// 保存草稿的基本校验
const canSaveDraft = computed(() => {
  return remediationForm.cityCode &&
         remediationForm.district &&
         remediationForm.speciality &&
         remediationForm.hazardSource &&
         remediationForm.title &&
         remediationForm.responsiblePerson &&
         remediationForm.remediationPerson;
});

// 保存草稿
const handleSaveDraft = async () => {
  try {
    // 基本必填字段校验
    if (!canSaveDraft.value) {
      message.error('请填写基本必填信息：地市、区县、专业、隐患来源、整改标题、责任人、整改人');
      return;
    }

    savingDraft.value = true;

    // 如果还没有创建草稿记录，先创建
    if (!currentRemediationId.value) {
      await createDraftRemediation();
    }

    // 上传暂存的文件
    if (stagedFiles.value.length > 0) {
      await uploadStagedFiles(currentRemediationId.value as string);
    }

    // 更新草稿数据
    await updateDraftData();

    message.success('草稿保存成功');

    // 保存成功后返回列表页
    await closeCurrent();
    go('/nrm/res-app/risk-manage/hazard-remediation-list');
  } catch (error) {
    console.error('保存草稿失败:', error);
    message.error('保存草稿失败: ' + (error.message || '未知错误'));
  } finally {
    savingDraft.value = false;
  }
};

// 发起整改（从草稿变为整改中）
const handleStartRemediation = async () => {
  try {
    // 验证表单
    if (!canSubmit.value) {
      message.error('请填写完整信息');
      return;
    }

    submitting.value = true;

    // 如果还没有创建草稿记录，先创建
    if (!currentRemediationId.value) {
      await createDraftRemediation();
    }

    // 上传暂存的文件
    if (stagedFiles.value.length > 0) {
      await uploadStagedFiles(currentRemediationId.value as string);
    }

    // 更新数据并变更状态为整改中
    await updateRemediationData('inProgress');

    message.success('隐患整改已发起，状态已更新为整改中');
    await closeCurrent();
    go('/nrm/res-app/risk-manage/hazard-remediation-list');
  } catch (error) {
    console.error('发起整改失败:', error);
    message.error('发起整改失败: ' + (error.message || '未知错误'));
  } finally {
    submitting.value = false;
  }
};

// 进入编辑模式
const handleEnterEditMode = () => {
  pageMode.value = 'edit';
  console.log('进入编辑模式');
};

// 保存更改（编辑模式）
const handleSaveChanges = async () => {
  try {
    submitting.value = true;

    // 上传暂存的文件
    if (stagedFiles.value.length > 0) {
      await uploadStagedFiles(currentRemediationId.value as string);
    }

    // 更新整改数据，保持当前状态
    await updateRemediationData(remediationForm.status);

    message.success('保存成功');
    console.log('保存成功，返回列表页');

    // 保存成功后返回列表页
    await closeCurrent();
    go('/nrm/res-app/risk-manage/hazard-remediation-list');
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败: ' + (error.message || '未知错误'));
  } finally {
    submitting.value = false;
  }
};



// 清理临时文件
const cleanupTempFiles = async () => {
  const tempFiles = remediationForm.attachments.filter(file =>
    file.s3Key && file.s3Key.includes('temp_')
  );

  if (tempFiles.length === 0) return;

  console.log('清理临时文件:', tempFiles.map(f => f.s3Key));

  for (const file of tempFiles) {
    try {
      // 调用隐患整改专用的删除附件API
      const deleteUrl = `/graph-rest-api/api/hazard-remediation/delete-attachment?s3Key=${encodeURIComponent(file.s3Key)}`;
      await fetch(deleteUrl, { method: 'DELETE' });
      console.log('已删除临时文件:', file.s3Key);
    } catch (error) {
      console.error('删除临时文件失败:', file.s3Key, error);
    }
  }
};

const handleCityChange = () => {
  // 当地市改变时，重置地图状态
  mapDataLoaded.value = false;
  queryTriggered.value = false;
  console.log('地市已更改:', remediationForm.cityCode);
};

const handleHazardTypeChange = () => {
  console.log('隐患类型已更改:', remediationForm.hazardType);
};

// 隐患来源变化处理
const handleHazardSourceChange = () => {
  // 清空所有相关字段
  // 保护组相关
  remediationForm.protectionScenario = '';
  remediationForm.customProtectionScenario = '';
  remediationForm.deviceType = '';
  remediationForm.customDeviceType = '';
  remediationForm.selectedProtectionGroupId = '';
  remediationForm.opticalPathGroupCode = '';
  remediationForm.opticalPathGroupName = '';

  // 生命线和业务相关
  remediationForm.isLifeline = '';
  remediationForm.lifelineBusinessId = '';
  remediationForm.circuitCode = '';
  remediationForm.circuitName = '';

  // 客户信息相关
  remediationForm.customerName = '';
  remediationForm.customerAccount = '';
  remediationForm.customerManager = '';
  remediationForm.customerManagerDepartment = '';
  remediationForm.customerLevel = '';
  remediationForm.differentiatedServiceLevel = '';

  // 自行发现相关
  remediationForm.selfDiscoveredDescription = '';
  remediationForm.discoveredBy = '';
  remediationForm.discoveredDate = null;



  // 重置光路配置为空数组（用户需要手动添加光路）
  opticalPaths.value = [];

  console.log('隐患来源已更改:', remediationForm.hazardSource);
};

// 保护组场景变化处理
const handleProtectionScenarioChange = () => {
  // 不再清空设备类型，保持用户已选择的设备类型
  // remediationForm.deviceType = ''; // 注释掉这行，避免清空设备类型
  remediationForm.selectedProtectionGroupId = '';

  // 根据场景类型调整光路数量（仅自行发现时）
  // 保护组场景变化时不自动添加空光路，让用户手动添加
  // 这样避免保存时包含空的光路编码

  console.log('保护组场景已更改:', remediationForm.protectionScenario);
};

// 设备类型变化处理
const handleDeviceTypeChange = () => {
  // 清空保护组选择
  remediationForm.selectedProtectionGroupId = '';

  console.log('设备类型已更改:', remediationForm.deviceType);
};

// 文件上传状态变化处理（暂存模式）
const handleAttachmentChange = (info: any) => {
  const { file, fileList } = info;

  console.log('文件状态变化:', {
    fileName: file.name,
    status: file.status,
    uid: file.uid
  });

  // 处理文件状态变化
  if (file.status === 'uploading') {
    // 文件正在上传（在暂存模式下不会触发）
    console.log(`${file.name} 正在处理...`);
  } else if (file.status === 'done') {
    // 文件添加完成，标记为暂存状态
    const fileIndex = fileList.findIndex(f => f.uid === file.uid);
    if (fileIndex > -1) {
      fileList[fileIndex].status = 'staged';
      fileList[fileIndex].isStaged = true;
    }
    console.log(`${file.name} 已添加到暂存列表`);
  } else if (file.status === 'error') {
    console.error(`${file.name} 处理失败:`, file.error);
    message.error(`${file.name} 处理失败`);
  } else if (file.status === 'removed') {
    // 文件被移除
    console.log(`${file.name} 已移除`);
  }

  // 更新文件列表，确保与组件状态同步
  remediationForm.attachments = fileList;
  console.log('更新后的附件列表:', remediationForm.attachments);
};



// 移除附件 - 支持暂存和已上传文件
const handleRemoveAttachment = async (file: any) => {
  console.log('删除附件:', file);

  try {
    // 如果是暂存文件，从暂存列表中移除
    if (file.isStaged) {
      const stagedIndex = stagedFiles.value.findIndex(f => f.name === file.name && f.size === file.size);
      if (stagedIndex > -1) {
        stagedFiles.value.splice(stagedIndex, 1);
      }
      console.log('删除暂存文件:', file.name);
      message.success('文件已从暂存列表移除');
    }
    // 如果文件已经上传到服务器，需要调用删除接口
    else if (file.s3Key && file.attachmentId) {
      const deleteUrl = `/graph-rest-api/api/hazard-remediation/delete-attachment?s3Key=${encodeURIComponent(file.s3Key)}&attachmentId=${file.attachmentId}`;

      const response = await fetch(deleteUrl, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

      const result = await response.json();
      if (result.code === '200') {
        console.log('服务器文件删除成功:', file.name);
        message.success('附件删除成功');
      } else {
        console.warn('服务器文件删除失败:', result.message);
        message.warning('附件删除失败: ' + result.message);
      }
    } else {
      // 其他情况（如上传失败的文件）
      console.log('删除临时文件:', file.name);
      message.success('附件已移除');
    }

    // 从前端显示列表中移除
    const index = remediationForm.attachments.findIndex((item: any) => item.uid === file.uid);
    if (index > -1) {
      remediationForm.attachments.splice(index, 1);
    }
  } catch (error) {
    console.error('删除附件失败:', error);
    message.error('删除附件失败: ' + (error.message || '未知错误'));
  }
};



// 上传暂存的文件到服务器
const uploadStagedFiles = async (remediationId: string) => {
  if (stagedFiles.value.length === 0) {
    console.log('没有暂存文件需要上传');
    return [];
  }

  console.log(`开始上传 ${stagedFiles.value.length} 个暂存文件到整改单 ${remediationId}`);
  uploadingFiles.value = true;

  const uploadResults: Array<{file: File, success: boolean, data?: any, error?: string}> = [];
  const currentUser = userStore.getUserInfo?.realName || userStore.getUserInfo?.username || '未知用户';

  try {
    for (const file of stagedFiles.value) {
      console.log(`上传文件: ${file.name}`);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('remediationId', remediationId);
      formData.append('uploader', currentUser);

      const response = await fetch('/graph-rest-api/api/hazard-remediation/upload-attachment', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

      const result = await response.json();
      if (result.code === '200') {
        console.log(`文件 ${file.name} 上传成功:`, result.data);
        uploadResults.push({
          file,
          success: true,
          data: result.data
        });

        // 更新前端显示列表中对应文件的状态
        const attachmentIndex = remediationForm.attachments.findIndex(
          (item: any) => item.originFileObj === file
        );
        if (attachmentIndex > -1) {
          const attachment = remediationForm.attachments[attachmentIndex];
          attachment.status = 'done';
          attachment.isStaged = false;
          attachment.s3Key = result.data.s3Key;
          attachment.attachmentId = result.data.attachmentId;
          attachment.url = `/graph-rest-api/api/file/download?s3Key=${encodeURIComponent(result.data.s3Key)}`;
        }
      } else {
        console.error(`文件 ${file.name} 上传失败:`, result.message);
        uploadResults.push({
          file,
          success: false,
          error: result.message
        });
      }
    }

    // 清空暂存列表
    stagedFiles.value = [];

    const successCount = uploadResults.filter(r => r.success).length;
    const failCount = uploadResults.filter(r => !r.success).length;

    if (failCount === 0) {
      message.success(`所有文件上传成功 (${successCount}个)`);
    } else {
      message.warning(`${successCount}个文件上传成功，${failCount}个文件上传失败`);
    }

    return uploadResults;
  } catch (error) {
    console.error('批量上传文件失败:', error);
    message.error('文件上传失败: ' + (error.message || '未知错误'));
    return [];
  } finally {
    uploadingFiles.value = false;
  }
};

// 下载附件
const handleDownloadAttachment = (file: any) => {
  try {
    console.log('下载附件:', file);

    // 如果是临时文件（前端上传但未保存），使用原始文件对象创建下载
    if (file.isTemp && file.originFileObj) {
      const url = URL.createObjectURL(file.originFileObj);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.name || '附件';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      return;
    }

    // 如果有下载URL，直接下载
    if (file.url) {
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name || file.originalFilename || '附件';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      return;
    }

    // 如果有S3信息，构建下载URL
    if (file.s3Key || file.s3_key) {
      const s3Key = file.s3Key || file.s3_key;
      const downloadUrl = `/graph-rest-api/api/file/download?s3Key=${encodeURIComponent(s3Key)}`;

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = file.name || file.originalFilename || file.original_filename || '附件';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      return;
    }

    message.error('附件信息不完整，无法下载');
  } catch (error) {
    console.error('下载附件失败:', error);
    message.error('下载附件失败: ' + (error?.message || '未知错误'));
  }
};

// 生命线业务变化处理
const handleLifelineChange = () => {
  if (remediationForm.isLifeline === '1') {
    // 非生命线业务，清空生命线业务相关字段
    remediationForm.lifelineBusinessId = '';
    remediationForm.lifelineBusinessName = '';
  }
  console.log('生命线业务状态已更改:', remediationForm.isLifeline);
};

// 客户等级变化处理
const handleCustomerLevelChange = () => {
  if (remediationForm.customerLevel !== 'differentiated') {
    // 如果不是差异化服务，清空差异化服务等级
    remediationForm.differentiatedServiceLevel = '';
  }

  console.log('客户等级已更改:', remediationForm.customerLevel);
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap = {
    draft: 'blue',
    inProgress: 'orange',
    completed: 'green'
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    inProgress: '整改中',
    completed: '已完成'
  };
  return textMap[status] || status;
};

// 获取当前步骤
const getCurrentStep = () => {
  const stepMap = {
    draft: 0,
    inProgress: 1,
    completed: 2
  };
  return stepMap[remediationForm.status] || 0;
};

// 时间线相关方法
const getTimelineColor = (type: string) => {
  const colorMap = {
    detection: 'blue',
    comparison: 'green',
    exemption: 'orange',
    completion: 'purple'
  };
  return colorMap[type] || 'default';
};

const getRiskLevelColor = (level: string) => {
  const colorMap = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  };
  return colorMap[level] || 'default';
};

const getComparisonResultColor = (result: string) => {
  const colorMap = {
    improved: 'green',
    unchanged: 'orange',
    worsened: 'red'
  };
  return colorMap[result] || 'default';
};

const getComparisonResultText = (result: string) => {
  const textMap = {
    improved: '风险改善',
    unchanged: '风险无变化',
    worsened: '风险恶化'
  };
  return textMap[result] || result;
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '';
  try {
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return dateTime;
  }
};









// 光路编码格式校验
const validateOpticalPathCode = (code: string): boolean => {
  if (!code || code.trim() === '') {
    return true; // 空值允许，由必填校验处理
  }

  // 光路编码格式：F或E开头，后跟10位数字
  // 例如：F1712080339, E2306101457
  const pattern = /^[FE]\d{10}$/;
  return pattern.test(code.trim());
};

// 光路编码确认保存处理（钩子按钮点击）
const handleOpticalPathConfirm = async (index: number) => {
  const path = opticalPaths.value[index];
  const trimmedValue = path.code.trim();

  // 如果是空值，不处理
  if (!trimmedValue) {
    message.warning('请输入光路编码');
    return;
  }

  // 验证格式
  if (!validateOpticalPathCode(trimmedValue)) {
    message.error(`光路编码格式不正确，应为F或E开头后跟10位数字，如：F1712080339`);
    return;
  }

  // 检查是否重复
  const existingCodes = opticalPaths.value
    .filter((_, i) => i !== index)
    .map(p => p.code.trim())
    .filter(code => code !== '');

  if (existingCodes.includes(trimmedValue)) {
    message.error('光路编码不能重复');
    return;
  }

  try {
    // 设置保存状态
    opticalPaths.value[index].saving = true;

    // 如果是编辑模式，立即保存到数据库
    if (isEdit.value && route.query.id) {
      await saveOpticalPathsToDatabase();
      opticalPaths.value[index].saved = true;
      message.success('光路编码已保存');
    } else {
      // 新建模式下，只标记为已确认状态（等待整体保存）
      opticalPaths.value[index].saved = true;
      message.success('光路编码已确认');
    }
  } catch (error) {
    console.error('保存光路编码失败:', error);
    message.error('保存光路编码失败');
  } finally {
    // 清除保存状态
    opticalPaths.value[index].saving = false;
  }
};

// 光路编码保存处理（保留用于兼容，但不再使用）
const handleOpticalPathSave = async (index: number, value: string) => {
  // 这个方法保留但不再使用，因为现在使用钩子按钮手动确认
  console.log('handleOpticalPathSave 已废弃，请使用钩子按钮确认保存');
};

const handleAddOpticalPath = () => {
  opticalPaths.value.push({
    code: '',
    saved: false,
    saving: false
  });
};

const handleRemoveOpticalPath = async (index: number) => {
  try {
    // 检查索引是否有效
    if (index < 0 || index >= opticalPaths.value.length) {
      console.warn('删除光路：无效的索引', index);
      return;
    }

    const pathToRemove = opticalPaths.value[index];
    console.log('删除光路:', { index, path: pathToRemove });

    // 删除本地数据
    opticalPaths.value.splice(index, 1);

    // 如果是编辑模式且有有效光路，保存到数据库
    if (isEdit.value && route.query.id) {
      try {
        await saveOpticalPathsToDatabase();
        message.success('光路删除成功');
      } catch (error) {
        console.error('保存光路到数据库失败:', error);
        // 即使保存失败，本地删除已经完成，给用户提示
        message.warning('光路已从本地删除，但同步到服务器时出现问题');
      }
    } else {
      // 新建模式下，只是本地删除
      console.log('新建模式：光路已从本地删除');
      message.success('光路已删除');
    }
  } catch (error) {
    console.error('删除光路失败:', error);
    message.error('删除光路失败: ' + (error?.message || '未知错误'));
  }
};





// 保存光路到数据库
const saveOpticalPathsToDatabase = async () => {
  try {
    const cityCode = route.query.cityCode as string || remediationForm.cityCode || 'xz';
    const validOpticalPaths = opticalPaths.value
      .filter(path => path.code.trim() !== '')
      .map(path => ({ opticalPathCode: path.code.trim() }));

    const params = {
      remediationId: route.query.id,
      opticalPaths: validOpticalPaths,
      shardingCode: `ds_bc_o3_${cityCode.toLowerCase()}`
    };

    console.log('保存光路参数:', params);

    // 设置服务的info数据，然后调用API
    hazardRemediationService.info.value = params;
    const response = await hazardRemediationService.doCreateNew('/api/hazard-remediation/optical-paths/batch');

    if (!response || !response.success) {
      throw new Error(response?.message || '保存光路失败');
    }

    console.log('光路保存成功');
  } catch (error) {
    console.error('保存光路到数据库失败:', error);
    throw error;
  }
};
// 查询光路数据
const handleQueryOpticalPaths = async () => {
  if (!hasValidOpticalPaths.value) {
    message.warning('请先配置光路编码');
    return;
  }

  try {
    queryLoading.value = true;
    queryTriggered.value = true;
    mapDataLoaded.value = false;

    console.log('开始查询光路数据:', validOpticalPathsForMap.value);

    // 等待一小段时间让UI更新
    await nextTick();

    // 设置地图数据已加载状态，让地图组件显示
    setTimeout(() => {
      mapDataLoaded.value = true;
      queryLoading.value = false;
    }, 500);

  } catch (error) {
    console.error('查询光路数据失败:', error);
    message.error('查询光路数据失败');
    queryLoading.value = false;
    queryTriggered.value = false;
  }
};

// 地图加载完成
const handleMapLoaded = () => {
  console.log('地图加载完成');
};

// 地图加载错误
const handleMapError = (error: any) => {
  console.error('地图加载错误:', error);
  message.error('地图加载失败');
};

// 风险检测方法
const performRiskDetection = async () => {
  // 调用带类型的风险检测方法，使用自动判断类型
  await performRiskDetectionWithType('auto');
};

// 获取风险警告类型
const getRiskAlertType = (checkResult: string) => {
  if (!checkResult) return 'info';

  switch (checkResult.toLowerCase()) {
    case 'high_risk':
    case 'critical':
    case 'error':
      return 'error';
    case 'medium_risk':
    case 'warning':
      return 'warning';
    case 'low_risk':
    case 'info':
      return 'info';
    case 'no_risk':
    case 'success':
      return 'success';
    default:
      return 'info';
  }
};

// 获取风险结果消息
const getRiskResultMessage = (checkResult: string) => {
  if (!checkResult) return '检测完成';

  switch (checkResult.toLowerCase()) {
    case 'high_risk':
      return '高风险：发现严重风险问题';
    case 'medium_risk':
      return '中风险：发现潜在风险问题';
    case 'low_risk':
      return '低风险：发现轻微风险问题';
    case 'no_risk':
      return '无风险：未发现风险问题';
    case 'critical':
      return '严重：发现关键问题';
    case 'warning':
      return '警告：发现需要注意的问题';
    case 'error':
      return '错误：检测过程中发现错误';
    case 'success':
      return '成功：检测通过';
    default:
      return `检测结果：${checkResult}`;
  }
};

// 关联光路保护组
const handleAssociateProtectionGroups = async () => {
  if (!hasValidOpticalPaths.value) {
    createMessage.warning('请先配置光路编码');
    return;
  }

  try {
    associateLoading.value = true;

    // 获取有效的光路编码
    const validOpticalCodes = opticalPaths.value
      .filter(path => path.code.trim() !== '')
      .map(path => path.code.trim());

    console.log('开始查询光路保护组，光路编码:', validOpticalCodes);

    // 获取城市代码
    const cityCode = remediationForm.cityCode || 'XZ';

    // 清空之前的结果
    associatedProtectionGroups.value = [];

    // 为每个光路编码单独查询保护组
    for (const optCode of validOpticalCodes) {
      console.log('查询光路编码:', optCode);

      // 使用与光路保护组管理页面相同的查询方式
      const protectionGroupService = usePageQuery({
        rootPath: '/graph-rest-api',
        queryUrl: '/api/opt-group-api',
        filter: {
          opt_code: optCode,
          ds: cityCode,
        },
        pagination: {
          pageSize: 100, // 设置较大的页面大小以获取所有结果
          hideOnSinglePage: true,
        },
      });

      // 执行查询
      await protectionGroupService.pageQuery();

      // 获取查询结果
      const groups = protectionGroupService.dataSource.value || [];

      // 为每个保护组添加匹配的光路编码信息
      groups.forEach(group => {
        group.matchedOpticalCode = optCode;
      });

      // 添加到总结果中
      associatedProtectionGroups.value.push(...groups);

      console.log(`光路编码 ${optCode} 查询到 ${groups.length} 个保护组`);
    }

    if (associatedProtectionGroups.value.length > 0) {
      createMessage.success(`成功关联 ${associatedProtectionGroups.value.length} 个光路保护组`);
      showProtectionGroupModal.value = true;
    } else {
      createMessage.info('未找到包含这些光路的保护组');
    }

  } catch (error) {
    console.error('关联光路保护组失败:', error);
    const errorMsg = error?.message || error?.toString() || '未知错误';
    createMessage.error('关联光路保护组失败: ' + errorMsg);
  } finally {
    associateLoading.value = false;
  }
};

// 获取保护组状态颜色
const getGroupStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    '正常': 'green',
    '单路由': 'orange',
    '同光缆': 'red',
    '缺管道': 'red',
    '同局前井': 'orange',
    '局内光路': 'orange',
    '无光路': 'red'
  };
  return colorMap[status] || 'default';
};

// 查看保护组详情
const viewGroupDetails = (group: any) => {
  // 跳转到保护组详情页面
  const cityCode = remediationForm.cityCode || 'XZ';
  go(`/nrm/res-app/risk-analyse/opt-road/opt_road_group_management?id=${group.id}&cityCode=${cityCode}`);
};

// 执行保护组风险检测
const performGroupRiskCheck = async (group: any) => {
  try {
    createMessage.loading('正在执行风险检测...', 0);

    // 这里可以调用保护组风险检测API
    // 暂时显示提示信息
    setTimeout(() => {
      message.destroy();
      createMessage.success('风险检测已启动，请稍后查看结果');
    }, 2000);

  } catch (error) {
    message.destroy();
    console.error('风险检测失败:', error);
    createMessage.error('风险检测失败: ' + (error.message || '未知错误'));
  }
};

// 删除草稿整改单
const deleteDraftRemediation = async () => {
  if (!currentRemediationId.value) return;

  try {
    const cityCode = (route.query.cityCode as string) || 'xz';
    const deleteParams = {
      id: currentRemediationId.value,
      shardingCode: `ds_bc_o3_${cityCode.toLowerCase()}`
    };

    hazardRemediationService.info.value = deleteParams;
    const response = await hazardRemediationService.doCreateNew('/api/hazard-remediation/delete');

    if (response && response.success) {
      console.log('草稿删除成功');
      currentRemediationId.value = '';
    } else {
      throw new Error(response?.message || '删除草稿失败');
    }
  } catch (error) {
    console.error('删除草稿失败:', error);
    throw error;
  }
};

// 更新草稿数据
const updateDraftData = async () => {
  if (!currentRemediationId.value) return;

  try {
    const cityCode = (route.query.cityCode as string) || remediationForm.cityCode || 'xz';

    // 获取有效光路编码
    const validOpticalPaths = opticalPaths.value
      .filter(path => path.code.trim() !== '')
      .map(path => path.code.trim());

    const updateParams = {
      id: currentRemediationId.value,
      // 基本信息
      cityCode: remediationForm.cityCode.toUpperCase(),
      areaCode: remediationForm.cityCode.toLowerCase(),
      district: remediationForm.district || '未指定',
      speciality: remediationForm.speciality || '数据',
      hazardSource: remediationForm.hazardSource || 'selfDiscovered',
      hazardType: remediationForm.hazardType || 'singleRoute',
      hazardLevel: remediationForm.hazardLevel || 'medium',
      title: remediationForm.title || '隐患整改草稿',
      remediationDescription: remediationForm.remediationDescription || '待完善整改描述',
      expectedCompletionDate: remediationForm.expectedCompletionDate?.format('YYYY-MM-DD') ||
                             dayjs().add(7, 'day').format('YYYY-MM-DD'),
      actualCompletionDate: remediationForm.actualCompletionDate?.format('YYYY-MM-DD'),
      responsiblePerson: remediationForm.responsiblePerson || userStore.getUserInfo?.realName || '未指定',
      responsiblePersonContact: remediationForm.responsiblePersonContact || '',
      remediationPerson: remediationForm.remediationPerson || userStore.getUserInfo?.realName || '未指定',
      remediationPersonContact: remediationForm.remediationPersonContact || '',

      // 保护组信息
      protectionScenario: remediationForm.protectionScenario,
      customProtectionScenario: remediationForm.customProtectionScenario,
      deviceType: remediationForm.deviceType,
      customDeviceType: remediationForm.customDeviceType,
      selectedProtectionGroupId: remediationForm.selectedProtectionGroupId,
      opticalPathGroupCode: remediationForm.opticalPathGroupCode,
      opticalPathGroupName: remediationForm.opticalPathGroupName,

      // 生命线业务信息
      isLifeline: remediationForm.isLifeline,
      lifelineBusinessId: remediationForm.lifelineBusinessId,
      lifelineBusinessName: remediationForm.lifelineBusinessName,

      // 业务信息
      circuitCode: remediationForm.circuitCode,
      circuitName: remediationForm.circuitName,

      // 客户信息
      customerName: remediationForm.customerName,
      customerAccount: remediationForm.customerAccount,
      customerManager: remediationForm.customerManager,
      customerManagerDepartment: remediationForm.customerManagerDepartment,
      customerLevel: remediationForm.customerLevel,
      differentiatedServiceLevel: remediationForm.differentiatedServiceLevel,

      // 自行发现相关字段
      selfDiscoveredDescription: remediationForm.selfDiscoveredDescription,
      discoveredBy: remediationForm.discoveredBy,
      discoveredDate: remediationForm.discoveredDate?.format('YYYY-MM-DD'),

      // 光路信息
      opticalPaths: validOpticalPaths,

      // 系统信息
      creator: userStore.getUserInfo?.realName || userStore.getUserInfo?.username || '未知用户',

      // 保持草稿状态
      status: 'draft',
      updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      shardingCode: `ds_bc_o3_${cityCode.toLowerCase()}`
    };

    console.log('更新草稿参数:', updateParams);

    hazardRemediationService.info.value = updateParams;
    const response = await hazardRemediationService.doCreateNew('/api/hazard-remediation/update');

    if (!response || !response.success) {
      throw new Error(response?.message || '更新草稿失败');
    }
  } catch (error) {
    console.error('更新草稿数据失败:', error);
    throw error;
  }
};

// 更新整改数据并变更状态
const updateRemediationData = async (newStatus: string) => {
  if (!currentRemediationId.value) return;

  try {
    const cityCode = (route.query.cityCode as string) || remediationForm.cityCode || 'xz';

    // 获取有效光路编码
    const validOpticalPaths = opticalPaths.value
      .filter(path => path.code.trim() !== '')
      .map(path => path.code.trim());

    const updateParams = {
      id: currentRemediationId.value,
      // 基本信息
      cityCode: remediationForm.cityCode.toUpperCase(),
      areaCode: remediationForm.cityCode.toLowerCase(),
      district: remediationForm.district,
      speciality: remediationForm.speciality,
      hazardSource: remediationForm.hazardSource,
      hazardType: remediationForm.hazardType,
      hazardLevel: remediationForm.hazardLevel,
      title: remediationForm.title,
      remediationDescription: remediationForm.remediationDescription,
      expectedCompletionDate: remediationForm.expectedCompletionDate?.format('YYYY-MM-DD'),
      actualCompletionDate: remediationForm.actualCompletionDate?.format('YYYY-MM-DD'),
      responsiblePerson: remediationForm.responsiblePerson,
      responsiblePersonContact: remediationForm.responsiblePersonContact,
      remediationPerson: remediationForm.remediationPerson,
      remediationPersonContact: remediationForm.remediationPersonContact,

      // 保护组信息
      protectionScenario: remediationForm.protectionScenario,
      customProtectionScenario: remediationForm.customProtectionScenario,
      deviceType: remediationForm.deviceType,
      customDeviceType: remediationForm.customDeviceType,
      selectedProtectionGroupId: remediationForm.selectedProtectionGroupId,
      opticalPathGroupCode: remediationForm.opticalPathGroupCode,
      opticalPathGroupName: remediationForm.opticalPathGroupName,

      // 生命线业务信息
      isLifeline: remediationForm.isLifeline,
      lifelineBusinessId: remediationForm.lifelineBusinessId,
      lifelineBusinessName: remediationForm.lifelineBusinessName,

      // 业务信息
      circuitCode: remediationForm.circuitCode,
      circuitName: remediationForm.circuitName,

      // 客户信息
      customerName: remediationForm.customerName,
      customerAccount: remediationForm.customerAccount,
      customerManager: remediationForm.customerManager,
      customerManagerDepartment: remediationForm.customerManagerDepartment,
      customerLevel: remediationForm.customerLevel,
      differentiatedServiceLevel: remediationForm.differentiatedServiceLevel,

      // 自行发现相关字段
      selfDiscoveredDescription: remediationForm.selfDiscoveredDescription,
      discoveredBy: remediationForm.discoveredBy,
      discoveredDate: remediationForm.discoveredDate?.format('YYYY-MM-DD'),

      // 光路信息
      opticalPaths: validOpticalPaths,

      // 系统信息
      creator: userStore.getUserInfo?.realName || userStore.getUserInfo?.username || '未知用户',

      // 状态变更
      status: newStatus,
      updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      shardingCode: `ds_bc_o3_${cityCode.toLowerCase()}`
    };

    console.log('更新整改数据参数:', updateParams);

    hazardRemediationService.info.value = updateParams;
    const response = await hazardRemediationService.doCreateNew('/api/hazard-remediation/update');

    if (!response || !response.success) {
      throw new Error(response?.message || '更新整改数据失败');
    }
  } catch (error) {
    console.error('更新整改数据失败:', error);
    throw error;
  }
};

// 创建草稿整改单
const createDraftRemediation = async () => {
  try {
    console.log('创建草稿整改单...');

    // 获取有效光路编码
    const validOpticalPaths = opticalPaths.value
      .filter(path => path.code.trim() !== '')
      .map(path => path.code.trim());

    // 构建草稿参数（包含所有字段）
    const draftParams = {
      // 基本信息
      cityCode: remediationForm.cityCode.toUpperCase(),
      areaCode: remediationForm.cityCode.toLowerCase(),
      district: remediationForm.district || '未指定',
      speciality: remediationForm.speciality || '数据',
      hazardSource: remediationForm.hazardSource || 'selfDiscovered',
      hazardType: remediationForm.hazardType || 'singleRoute',
      hazardLevel: remediationForm.hazardLevel || 'medium',
      title: remediationForm.title || '隐患整改草稿',

      // 必填字段：整改描述（数据库不允许为空）
      remediationDescription: remediationForm.remediationDescription || '待完善整改描述',

      // 责任人信息（必填）
      responsiblePerson: remediationForm.responsiblePerson || userStore.getUserInfo?.realName || '未指定',
      responsiblePersonContact: remediationForm.responsiblePersonContact || '',
      remediationPerson: remediationForm.remediationPerson || userStore.getUserInfo?.realName || '未指定',
      remediationPersonContact: remediationForm.remediationPersonContact || '',

      // 预计完成时间（必填）
      expectedCompletionDate: remediationForm.expectedCompletionDate ?
        remediationForm.expectedCompletionDate.format('YYYY-MM-DD') :
        dayjs().add(7, 'day').format('YYYY-MM-DD'),
      actualCompletionDate: remediationForm.actualCompletionDate?.format('YYYY-MM-DD'),

      // 保护组信息
      protectionScenario: remediationForm.protectionScenario,
      customProtectionScenario: remediationForm.customProtectionScenario,
      deviceType: remediationForm.deviceType,
      customDeviceType: remediationForm.customDeviceType,
      selectedProtectionGroupId: remediationForm.selectedProtectionGroupId,
      opticalPathGroupCode: remediationForm.opticalPathGroupCode,
      opticalPathGroupName: remediationForm.opticalPathGroupName,

      // 生命线业务信息
      isLifeline: remediationForm.isLifeline,
      lifelineBusinessId: remediationForm.lifelineBusinessId,
      lifelineBusinessName: remediationForm.lifelineBusinessName,

      // 业务信息
      circuitCode: remediationForm.circuitCode,
      circuitName: remediationForm.circuitName,

      // 客户信息
      customerName: remediationForm.customerName,
      customerAccount: remediationForm.customerAccount,
      customerManager: remediationForm.customerManager,
      customerManagerDepartment: remediationForm.customerManagerDepartment,
      customerLevel: remediationForm.customerLevel,
      differentiatedServiceLevel: remediationForm.differentiatedServiceLevel,

      // 自行发现相关字段
      selfDiscoveredDescription: remediationForm.selfDiscoveredDescription,
      discoveredBy: remediationForm.discoveredBy,
      discoveredDate: remediationForm.discoveredDate?.format('YYYY-MM-DD'),

      // 光路信息
      opticalPaths: validOpticalPaths,

      // 状态设置为草稿
      status: 'draft',

      // 系统信息
      creator: userStore.getUserInfo?.realName || userStore.getUserInfo?.username || '未知用户',
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),

      // 分片代码
      shardingCode: `ds_bc_o3_${remediationForm.cityCode.toLowerCase()}`
    };

    console.log('草稿参数:', draftParams);
    console.log('光路组编码:', draftParams.opticalPathGroupCode);
    console.log('光路组名称:', draftParams.opticalPathGroupName);
    console.log('客户经理姓名:', draftParams.customerManager);
    console.log('客户经理部门:', draftParams.customerManagerDepartment);
    console.log('客户等级:', draftParams.customerLevel);

    // 设置服务的info数据
    hazardRemediationService.info.value = draftParams;

    // 调用创建API
    const response = await hazardRemediationService.doCreateNew('/api/hazard-remediation/create');

    if (response && response.success && response.data && response.data.id) {
      currentRemediationId.value = response.data.id;
      console.log('草稿整改单创建成功，ID:', currentRemediationId.value);
      message.success('整改单已创建，可以开始上传文件');
      return response.data.id;
    } else {
      throw new Error(response?.message || '创建草稿失败');
    }
  } catch (error) {
    console.error('创建草稿整改单失败:', error);
    throw error;
  }
};

// 提交整改申请
const handleSubmitRemediation = async () => {
  try {
    submitting.value = true;

    // 验证表单
    if (!canSubmit.value) {
      message.error('请填写完整信息');
      return;
    }

    // 获取有效光路编码
    const validOpticalPaths = opticalPaths.value
      .filter(path => path.code.trim() !== '')
      .map(path => path.code.trim());

    // 判断是新建还是编辑
    const editId = route.query.id as string;
    const isEdit = !!editId;

    // 如果是新建模式且还没有整改单ID，先创建草稿
    if (!isEdit && !currentRemediationId.value) {
      console.log('新建模式：先创建草稿整改单');
      await createDraftRemediation();
    }

    // 构建提交参数
    const submitParams = {
      // 使用当前整改单ID（编辑模式用原ID，新建模式用草稿ID）
      id: isEdit ? editId : currentRemediationId.value,

      // 基本信息
      cityCode: remediationForm.cityCode.toUpperCase(),
      areaCode: remediationForm.cityCode.toLowerCase(),
      district: remediationForm.district,
      speciality: remediationForm.speciality,
      hazardSource: remediationForm.hazardSource,
      hazardType: remediationForm.hazardType,
      hazardLevel: remediationForm.hazardLevel,
      title: remediationForm.title || '隐患整改',
      remediationDescription: remediationForm.remediationDescription || '待完善整改描述',
      expectedCompletionDate: remediationForm.expectedCompletionDate?.format('YYYY-MM-DD') || dayjs().add(7, 'day').format('YYYY-MM-DD'),
      actualCompletionDate: remediationForm.actualCompletionDate?.format('YYYY-MM-DD'),
      responsiblePerson: remediationForm.responsiblePerson || userStore.getUserInfo?.realName || '未指定',
      responsiblePersonContact: remediationForm.responsiblePersonContact || '',
      remediationPerson: remediationForm.remediationPerson || userStore.getUserInfo?.realName || '未指定',
      remediationPersonContact: remediationForm.remediationPersonContact || '',

      // 保护组信息
      protectionScenario: remediationForm.protectionScenario,
      customProtectionScenario: remediationForm.customProtectionScenario,
      deviceType: remediationForm.deviceType,
      customDeviceType: remediationForm.customDeviceType,
      selectedProtectionGroupId: remediationForm.selectedProtectionGroupId,
      opticalPathGroupCode: remediationForm.opticalPathGroupCode,
      opticalPathGroupName: remediationForm.opticalPathGroupName,

      // 生命线业务信息
      isLifeline: remediationForm.isLifeline,
      lifelineBusinessId: remediationForm.lifelineBusinessId,
      lifelineBusinessName: remediationForm.lifelineBusinessName,

      // 业务信息
      circuitCode: remediationForm.circuitCode,
      circuitName: remediationForm.circuitName,

      // 客户信息
      customerName: remediationForm.customerName,
      customerAccount: remediationForm.customerAccount,
      customerManager: remediationForm.customerManager,
      customerManagerDepartment: remediationForm.customerManagerDepartment,
      customerLevel: remediationForm.customerLevel,
      differentiatedServiceLevel: remediationForm.differentiatedServiceLevel,

      // 自行发现相关字段
      selfDiscoveredDescription: remediationForm.selfDiscoveredDescription,
      discoveredBy: remediationForm.discoveredBy,
      discoveredDate: remediationForm.discoveredDate?.format('YYYY-MM-DD'),

      // 光路信息
      opticalPaths: validOpticalPaths,

      // 系统信息
      creator: userStore.getUserInfo?.realName || userStore.getUserInfo?.username || '未知用户',

      // 状态管理：从草稿进入正式整改流程
      status: 'inProgress',
      updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),

      // 分片代码（按照地市格式）
      shardingCode: `ds_bc_o3_${remediationForm.cityCode.toLowerCase()}`
    };

    console.log(`${isEdit ? '更新' : '创建'}整改参数:`, submitParams);
    console.log('shardingCode详细信息:', {
      cityCode: remediationForm.cityCode,
      lowerCase: remediationForm.cityCode.toLowerCase(),
      finalShardingCode: submitParams.shardingCode
    });

    // 设置服务的info数据
    hazardRemediationService.info.value = submitParams;

    // 因为整改单已经创建（编辑模式或新建草稿），统一使用更新API
    const apiUrl = '/api/hazard-remediation/update';
    const response = await hazardRemediationService.doCreateNew(apiUrl);

    console.log(`${isEdit ? '更新' : '创建'}响应:`, response);

    // 获取整改单ID
    const remediationId = response?.data?.id || route.query.id;

    // 即时上传模式下，文件已经在选择时上传，这里只需要检查是否有上传失败的文件
    const failedUploads = remediationForm.attachments.filter(file =>
      file.status === 'error' || (file.status === 'done' && !file.s3Key)
    );

    if (failedUploads.length > 0) {
      console.warn('存在上传失败的文件:', failedUploads);
      message.warning(`有 ${failedUploads.length} 个文件上传失败，请重新上传后再保存`);
      return;
    }

    message.success(`隐患整改${isEdit ? '更新' : '申请'}已${isEdit ? '保存' : '提交'}，状态已更新为整改中`);
    // 跳转回列表页面
    await closeCurrent();
    go('/nrm/res-app/risk-manage/hazard-remediation-list');

  } catch (error) {
    console.error(`${route.query.id ? '更新' : '创建'}整改失败:`, error);
    message.error(`${route.query.id ? '更新' : '创建'}整改失败: ` + (error.message || '未知错误'));
  } finally {
    submitting.value = false;
  }
};



// 初始化空表单数据
const initializeEmptyForm = () => {
  // 只设置必要的默认值
  remediationForm.cityCode = 'XZ'; // 默认徐州（大写）
  remediationForm.expectedCompletionDate = dayjs().add(7, 'day'); // 默认一周后完成
  remediationForm.isLifeline = '1'; // 默认非生命线业务

  // 初始化光路配置为空数组（用户需要手动添加光路）
  opticalPaths.value = [];

  console.log('空表单数据初始化完成');
};

// 保存风险检测结果到数据库
const saveDetectionResult = async (detectionResult: RiskDetectionResult, detectionType: string = 'initial') => {
  if (!route.query.id) return;

  try {
    const cityCode = route.query.cityCode as string || 'xz';
    const currentUser = userStore.getUserInfo?.realName || userStore.getUserInfo?.username || '未知用户';

    // 根据检测类型确定检测阶段描述
    let detectionPhase = '';
    switch (detectionType) {
      case 'initial':
        detectionPhase = '整改前检测';
        break;
      case 'inProgress':
        detectionPhase = '整改中检测';
        break;
      case 'completion':
        detectionPhase = '整改后检测';
        break;
      default:
        detectionPhase = '风险检测';
        break;
    }

    const saveParams = {
      remediationId: route.query.id,
      detectionType: detectionType, // initial, inProgress, completion
      detectionPhase: detectionPhase,
      checkResult: detectionResult.check_result,
      exceptionInfo: detectionResult.exception_info,
      riskLevel: determineRiskLevel(detectionResult.check_result),
      routeNum: detectionResult.route_num ? parseInt(detectionResult.route_num) : 0,
      opticalPathCount: validOpticalPathsForMap.value.length,
      detectionSnapshot: JSON.stringify(detectionResult),
      opticalPathsSnapshot: JSON.stringify(validOpticalPathsForMap.value),
      detector: currentUser,
      detectionStatus: 'completed',
      errorMessage: null, // 添加错误信息字段
      shardingCode: `ds_bc_o3_${cityCode.toLowerCase()}`
    };

    console.log('准备保存检测结果，参数:', saveParams);

    hazardRemediationService.info.value = saveParams;
    const response = await hazardRemediationService.doCreateNew(`/api/hazard-remediation/${route.query.id}/save-risk-detection`);

    if (response && response.success) {
      console.log('检测结果保存成功:', response);
    } else {
      console.warn('检测结果保存失败:', response?.message || '未知错误');
      console.warn('完整响应:', response);
    }
  } catch (error) {
    console.error('保存检测结果失败:', error);
    console.error('API路径:', `/api/hazard-remediation/${route.query.id}/save-risk-detection`);
  }
};

// 确定风险等级
const determineRiskLevel = (checkResult: string) => {
  if (!checkResult) return 'none';

  switch (checkResult.toLowerCase()) {
    case '无光路':
    case '单光路':
    case '单路由':
    case '缺管道':
    case '同管道':
    case '同光缆':
    case 'high_risk':
      return 'high';
    case 'medium_risk':
      return 'medium';
    case 'low_risk':
      return 'low';
    case '正常':
    case 'no_risk':
      return 'none';
    default:
      return 'medium';
  }
};



// 加载风险检测时间线
const loadDetectionTimeline = async () => {
  if (!route.query.id) return;

  try {
    const cityCode = route.query.cityCode as string || 'xz';
    const queryParams = {
      remediationId: route.query.id,
      shardingCode: `ds_bc_o3_${cityCode.toLowerCase()}`
    };

    // 调用后端API获取检测时间线
    hazardRemediationService.info.value = queryParams;
    const response = await hazardRemediationService.doCreateNew('/api/hazard-remediation/detection-timeline');

    if (response && response.success && response.data && Array.isArray(response.data)) {
      detectionTimeline.value = response.data.map((item: any) => {
        let eventData = null;

        // 解析 event_data 字段
        if (item.event_data) {
          try {
            eventData = typeof item.event_data === 'string' ? JSON.parse(item.event_data) : item.event_data;

            // 确保异常信息字段被正确映射
            if (eventData && !(eventData as any).exceptionInfo) {
              (eventData as any).exceptionInfo = (eventData as any).exception_info || item.exception_info || '';
            }
          } catch (error) {
            console.error('解析 event_data 失败:', error);
            eventData = null;
          }
        }

        return {
          id: item.id,
          timeline_type: item.timeline_type,
          event_description: item.event_description,
          event_time: item.event_time,
          operator: item.operator,
          event_data: eventData
        };
      });
      console.log('检测时间线加载成功，记录数量:', detectionTimeline.value.length);
    } else {
      // 如果没有检测记录或数据格式不正确，显示空数组
      console.log('检测时间线数据为空或格式不正确:', response);
      detectionTimeline.value = [];
    }
  } catch (error) {
    console.error('加载检测时间线失败:', error);
    // 出错时也显示空数组，不影响页面正常使用
    detectionTimeline.value = [];
  }
};

// 完成整改处理
const handleCompleteRemediation = async () => {
  try {
    // 显示完成整改弹窗
    showCompleteModal.value = true;

    // 重置完成表单
    completeForm.actualCompletionDate = dayjs();
    completeForm.completionNotes = '';
    completeForm.remainingIssues = '';
    completeForm.exemptionReason = '';

    // 默认启用风险检测
    enableFinalDetection.value = true;

    // 加载最新的时间线数据
    await loadDetectionTimeline();
  } catch (error) {
    console.error('打开完成整改弹窗失败:', error);
    message.error('操作失败: ' + (error.message || '未知错误'));
  }
};

// 检测选项变化处理
const onDetectionOptionChange = () => {
  if (!enableFinalDetection.value) {
    // 如果不执行检测，清空检测结果
    riskDetectionResult.value = null;
    // 清空遗留问题相关字段
    completeForm.remainingIssues = '';
    completeForm.exemptionReason = '';
  }
};

// 执行整改后风险检测
const performFinalRiskDetection = async () => {
  try {
    console.log('开始执行整改后风险检测');

    // 执行风险检测
    await performRiskDetectionWithType('completion');

    console.log('完成整改风险检测完成，最新检测结果:', riskDetectionResult.value);
  } catch (error: any) {
    console.error('完成整改风险检测失败:', error);
    message.error('风险检测失败: ' + (error.message || '未知错误'));
  }
};

// 带检测类型的风险检测方法
const performRiskDetectionWithType = async (detectionType: string = 'auto') => {
  if (!canPerformRiskDetection.value) {
    message.warning('请先配置光路编码');
    return;
  }

  try {
    riskDetectionLoading.value = true;

    // 获取有效的光路编码
    const validOpticalPaths = opticalPaths.value
      .filter(path => path.code.trim() !== '')
      .map(path => path.code.trim());

    console.log('开始风险检测，光路编码:', validOpticalPaths);

    // 构建风险检测参数 - 参考opt_road_group_management.vue中的riskAnalyze方法
    const riskParams = {
      ds: remediationForm.cityCode.toLowerCase(), // 地市代码，确保小写
      id: 'temp_group_' + Date.now(), // 临时光路组ID
      group_name: `隐患整改临时组_${remediationForm.title || '未命名'}`, // 光路组名称
      opticalPaths: validOpticalPaths, // 光路编码列表
      speciality: remediationForm.speciality || '数据', // 专业类型，使用表单中的专业
      checkType: detectionType === 'completion' ? 'completion_check' : 'hazard_remediation' // 检测类型
    };

    // 设置风险分析服务参数
    riskAnalyzeService.info.value = riskParams;

    createMessage.info('开始检测，稍等！');

    // 调用风险分析API - 使用正确的临时风险检测接口
    const response = await riskAnalyzeService.doCreateNew('/api/opt-group-api/risk-analyze-temp');

    console.log('风险检测结果:', response);

    if (response) {
      // 直接使用返回的数据作为检测结果
      riskDetectionResult.value = {
        check_result: response.check_result || '未知',
        exception_info: response.exception_info || '无异常信息',
        route_num: response.route_num,
        pipeSegments: response.pipeSegments,
        ...response
      };

      // 如果是编辑模式，保存检测结果到数据库
      if (isEdit.value && route.query.id && riskDetectionResult.value) {
        // 根据传入的检测类型或当前状态判断检测类型
        let finalDetectionType = detectionType;
        if (detectionType === 'auto') {
          finalDetectionType = remediationForm.status === 'inProgress' ? 'inProgress' : 'initial';
        }
        await saveDetectionResult(riskDetectionResult.value, finalDetectionType);
        // 重新加载时间线
        await loadDetectionTimeline();
      }

      // 只提示检测完成，不提示具体问题
      message.success('风险检测完成');
    } else {
      message.error('风险检测失败：未获取到检测结果');
    }

  } catch (error) {
    console.error('风险检测失败:', error);
    message.error('风险检测失败：' + (error?.message || '网络错误'));
  } finally {
    riskDetectionLoading.value = false;
  }
};

// 在弹窗中执行风险检测
// const performRiskDetection = async () => {
//   try {
//     riskDetectionLoading.value = true;

//     const validOpticalPaths = opticalPaths.value
//       .filter(path => path.code.trim() !== '')
//       .map(path => path.code.trim());

//     if (validOpticalPaths.length === 0) {
//       message.error('请先配置光路信息');
//       return;
//     }

//     // 构建风险检测参数
//     const detectionParams = {
//       ds: remediationForm.cityCode.toLowerCase(),
//       id: 'completion_check_' + Date.now(),
//       group_name: `完成整改检测_${remediationForm.title || '未命名'}`,
//       opticalPaths: validOpticalPaths,
//       speciality: remediationForm.speciality || '数据',
//       checkType: 'completion_check'
//     };

//     riskAnalyzeService.info.value = detectionParams;
//     const response = await riskAnalyzeService.doCreateNew('/api/opt-group-api/risk-analyze-temp');

//     if (response && response.success) {
//       riskDetectionResult.value = response.data;

//       // 保存检测结果
//       await saveDetectionResult(response.data, 'completion');

//       // 只提示检测完成，不提示具体问题
//       message.success('风险检测完成');
//     }
//   } catch (error) {
//     console.error('风险检测失败:', error);
//     message.error('风险检测失败');
//   } finally {
//     riskDetectionLoading.value = false;
//   }
// };

// 执行完成操作
const performCompletion = async () => {
  try {
    // 验证必填字段
    if (!completeForm.actualCompletionDate) {
      message.error('请选择实际完成时间');
      return;
    }

    // 如果启用了风险检测但还没有检测结果，提示用户先执行检测
    if (enableFinalDetection.value && !riskDetectionResult.value) {
      message.error('请先执行风险检测');
      return;
    }

    // 如果有风险问题但没有填写遗留问题描述，提示用户填写
    if (hasRiskIssues.value && !completeForm.remainingIssues.trim()) {
      message.error('检测到风险问题，请填写遗留问题描述');
      return;
    }

    completing.value = true;
    const cityCode = route.query.cityCode as string || remediationForm.cityCode || 'xz';
    const currentUser = userStore.getUserInfo?.realName || userStore.getUserInfo?.username || '系统用户';

    const updateParams = {
      id: route.query.id,
      status: 'completed',
      actualCompletionDate: completeForm.actualCompletionDate.format('YYYY-MM-DD'),
      completionDescription: completeForm.completionNotes || '',
      userRemainingIssues: completeForm.remainingIssues || '',
      exemptionReason: completeForm.exemptionReason || '',
      completionOperator: currentUser,
      completionTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      hasRemainingIssues: hasRiskIssues.value,
      shardingCode: `ds_bc_o3_${cityCode.toLowerCase()}`
    };

    console.log('完成整改参数:', updateParams);

    hazardRemediationService.info.value = updateParams;
    const response = await hazardRemediationService.doCreateNew('/api/hazard-remediation/update');

    if (response && response.success) {
      message.success('整改已完成');

      // 更新主表单的状态和实际完成时间
      remediationForm.status = 'completed';
      remediationForm.actualCompletionDate = completeForm.actualCompletionDate;

      showCompleteModal.value = false;
      completing.value = false;

      // 延迟一下再跳转，让用户看到成功提示
      setTimeout(async () => {
        // 关闭表单页面，回到列表
        await closeCurrent();
        go('/nrm/res-app/risk-manage/hazard-remediation-list');
      }, 1000);
    } else {
      message.error(response?.message || '完成整改失败');
      completing.value = false;
    }
  } catch (error) {
    console.error('执行完成操作失败:', error);
    message.error('完成整改失败: ' + (error.message || '未知错误'));
    completing.value = false;
  }
};

// 进入整改阶段（从草稿状态变更为整改中）
const handleEnterRemediationPhase = async () => {
  try {
    const cityCode = route.query.cityCode as string || 'xz';
    const updateParams = {
      id: route.query.id,
      status: 'inProgress',
      shardingCode: `ds_bc_o3_${cityCode.toLowerCase()}`
    };

    console.log('进入整改阶段，参数:', updateParams);

    hazardRemediationService.info.value = updateParams;
    const response = await hazardRemediationService.doCreateNew('/api/hazard-remediation/update-status');

    if (response && response.success) {
      message.success('已进入整改阶段');
      // 重新加载数据以更新状态显示
      await loadEditData();
    } else {
      message.error(response?.message || '状态更新失败');
    }
  } catch (error) {
    console.error('进入整改阶段失败:', error);
    message.error('状态更新失败: ' + (error.message || '未知错误'));
  }
};

// 导出整改报告（前端实现）
const handleExportReport = async () => {
  try {
    exporting.value = true;

    // 直接使用前端已有的数据，不调用后端API
    const remediationData = {
      id: remediationForm.id || route.query.id,
      title: remediationForm.title,
      status: remediationForm.status,
      cityCode: remediationForm.cityCode,
      district: remediationForm.district,
      speciality: remediationForm.speciality,
      hazardSource: remediationForm.hazardSource,
      hazardType: remediationForm.hazardType,
      hazardLevel: remediationForm.hazardLevel,
      responsiblePerson: remediationForm.responsiblePerson,
      responsiblePersonContact: remediationForm.responsiblePersonContact,
      remediationPerson: remediationForm.remediationPerson,
      remediationPersonContact: remediationForm.remediationPersonContact,
      expectedCompletionDate: remediationForm.expectedCompletionDate ? remediationForm.expectedCompletionDate.format('YYYY-MM-DD') : '',
      actualCompletionDate: remediationForm.actualCompletionDate ? remediationForm.actualCompletionDate.format('YYYY-MM-DD') : '',
      createTime: remediationForm.createTime,
      updateTime: remediationForm.updateTime,
      remediationDescription: remediationForm.remediationDescription,

      // 保护组信息
      protectionScenario: remediationForm.protectionScenario,
      customProtectionScenario: remediationForm.customProtectionScenario,
      deviceType: remediationForm.deviceType,
      customDeviceType: remediationForm.customDeviceType,
      selectedProtectionGroupId: remediationForm.selectedProtectionGroupId,

      // 光路信息
      opticalPathGroupCode: remediationForm.opticalPathGroupCode,
      opticalPathGroupName: remediationForm.opticalPathGroupName,

      // 生命线业务信息
      isLifeline: remediationForm.isLifeline,
      lifelineBusinessId: remediationForm.lifelineBusinessId,

      // 业务信息
      circuitCode: remediationForm.circuitCode,
      circuitName: remediationForm.circuitName,

      // 客户信息
      customerName: remediationForm.customerName,
      customerAccount: remediationForm.customerAccount,
      customerManager: remediationForm.customerManager,
      customerManagerDepartment: remediationForm.customerManagerDepartment,
      customerLevel: remediationForm.customerLevel,
      differentiatedServiceLevel: remediationForm.differentiatedServiceLevel,

      // 自行发现相关字段
      selfDiscoveredDescription: remediationForm.selfDiscoveredDescription,
      discoveredBy: remediationForm.discoveredBy,
      discoveredDate: remediationForm.discoveredDate ? remediationForm.discoveredDate.format('YYYY-MM-DD') : ''
    };

    // 使用前端已有的数据
    const timelineData = detectionTimeline.value || [];
    const attachmentData = remediationForm.attachments || [];

    console.log('导出数据:', { remediationData, timelineData, attachmentData });

    // 使用 Word 格式导出报告
    await generateWordReport(remediationData, timelineData, attachmentData);

    message.success('Word报告导出成功');
  } catch (error) {
    console.error('导出报告失败:', error);
    message.error('导出报告失败: ' + (error?.message || '网络错误'));
  } finally {
    exporting.value = false;
  }
};

// 生成Word报告
const generateWordReport = async (remediationData: any, timelineData: any[], attachmentData: any[]) => {
  try {
    // 创建Word文档内容
    const docContent = generateWordContent(remediationData, timelineData, attachmentData);

    // 创建Blob对象
    const blob = new Blob([docContent], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `隐患整改报告_${remediationData.id || '未知'}_${new Date().toISOString().slice(0, 10)}.docx`;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    window.URL.revokeObjectURL(url);

  } catch (error) {
    console.error('生成Word报告失败:', error);
    throw error;
  }
};

// 生成Word文档内容
const generateWordContent = (remediationData: any, timelineData: any[], attachmentData: any[]): string => {
  const content = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>隐患整改报告</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; }
        .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .subtitle { font-size: 14px; color: #666; }
        .section { margin-bottom: 25px; }
        .section-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; border-bottom: 2px solid #1890ff; padding-bottom: 5px; }
        .info-table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
        .info-table th, .info-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .info-table th { background-color: #f5f5f5; font-weight: bold; width: 150px; }
        .status-high { color: #f5222d; font-weight: bold; }
        .status-medium { color: #fa8c16; font-weight: bold; }
        .status-low { color: #52c41a; font-weight: bold; }
        .status-completed { color: #52c41a; font-weight: bold; }
        .status-processing { color: #fa8c16; font-weight: bold; }
        .list-item { margin-bottom: 8px; padding-left: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">隐患整改报告</div>
        <div class="subtitle">生成时间：${new Date().toLocaleString('zh-CN')}</div>
    </div>

    <div class="section">
        <div class="section-title">基本信息</div>
        <table class="info-table">
            <tr><th>整改单号</th><td>${remediationData.id || ''}</td></tr>
            <tr><th>状态</th><td class="status-${remediationData.status}">${getStatusText(remediationData.status)}</td></tr>
            <tr><th>标题</th><td>${remediationData.title || ''}</td></tr>
            <tr><th>地市</th><td>${getCityText(remediationData.cityCode) || ''}</td></tr>
            <tr><th>区县</th><td>${remediationData.district || ''}</td></tr>
            <tr><th>专业</th><td>${remediationData.speciality || ''}</td></tr>
            <tr><th>隐患来源</th><td>${getHazardSourceText(remediationData.hazardSource)}</td></tr>
            <tr><th>隐患类型</th><td>${getHazardTypeText(remediationData.hazardType)}</td></tr>
            <tr><th>隐患等级</th><td class="status-${remediationData.hazardLevel}">${getHazardLevelText(remediationData.hazardLevel)}</td></tr>
            <tr><th>整改描述</th><td>${remediationData.remediationDescription || ''}</td></tr>
        </table>
    </div>

    <div class="section">
        <div class="section-title">责任人信息</div>
        <table class="info-table">
            <tr><th>责任人</th><td>${remediationData.responsiblePerson || ''}</td></tr>
            <tr><th>责任人联系方式</th><td>${remediationData.responsiblePersonContact || ''}</td></tr>
            <tr><th>整改人</th><td>${remediationData.remediationPerson || ''}</td></tr>
            <tr><th>整改人联系方式</th><td>${remediationData.remediationPersonContact || ''}</td></tr>
        </table>
    </div>

    <div class="section">
        <div class="section-title">时间信息</div>
        <table class="info-table">
            <tr><th>创建时间</th><td>${remediationData.createTime || ''}</td></tr>
            <tr><th>更新时间</th><td>${remediationData.updateTime || ''}</td></tr>
            <tr><th>预计完成时间</th><td>${remediationData.expectedCompletionDate || '未设置'}</td></tr>
            <tr><th>实际完成时间</th><td>${remediationData.actualCompletionDate || '未完成'}</td></tr>
        </table>
    </div>

    <div class="section">
        <div class="section-title">光路信息</div>
        <table class="info-table">
            <tr><th>光路编码</th><td>${getOpticalPathsText()}</td></tr>
            <tr><th>光路组编码</th><td>${remediationData.opticalPathGroupCode || '-'}</td></tr>
            <tr><th>光路组名称</th><td>${remediationData.opticalPathGroupName || '-'}</td></tr>
        </table>
    </div>

    <div class="section">
        <div class="section-title">隐患来源详细信息</div>
        <table class="info-table">
            <tr><th>隐患来源</th><td>${getHazardSourceText(remediationData.hazardSource)}</td></tr>
            ${remediationData.hazardSource === 'selfDiscovered' ? `
            <tr><th>自行发现说明</th><td>${remediationData.selfDiscoveredDescription || '-'}</td></tr>
            <tr><th>发现人</th><td>${remediationData.discoveredBy || '-'}</td></tr>
            <tr><th>发现时间</th><td>${remediationData.discoveredDate || '-'}</td></tr>
            ` : ''}
            ${remediationData.hazardSource === 'protectionGroup' ? `
            <tr><th>保护组场景</th><td>${remediationData.protectionScenario || '-'}</td></tr>
            <tr><th>自定义保护组场景</th><td>${remediationData.customProtectionScenario || '-'}</td></tr>
            <tr><th>设备类型</th><td>${remediationData.deviceType || '-'}</td></tr>
            <tr><th>自定义设备类型</th><td>${remediationData.customDeviceType || '-'}</td></tr>
            ` : ''}
        </table>
    </div>

    <div class="section">
        <div class="section-title">客户信息</div>
        <table class="info-table">
            <tr><th>是否生命线业务</th><td>${remediationData.isLifeline === '0' ? '是' : remediationData.isLifeline === '1' ? '否' : '-'}</td></tr>
            <tr><th>生命线业务ID</th><td>${remediationData.lifelineBusinessId || '-'}</td></tr>
            <tr><th>电路编码</th><td>${remediationData.circuitCode || '-'}</td></tr>
            <tr><th>电路名称</th><td>${remediationData.circuitName || '-'}</td></tr>
            <tr><th>客户名称</th><td>${remediationData.customerName || '-'}</td></tr>
            <tr><th>客户账号</th><td>${remediationData.customerAccount || '-'}</td></tr>
            <tr><th>客户经理</th><td>${remediationData.customerManager || '-'}</td></tr>
            <tr><th>客户经理部门</th><td>${remediationData.customerManagerDepartment || '-'}</td></tr>
            <tr><th>客户等级</th><td>${remediationData.customerLevel || '-'}</td></tr>
            <tr><th>差异化服务等级</th><td>${remediationData.differentiatedServiceLevel || '-'}</td></tr>
        </table>
    </div>

    ${timelineData.length > 0 ? `
    <div class="section">
        <div class="section-title">检测历史记录</div>
        <table class="info-table">
            <tr>
                <th>检测时间</th>
                <th>检测类型</th>
                <th>检测结果</th>
                <th>异常信息</th>
                <th>风险等级</th>
                <th>检测人</th>
            </tr>
            ${timelineData.map(item => {
              const eventData = item.event_data ? (typeof item.event_data === 'string' ? JSON.parse(item.event_data) : item.event_data) : {};
              return `
              <tr>
                  <td>${formatDateTime(item.event_time) || '-'}</td>
                  <td>${item.timeline_type === 'detection' ? '风险检测' : item.timeline_type || '-'}</td>
                  <td>${eventData.checkResult || eventData.check_result || '-'}</td>
                  <td>${eventData.exceptionInfo || eventData.exception_info || '-'}</td>
                  <td class="status-${eventData.riskLevel || eventData.risk_level}">${eventData.riskLevel || eventData.risk_level || '-'}</td>
                  <td>${item.operator || '-'}</td>
              </tr>
              `;
            }).join('')}
        </table>
    </div>
    ` : ''}

    ${attachmentData.length > 0 ? `
    <div class="section">
        <div class="section-title">附件清单</div>
        <table class="info-table">
            <tr>
                <th>文件名</th>
                <th>文件大小</th>
                <th>文件类型</th>
                <th>上传人</th>
                <th>上传时间</th>
            </tr>
            ${attachmentData.map(item => `
            <tr>
                <td>${item.name || item.originalFilename || item.original_filename || '-'}</td>
                <td>${formatFileSize(item.size || item.fileSize || item.file_size) || '-'}</td>
                <td>${item.type || item.fileType || item.file_type || '-'}</td>
                <td>${item.uploader || item.upload_user || '-'}</td>
                <td>${formatDateTime(item.createTime || item.create_time || item.uploadTime || item.upload_time) || '-'}</td>
            </tr>
            `).join('')}
        </table>
    </div>
    ` : `
    <div class="section">
        <div class="section-title">附件清单</div>
        <div class="list-item">• 暂无附件</div>
    </div>
    `}

    <div class="section">
        <div class="section-title">报告说明</div>
        <div class="list-item">• 本报告由系统自动生成，记录了隐患整改的完整过程</div>
        <div class="list-item">• 包含基本信息、责任人信息、时间节点、检测记录和附件清单</div>
        <div class="list-item">• 如有疑问，请联系相关责任人或系统管理员</div>
    </div>
</body>
</html>
  `;

  return content;
};

// 格式化文件大小
const formatFileSize = (fileSize: number | null | undefined): string => {
  if (!fileSize || fileSize === 0) {
    return '0 B';
  }

  const units = ['B', 'KB', 'MB', 'GB'];
  let unitIndex = 0;
  let size = fileSize;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
};

// 获取隐患来源文本
const getHazardSourceText = (hazardSource: string): string => {
  const textMap: Record<string, string> = {
    'protectionGroup': '保护组',
    'lifelineBusiness': '生命线业务',
    'selfDiscovered': '自行发现'
  };
  return textMap[hazardSource] || hazardSource || '';
};

// 获取隐患类型文本
const getHazardTypeText = (hazardType: string): string => {
  const textMap: Record<string, string> = {
    'noOpticalPath': '无光路',
    'singleOpticalPath': '单光路',
    'singleRoute': '单路由',
    'missingPipeline': '缺管道',
    'samePipeline': '同管道',
    'sameOpticalCable': '同光缆',
    'intraOfficeOpticalPath': '局内光路',
    'sameOfficeWell': '同局前井'
  };
  return textMap[hazardType] || hazardType || '';
};

// 获取隐患等级文本
const getHazardLevelText = (hazardLevel: string): string => {
  const textMap: Record<string, string> = {
    'high': '高',
    'medium': '中',
    'low': '低'
  };
  return textMap[hazardLevel] || hazardLevel || '';
};

// 获取光路编码文本
const getOpticalPathsText = (): string => {
  const paths = opticalPaths.value
    .filter(path => path.code.trim() !== '')
    .map(path => path.code.trim());
  return paths.length > 0 ? paths.join(', ') : '-';
};



// 获取城市文本
const getCityText = (cityCode: string): string => {
  const cityMap: Record<string, string> = {
    'NJ': '南京', 'WX': '无锡', 'XZ': '徐州', 'CZ': '常州',
    'SZ': '苏州', 'NT': '南通', 'LYG': '连云港', 'HA': '淮安',
    'YC': '盐城', 'YZ': '扬州', 'ZJ': '镇江', 'TZ': '泰州', 'SQ': '宿迁'
  };
  return cityMap[cityCode?.toUpperCase()] || cityCode || '';
};



// 获取隐患等级颜色
const getHazardLevelColor = (level: string): string => {
  const colorMap: Record<string, string> = {
    'high': '#f5222d',
    'medium': '#fa8c16',
    'low': '#52c41a'
  };
  return colorMap[level] || '#d9d9d9';
};







// 获取编辑数据
const loadEditData = async () => {
  const editId = route.query.id as string;
  const pageMode = route.query.mode as string;

  console.log('=== 开始加载编辑数据 ===');
  console.log('编辑ID:', editId);
  console.log('页面模式:', pageMode);
  console.log('路由查询参数:', route.query);

  if (!editId) {
    console.log('没有编辑ID，跳过数据加载');
    return;
  }

  try {
    // 从路由参数获取地市代码，如果没有则使用默认值
    const cityCode = route.query.cityCode as string || 'xz';
    const queryParams = {
      id: editId,
      shardingCode: `ds_bc_o3_${cityCode.toLowerCase()}`
    };

    console.log('开始加载编辑数据，参数:', queryParams);
    hazardRemediationService.info.value = queryParams;
    const response = await hazardRemediationService.doCreateNew('/api/hazard-remediation/get');
    console.log('编辑数据加载响应:', response);

    if (response && response.data) {
      const data = response.data;
      console.log('开始填充表单数据，原始数据:', data);

      // 填充表单数据
      remediationForm.id = data.id || editId;
      remediationForm.status = data.status || 'draft';
      console.log('加载的整改单状态:', remediationForm.status);
      remediationForm.cityCode = data.city_code || 'xz';
      remediationForm.district = data.district || '';
      remediationForm.speciality = data.speciality || '数据';
      remediationForm.hazardSource = data.hazard_source || '';
      remediationForm.hazardType = data.hazard_type || '';
      remediationForm.hazardLevel = data.hazard_level || '';
      remediationForm.title = data.title || '';
      remediationForm.remediationDescription = data.remediation_description || '';
      remediationForm.expectedCompletionDate = data.expected_completion_date ? dayjs(data.expected_completion_date) : null;
      remediationForm.actualCompletionDate = data.actual_completion_date ? dayjs(data.actual_completion_date) : null;
      remediationForm.responsiblePerson = data.responsible_person || '';
      remediationForm.responsiblePersonContact = data.responsible_person_contact || '';
      remediationForm.remediationPerson = data.remediation_person || '';
      remediationForm.remediationPersonContact = data.remediation_person_contact || '';

      console.log('表单数据填充完成:', remediationForm);

      // 填充其他字段...
      remediationForm.isLifeline = data.is_lifeline || '';
      remediationForm.lifelineBusinessId = data.lifeline_business_id || '';
      remediationForm.lifelineBusinessName = data.lifeline_business_name || '';
      remediationForm.customerName = data.customer_name || '';
      remediationForm.customerAccount = data.customer_account || '';
      remediationForm.circuitCode = data.circuit_code || '';
      remediationForm.circuitName = data.circuit_name || '';

      // 保护组信息
      remediationForm.protectionScenario = data.protection_scenario || '';
      remediationForm.customProtectionScenario = data.custom_protection_scenario || '';
      remediationForm.deviceType = data.device_type || '';
      remediationForm.customDeviceType = data.custom_device_type || '';
      remediationForm.selectedProtectionGroupId = data.selected_protection_group_id || '';
      remediationForm.opticalPathGroupCode = data.optical_path_group_code || '';
      remediationForm.opticalPathGroupName = data.optical_path_group_name || '';

      // 客户信息
      remediationForm.customerManager = data.customer_manager || '';
      remediationForm.customerManagerDepartment = data.customer_manager_department || '';
      remediationForm.customerLevel = data.customer_level || '';
      remediationForm.differentiatedServiceLevel = data.differentiated_service_level || '';

      // 自行发现相关字段
      remediationForm.selfDiscoveredDescription = data.self_discovered_description || '';
      remediationForm.discoveredBy = data.discovered_by || '';
      remediationForm.discoveredDate = data.discovered_date ? dayjs(data.discovered_date) : null;

      // 添加调试信息
      console.log('=== 字段映射调试信息 ===');
      console.log('光路组编码 - 数据库值:', data.optical_path_group_code, '-> 表单值:', remediationForm.opticalPathGroupCode);
      console.log('光路组名称 - 数据库值:', data.optical_path_group_name, '-> 表单值:', remediationForm.opticalPathGroupName);
      console.log('客户经理姓名 - 数据库值:', data.customer_manager, '-> 表单值:', remediationForm.customerManager);
      console.log('客户经理部门 - 数据库值:', data.customer_manager_department, '-> 表单值:', remediationForm.customerManagerDepartment);
      console.log('客户等级 - 数据库值:', data.customer_level, '-> 表单值:', remediationForm.customerLevel);
      console.log('差异化服务等级 - 数据库值:', data.differentiated_service_level, '-> 表单值:', remediationForm.differentiatedServiceLevel);

      // 处理光路数据
      if (data.opticalPaths && Array.isArray(data.opticalPaths)) {
        opticalPaths.value = data.opticalPaths
          .filter((path: any) => {
            // 过滤空对象和无效光路
            if (!path || typeof path !== 'object') {
              return false;
            }
            const code = path.opticalPathCode || path.optical_path_code || '';
            return code && code.trim() !== '';
          })
          .map((path: any) => ({
            code: path.opticalPathCode || path.optical_path_code || '',
            saved: true, // 从数据库加载的光路标记为已保存
            saving: false // 初始化保存状态
          }));

        console.log('加载光路数据:', {
          原始数据: data.opticalPaths,
          过滤后数据: opticalPaths.value
        });
      }

      // 处理附件数据
      if (data.attachments && Array.isArray(data.attachments)) {
        remediationForm.attachments = data.attachments
          .filter((att: any) => {
            // 过滤空对象和无效附件
            if (!att || typeof att !== 'object') {
              return false;
            }
            // 检查是否有有效的文件名或S3Key
            const hasValidFilename = (att.originalFilename || att.original_filename) &&
                                   (att.originalFilename || att.original_filename).trim() !== '';
            const hasValidS3Key = (att.s3Key || att.s3_key) &&
                                 (att.s3Key || att.s3_key).trim() !== '';
            const hasValidId = att.id && att.id.toString().trim() !== '';

            return hasValidId && (hasValidFilename || hasValidS3Key);
          })
          .map((att: any) => ({
            uid: att.id,
            name: att.originalFilename || att.original_filename,
            status: 'done',
            url: att.s3Key ? `/api/file/download?s3Key=${encodeURIComponent(att.s3Key || att.s3_key)}` : '',
            s3Key: att.s3Key || att.s3_key,
            originalFilename: att.originalFilename || att.original_filename
          }));

        console.log('加载附件数据:', {
          原始数据: data.attachments,
          过滤后数据: remediationForm.attachments
        });
      }
    }
  } catch (error) {
    console.error('加载编辑数据失败:', error);
    message.error('加载数据失败: ' + (error?.message || '未知错误'));
  }
};



// 组件挂载
onMounted(async () => {
  console.log('隐患整改表单组件已挂载');

  // 初始化用户权限和地市信息
  initUserInfo();

  // 初始化页面模式
  if (route.query.id) {
    // 编辑模式：默认为查看模式
    pageMode.value = 'view';
  } else {
    // 新建模式：直接进入编辑模式
    pageMode.value = 'edit';
  }

  // 设置默认责任人为当前用户
  const currentUser = userStore.getUserInfo?.realName || userStore.getUserInfo?.username || '';
  if (currentUser && !currentUser.includes('测试')) {
    remediationForm.responsiblePerson = currentUser;
  }

  // 如果是新建，设置创建时间
  if (!remediationForm.createTime) {
    const now = new Date();
    remediationForm.createTime = now.toLocaleString('zh-CN');
    remediationForm.updateTime = now.toLocaleString('zh-CN');
  }

  // 加载生命线业务列表
  await loadLifelineBusinessList();

  // 如果是编辑模式，加载数据
  if (route.query.id) {
    currentRemediationId.value = route.query.id as string;
    await loadEditData();
    await loadDetectionTimeline();
  } else {
    // 初始化空表单，不立即创建草稿
    initializeEmptyForm();
    // 注意：不在这里创建草稿，而是在用户保存或发起时创建
  }
});
</script>

<style lang="less" scoped>
.hazard-remediation-container {
  height:auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.06) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.15),
      0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #666;
    margin: 0;
  }
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}



.form-section {
  flex: 1;

  .form-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: 100%;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.12),
        0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .ant-card-head {
      background: transparent;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);

      .ant-card-head-title {
        font-weight: 600;
        font-size: 16px;
        color: #1a1a1a;
      }
    }

    .ant-card-body {
      padding: 24px;
    }
  }
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;

  &.full-width {
    grid-column: 1 / -1;
  }

  .form-label {
    font-weight: 500;
    color: #333;
    font-size: 14px;

    &.required::before {
      content: '*';
      color: #ff4d4f;
      margin-right: 4px;
    }
  }

  .ant-input,
  .ant-select,
  .ant-date-picker,
  .ant-input-number {
    width: 100%;
  }
}

.optical-paths-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.optical-paths-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.optical-path-item {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;

  .path-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .path-label {
      font-weight: 500;
      color: #333;
    }

    .path-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .save-button {
        padding: 4px 8px;
        height: auto;
        border: 1px solid #52c41a;
        border-radius: 4px;
        background: #f6ffed;

        &:hover {
          background: #d9f7be;
          border-color: #389e0d;
        }
      }

      .saved-indicator {
        display: inline-flex;
        align-items: center;
        padding: 2px 6px;
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 4px;
        font-size: 12px;
        color: #52c41a;
      }
    }
  }

  .path-input-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

.query-actions {
  margin-top: 16px;
}

.map-section {
  .map-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }
}

.map-container {
  min-height: 400px;
  position: relative;
}

.map-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;

  p {
    margin-top: 16px;
    margin-bottom: 0;
  }
}

.map-content {
  height: 400px;
  border-radius: 6px;
  overflow: hidden;
}

// 优化的按钮区域样式
.action-buttons-section {
  padding: 32px 0;
  display: flex;
  justify-content: center;

  .button-container {
    .button-group {
      display: flex;
      gap: 16px;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;

      .action-btn {
        height: 48px;
        padding: 0 24px;
        border-radius: 12px;
        font-size: 15px;
        font-weight: 600;
        border: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        min-width: 120px;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover::before {
          left: 100%;
        }

        .anticon {
          font-size: 16px;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        &:active {
          transform: translateY(0);
        }

        // 主要按钮样式 - 清新蓝色
        &.primary-btn {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          color: white;
          box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);

          &:hover {
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
          }

          &:disabled {
            background: #e5e7eb;
            color: rgba(0, 0, 0, 0.25);
            box-shadow: none;
            transform: none;
          }
        }

        // 次要按钮样式 - 简洁白色
        &.secondary-btn {
          background: rgba(255, 255, 255, 0.95);
          color: #6b7280;
          border: 1px solid rgba(209, 213, 219, 0.8);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

          &:hover {
            background: rgba(255, 255, 255, 1);
            color: #374151;
            border-color: rgba(156, 163, 175, 0.8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }

        // 草稿按钮样式 - 温和橙色
        &.draft-btn {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          color: white;
          box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);

          &:hover {
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.5);
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
          }
        }

        // 成功按钮样式 - 清新绿色
        &.success-btn {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          color: white;
          box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);

          &:hover {
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.5);
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
          }
        }

        // 信息按钮样式 - 优雅紫色
        &.info-btn {
          background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
          color: white;
          box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);

          &:hover {
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.5);
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
          }
        }
      }

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 12px;

        .action-btn {
          width: 100%;
          max-width: 280px;
        }
      }
    }
  }
}

// 状态卡片样式
.status-card {
  margin-bottom: 24px;
  border: 2px solid #e6f7ff;
  border-radius: 8px;

  .ant-card-body {
    padding: 20px;
  }
}

.status-container {
  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .status-title {
      margin: 0;
      color: #1890ff;
      font-size: 18px;
      font-weight: 600;
    }

    .status-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-label {
        font-size: 14px;
        color: #666;
      }

      .status-tag {
        font-size: 14px;
        font-weight: 600;
        padding: 4px 12px;
        border-radius: 16px;
      }
    }
  }

  .status-timeline {
    margin-bottom: 20px;

    .ant-steps {
      .ant-steps-item-title {
        font-size: 14px;
        font-weight: 600;
      }

      .ant-steps-item-description {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .status-details {
    display: flex;
    gap: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .status-detail-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .detail-label {
        font-size: 14px;
        color: #666;
        white-space: nowrap;
      }

      .detail-value {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 12px;
    }
  }
}

// 优化的文件上传状态样式
.upload-status-container {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;

  .staged-files-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 20px;
    color: white;
    font-size: 13px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    animation: pulse 2s infinite;

    .staged-count {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 700;
    }

    .staged-text {
      white-space: nowrap;
    }
  }

  .upload-formats-tip {
    color: #666;
    font-size: 12px;
    opacity: 0.8;
    font-style: italic;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.5);
  }
  100% {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }
}

.upload-status-tip {
  margin-top: 8px;

  .ant-alert {
    border-radius: 6px;

    .ant-alert-message {
      font-weight: 500;
    }

    .ant-alert-description {
      margin-top: 4px;
      font-size: 12px;
    }
  }
}

// 隐患来源样式
.hazard-source-container {
  text-align: center;
  padding: 20px 0;
}

.hazard-source-item {
  .hazard-source-label {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #1890ff !important;
    display: block;
    margin-bottom: 16px;
    text-align: center;

    &::before {
      font-size: 18px;
    }
  }

  .hazard-source-select {
    border: 2px solid #1890ff;
    border-radius: 8px;
    font-size: 16px;
    min-width: 300px;

    &:hover {
      border-color: #40a9ff;
    }

    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
    }

    .ant-select-selector {
      height: 48px !important;
      line-height: 48px !important;
      font-size: 16px;
    }

    .ant-select-selection-item {
      line-height: 48px !important;
    }
  }
}

// 时间线样式
.timeline-content {
  .timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .timeline-title {
      font-weight: 600;
      color: #333;
    }

    .timeline-time {
      color: #666;
      font-size: 12px;
    }
  }

  .timeline-details {
    margin-bottom: 8px;

    .detail-text {
      margin-left: 8px;
      color: #666;
      font-size: 12px;

      &.exception-info {
        color: #fa8c16;
        font-weight: 500;
        margin-right: 12px;
        padding: 2px 6px;
        background: #fff7e6;
        border-radius: 4px;
        border: 1px solid #ffd591;
      }
    }
  }

  .timeline-operator {
    color: #999;
    font-size: 12px;
  }
}

// 优化的状态操作区域样式
.status-action-section {
  margin-top: 24px;

  .status-operations-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.12),
        0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .status-card-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .status-tag {
        font-weight: 600;
        border-radius: 12px;
        padding: 6px 16px;
        font-size: 13px;
        border: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .status-operations-content {
      .operation-section {
        .operation-buttons {
          display: flex;
          justify-content: center;
          padding: 8px 0;

          .primary-action-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border: none;
            min-width: 200px;
            height: 52px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            color: white;
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
              transition: left 0.5s;
            }

            &:hover {
              transform: translateY(-3px);
              box-shadow: 0 10px 30px rgba(59, 130, 246, 0.6);
              background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            }

            &:hover::before {
              left: 100%;
            }

            .anticon {
              font-size: 18px;
            }
          }

          .success-action-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: none;
            min-width: 200px;
            height: 52px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            color: white;
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
              transition: left 0.5s;
            }

            &:hover {
              transform: translateY(-3px);
              box-shadow: 0 10px 30px rgba(16, 185, 129, 0.6);
              background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }

            &:hover::before {
              left: 100%;
            }

            .anticon {
              font-size: 18px;
            }
          }
        }

        &.completed-section {
          padding: 24px 0;

          .completion-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 16px 24px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 12px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);

            .completion-icon {
              font-size: 20px;
              color: white;
            }

            .completion-text {
              font-size: 16px;
            }
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .status-operations-card {
      .status-operations-content {
        .operation-section {
          .operation-buttons {
            .primary-action-btn,
            .success-action-btn {
              width: 100%;
              max-width: 300px;
              min-width: auto;
            }
          }
        }
      }
    }
  }
}

// 禁用字段样式优化
.ant-input[disabled],
.ant-select[disabled] .ant-select-selector,
.ant-textarea[disabled] {
  background-color: #f8f9fa !important;
  border-color: #e9ecef !important;
  color: #495057 !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.ant-input[disabled]::placeholder,
.ant-textarea[disabled]::placeholder {
  color: #6c757d !important;
}

.ant-select-disabled .ant-select-selection-item {
  color: #495057 !important;
}

// 响应式调整
@media (max-width: 768px) {
  .hazard-remediation-container {
    padding: 16px;
  }

  .form-row {
    flex-direction: column;
    gap: 8px;

    .form-label {
      min-width: auto;
      line-height: 1.5;
    }
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
}

/* 保护组弹窗样式 */
.protection-groups-content {
  .protection-group-card {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .group-info {
      .group-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        border-top: 1px solid #f0f0f0;
        padding-top: 12px;
      }
    }
  }
}
</style>
