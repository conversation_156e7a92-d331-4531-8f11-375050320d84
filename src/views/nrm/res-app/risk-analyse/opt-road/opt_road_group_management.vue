<template>
  <page-wrapper>
    <!-- 重新设计的搜索区域 -->
    <div class="search-container">
      <!-- 筛选条件区域 -->
      <div class="filter-section">
        <!-- 常用筛选 -->
        <a-row :gutter="[24, 16]">
          <!-- 第一行：区域筛选 + 状态筛选 -->
          <a-col :span="12">
            <div class="filter-group">
              <span class="filter-label">区域筛选：</span>
              <a-space size="small">
                <a-select
                  v-model:value="filter.ds"
                  :filterOption="
                    (input: string, option: any) => {
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }
                  "
                  style="width: 140px"
                  show-search
                  placeholder="地市"
                  class="filter-select"
                  :disabled="cityUnchangable"
                  @change="handleCityChange"
                >
                  <a-select-option
                    v-for="item in dictionary.cityList"
                    :key="item.name"
                    :value="item.name"
                    :label="item.name"
                    >{{ item.name }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="filter.area_name"
                  :filterOption="
                    (input: string, option: any) => {
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }
                  "
                  style="width: 140px"
                  show-search
                  placeholder="区县"
                  class="filter-select"
                  :allowClear="true"
                  @change="handleFilterChange"
                >
                  <a-select-option
                    v-for="item in dictionary.leafRegionList"
                    :key="item.name"
                    :value="item.name"
                    :label="item.name"
                    >{{ item.name }}
                  </a-select-option>
                </a-select>

              </a-space>
            </div>
          </a-col>

          <a-col :span="12">
            <div class="filter-group">
              <span class="filter-label">状态筛选：</span>
              <a-space size="small">
                <a-select
                  v-model:value="filter.result"
                  style="width: 140px"
                  show-search
                  placeholder="检测结果"
                  class="filter-select"
                  allow-clear
                  @change="query()"
                >
                  <a-select-option
                    v-for="item in dictionary.StateList"
                    :key="item"
                    :value="item"
                    :label="item"
                    >{{ item }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model:value="filter.deal_result"
                  style="width: 140px"
                  show-search
                  placeholder="处理结果"
                  class="filter-select"
                  allow-clear
                  @change="query()"
                >
                  <a-select-option :key="'待整改'" :value="'待整改'" label="待整改"
                    >待整改</a-select-option
                  >
                  <a-select-option :key="'已整改'" :value="'已整改'" label="已整改"
                    >已整改</a-select-option
                  >
                  <a-select-option :key="'忽略异常'" :value="'忽略异常'" label="忽略异常"
                  >已忽略</a-select-option
                  >
                </a-select>
                <a-range-picker
                  v-model:value="create_date_range"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :placeholder="['开始日期', '结束日期']"
                  style="width: 200px"
                  class="filter-input"
                />
              </a-space>
            </div>
          </a-col>
          <!-- 第二行：来源筛选 + 详细筛选 -->
          <a-col :span="12">
            <div class="filter-group">
              <span class="filter-label">来源筛选：</span>
              <a-space size="small">
                <a-input
                  v-model:value="filter.create_op"
                  @blur="filter.create_op = filter.create_op?.trim() || ''"
                  placeholder="创建人"
                  style="width: 140px"
                  class="filter-input"
                />
                <a-select
                  v-model:value="filter.source_type_id"
                  style="width: 180px"
                  show-search
                  placeholder="数据来源"
                  class="filter-select"
                  allow-clear
                  @change="query()"
                >
                  <a-select-option :key="0" :value="0" label="人工整理">人工整理</a-select-option>
                  <a-select-option :key="1" :value="1" label="接入设备安全保护"
                    >接入设备安全保护</a-select-option
                  >
                  <a-select-option :key="2" :value="2" label="组网安全保护"
                    >组网安全保护</a-select-option
                  >
                  <a-select-option :key="10" :value="10" label="OLT上联">OLT上联</a-select-option>
                </a-select>
              </a-space>
            </div>
          </a-col>

          <a-col :span="12">
            <div class="filter-group">
              <span class="filter-label">详细筛选：</span>
              <a-space size="small">
                <a-select
                  v-model:value="filter.speciality"
                  style="width: 120px"
                  show-search
                  placeholder="专业"
                  class="filter-select"
                  :allowClear="true"
                  @change="handleFilterChange"
                >
                  <a-select-option
                    v-for="item in dictionary.speciality"
                    :key="item.speciality"
                    :value="item.speciality"
                    :label="item.speciality"
                    >{{ item.speciality }}
                  </a-select-option>
                </a-select>
                <a-input
                  v-model:value="filter.opt_code"
                  @blur="filter.opt_code = filter.opt_code?.trim() || ''"
                  placeholder="光路编码"
                  style="width: 140px"
                  class="filter-input"
                />
                <a-input
                  v-model:value="filter.name"
                  @blur="filter.name = filter.name?.trim() || ''"
                  placeholder="光路组名称"
                  style="width: 140px"
                  class="filter-input"
                />
                <a-input
                  v-model:value="filter.code"
                  @blur="filter.code = filter.code?.trim() || ''"
                  placeholder="光路组编码"
                  style="width: 140px"
                  class="filter-input"
                />
              </a-space>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <div class="action-buttons">
          <a-space size="middle">
            <a-button type="primary" @click="query()" :loading="queryLoading" class="action-btn primary-btn">
              <SearchOutlined />
              查询
            </a-button>
            <a-button @click="showNewDialog()" class="action-btn secondary-btn">
              <PlusOutlined />
              新增光路组
            </a-button>
            <a-button @click="showUploadDialog()" class="action-btn secondary-btn">
              <UploadOutlined />
              导入光路组
            </a-button>
            <a-button @click="check_analyze" :loading="etlLoading" class="action-btn warning-btn">
              <ExperimentOutlined />
              一键检测
              <span v-if="etlLoading" class="loading-text">请耐心等待</span>
            </a-button>
            <a-button @click="handleExport" :loading="exportLoading" class="action-btn secondary-btn">
              <DownloadOutlined />
              导出
            </a-button>

            <a-button @click="goToOperationLog" class="action-btn secondary-btn">
              <FileTextOutlined />
              操作日志
            </a-button>
            <a-button @click="goToLogAnalytics" class="action-btn secondary-btn">
              <BarChartOutlined />
              日志分析
            </a-button>
            <a-button @click="goToStatistics" class="action-btn secondary-btn">
              <ExperimentOutlined />
              统计分析
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 上传对话框 -->
    <uploadDialog
      :filter="filter"
      v-model:show="uploadDialogVisible"
      @on-finish="queryService.pageQuery"
    />
    <!-- <a-card>
      <div
        class="staticBox"
        v-if="dictionary.group_static && dictionary.group_static.length > 0"
        @click="staticDetailShow = !staticDetailShow"
      >
        <InfoCircleFilled style="margin-right: 0; margin-left: 10px" />
        <span class="stcTitle">统计：</span>
        <span>总量 {{ dictionary.group_static[0].total_count || 0 }}</span>
        <span style="color: red">同管道 {{ dictionary.group_static[0].samepiple_count || 0 }}</span>
        <span style="color: red">同光缆 {{ dictionary.group_static[0].samecable_count || 0 }}</span>
        <span style="color: red"
          >单光路 {{ dictionary.group_static[0].singlefiber_count || 0 }}</span
        >
        <span style="color: red"
          >单路由 {{ dictionary.group_static[0].singleroute_count || 0 }}</span
        >
        <span style="color: red">无光路 {{ dictionary.group_static[0].nonefiber_count || 0 }}</span>
        <span style="color: red">缺管道 {{ dictionary.group_static[0].lackpiple_count || 0 }}</span>
        <span style="color: darkorange"
          >同局前井 {{ dictionary.group_static[0].samejqj_count || 0 }}</span
        >
        <span style="color: darkgreen"
          >局内光路 {{ dictionary.group_static[0].jnfiber_count || 0 }}</span
        >
        <span style="color: darkgreen">正常 {{ dictionary.group_static[0].normal }}</span>
      </div>
      <div class="staticBox" v-else @click="staticDetailShow = !staticDetailShow">
        <InfoCircleFilled style="margin-right: 0; margin-left: 10px" />
        <span class="stcTitle">统计：</span>
        <span>总量 0</span>
        <span>同管道 0</span>
        <span>同光缆 0</span>
        <span>单光路 0</span>
        <span>单路由 0</span>
        <span>无光路 0</span>
        <span>缺管道 0</span>
        <span>同局前井 0</span>
        <span>局内光路 0</span>
        <span>正常 0</span>
      </div>
      <a-table
        v-if="staticDetailShow"
        :dataSource="dictionary.group_static"
        :columns="columns_opt_static"
        style="background-color: #ecf4ff"
        :pagination="false"
      />
      <a-button v-if="staticDetailShow" @click="exportExcel" class="btnSearch">导出总览</a-button>
    </a-card> -->
    <a-card>
      <a-table
        class="tableBox"
        :dataSource="dataSource"
        :columns="columns"
         size="small"
        :pagination="queryService.pagination"
        @change="queryService.pageChange"
        :loading="loading"
        :expand-column-width="50"
        :row-key="(record) => record.id"
        :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
        bordered
        :scroll="{ y: 500 }"
        :expandedRowKeys="expandedRowKeys"
        @expand="handleExpand"
      >
        <template #bodyCell="{ column, record }">
          <template
            v-if="
              column.key !== 'result' &&
              column.key !== 'action' &&
              column.key !== 'checkTime' &&
              column.key !== 'createDate' &&
              column.key !== 'checkResult' &&
              column.key !== 'previousCheckResult'
            "
          >
            <a-tooltip :title="record[column.key]" placement="topLeft">
              {{ record[column.key] }}
            </a-tooltip>
          </template>
          <!-- <template v-if="column.key === 'actionStart'">
            <CaretRightOutlined
              :style="{ fontSize: '16px', color: '#2F6CED' }"
              @click="handleRowExpand(record)"
            />
          </template> -->
          <template v-if="column.key === 'checkTime' && record.checkTime != null">
            {{ formatToDateTime(record.checkTime) }}
          </template>
          <template v-if="column.key === 'createDate' && record.createDate != null">
            {{ formatToDateTime(record.createDate) }}
          </template>

          <!-- 检测结果显示 -->
          <template v-if="column.key === 'checkResult'">
            <div class="result-container">
              <a-tooltip :title="record.checkResult" placement="topLeft">
                <a-tag
                  :class="getResultTagClass(record.checkResult)"
                  class="result-tag"
                >
                  {{ cleanResultText(record.checkResult) }}
                </a-tag>
              </a-tooltip>
            </div>
          </template>

          <!-- 上次检测结果显示 -->
          <template v-if="column.key === 'previousCheckResult'">
            <div class="result-container">
              <a-tooltip :title="record.previousCheckResult" placement="topLeft">
                <a-tag
                  v-if="record.previousCheckResult"
                  :class="getResultTagClass(record.previousCheckResult)"
                  class="result-tag"
                >
                  {{ cleanResultText(record.previousCheckResult) }}
                </a-tag>
                <span v-else class="no-data">-</span>
              </a-tooltip>
            </div>
          </template>

          <template v-if="column.key === 'action'">
            <!-- 主要操作按钮（放外面的，都用两个字） -->
            <a-button
              type="link"
              class="btnSty"
              @click="
                () => {
                  let ds = filter.ds;
                  // 传递 objectCode 和 objectName 参数到详情页面
                  go({
                    path: './risk-show-opt-road/' + record.id + '_' + ds,
                    query: {
                      objectCode: record.code,
                      objectName: record.name
                    }
                  });
                }
              "
            >
              <EyeOutlined />
              详情
            </a-button>

            <!-- 风险检测按钮移到外面，简化为"检测" -->
            <a-button
              type="link"
              class="btnSty"
              @click="riskAnalyze(record)"
            >
              <BugOutlined />
              检测
            </a-button>

            <a-button
              v-if="record.dealResult && record.dealResult.includes('待整改')"
              type="link"
              class="btnSty"
              :loading="ignorRisk_infoService.loading.value"
              @click="ignoreRisk(record)"
            >
              <ExclamationCircleOutlined />
              忽略
            </a-button>

            <a-button
              v-if="record.dealResult && record.dealResult.includes('忽略')"
              type="link"
              class="btnSty"
              :loading="ignorRisk_infoService.loading.value"
              @click="restorRisk(record)"
            >
              <UndoOutlined />
              恢复
            </a-button>

            <!-- 更多操作下拉菜单（都用四个字） -->
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <!-- 查看操作 -->
                  <a-menu-item key="detail" @click="showDetailDrawer(record)">
                    <InfoCircleOutlined />
                    更多信息
                  </a-menu-item>

                  <a-menu-divider />

                  <!-- 编辑操作 -->
                  <a-menu-item key="edit" @click="showEditDialog(record)">
                    <EditOutlined />
                    修改信息
                  </a-menu-item>

                  <a-menu-item key="add" @click="saveOpt(record)">
                    <PlusOutlined />
                    增加光路
                  </a-menu-item>

                  <a-menu-divider />

                  <!-- 危险操作 -->
                  <a-menu-item key="delete" @click="deleteRecord(record)" class="danger-item">
                    <DeleteOutlined />
                    删除记录
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" class="btnSty">
                <MoreOutlined />
                更多
              </a-button>
            </a-dropdown>
          </template>
        </template>

        <!-- 子表格模板 -->
        <template #expandedRowRender="{ record }">
          <a-table
            :columns="innerColumns"
            :data-source="record.members || []"
            :pagination="false"
            size="small"
            :scroll="{ x: 700, y: 300 }"
          >
            <template #bodyCell="{ column, record: innerRecord }">
              <template v-if="column.key !== 'action'">
                <a-tooltip :title="innerRecord[column.key]" placement="topLeft">
                  {{ innerRecord[column.key] }}
                </a-tooltip>
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="link" class="btnSty" @click="handUpdateOpt(innerRecord)">修改</a-button>
                <a-button type="link" class="btnSty" style="color:#F31818" @click="handleDeleteOpt(innerRecord)">删除</a-button>
              </template>
            </template>
          </a-table>
        </template>
      </a-table>
      <NoteDialog
        :info="current"
        :filter="filter"
        v-model:show="NoteDialogVisible"
        @on-finish="queryService.pageQuery"
      />
      <EditDialog
        :dictionary="dictionary"
        :info="current"
        :filter="filter"
        v-model:show="showEditVisible"
        @on-finish="queryService.pageQuery"
      />
      <ElementEditDialog
        :info="current"
        :filter="filter"
        v-model:show="showElementEditVisible"
        @on-finish="onFinish(current)"
      />
    </a-card>

    <!-- 详细信息抽屉 -->
    <a-drawer
      v-model:visible="detailDrawerVisible"
      title="光路保护组详细信息"
      placement="right"
      :width="600"
      :closable="true"
      :mask-closable="true"
      class="detail-drawer"
    >
      <div class="detail-content" v-if="currentDetailRecord">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <InfoCircleOutlined />
            基本信息
          </h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">保护组ID：</span>
              <span class="value">{{ currentDetailRecord.id || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">保护组编码：</span>
              <span class="value">{{ currentDetailRecord.code || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">保护组名称：</span>
              <span class="value">{{ currentDetailRecord.name || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">专业类型：</span>
              <span class="value">{{ currentDetailRecord.speciality || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">区域名称：</span>
              <span class="value">{{ currentDetailRecord.areaName || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">区县：</span>
              <span class="value">{{ currentDetailRecord.leafRegion || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">状态：</span>
              <span class="value">{{ currentDetailRecord.state || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">来源类型：</span>
              <span class="value">{{ currentDetailRecord.sourceType || '-' }}</span>
            </div>

            <div class="detail-item">
              <span class="label">拓扑</span>
              <span class="value">{{ currentDetailRecord.topo || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">资源类型</span>
              <span class="value">{{ currentDetailRecord.resType || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 检测结果信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <BugOutlined />
            检测结果
          </h3>
          <div class="detail-grid">
            <div class="detail-item full-width">
              <span class="label">检测结果：</span>
              <span class="value" :class="getResultClass(currentDetailRecord.result)">
                {{ currentDetailRecord.result || '-' }}
              </span>
            </div>

    
            <div class="detail-item full-width">
              <span class="label">处理结果：</span>
              <span class="value">{{ currentDetailRecord.dealResult || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">检测时间：</span>
              <span class="value">{{ formatToDateTime(currentDetailRecord.checkTime) || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">处理时间：</span>
              <span class="value">{{ formatToDateTime(currentDetailRecord.dealTime) || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">确认状态：</span>
              <span class="value">{{ currentDetailRecord.confirmStatus || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">确认人员：</span>
              <span class="value">{{ currentDetailRecord.confirmOp || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">确认时间：</span>
              <span class="value">{{ formatToDateTime(currentDetailRecord.confirmTime) || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">路由数量：</span>
              <span class="value">{{ currentDetailRecord.routeNum || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 创建信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <UserOutlined />
            创建信息
          </h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">创建人员：</span>
              <span class="value">{{ currentDetailRecord.createOp || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatToDateTime(currentDetailRecord.createDate) || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">维护人员：</span>
              <span class="value">{{ currentDetailRecord.maintainers || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">备注信息：</span>
              <span class="value">{{ currentDetailRecord.note || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 光路成员信息 -->
        <div class="detail-section" v-if="currentDetailRecord.members && currentDetailRecord.members.length > 0">
          <h3 class="section-title">
            <TeamOutlined />
            光路成员 ({{ currentDetailRecord.members.length }}条)
          </h3>
          <div class="members-list">
            <div
              v-for="(member, index) in currentDetailRecord.members"
              :key="member.id || index"
              class="member-item"
            >
              <div class="member-header">
                <span class="member-index">{{ index + 1 }}</span>
                <span class="member-code">{{ member.code }}</span>
                <span class="member-route">路由号: {{ member.route_no || '-' }}</span>
              </div>
              <div class="member-details">
                <div class="member-detail">
                  <span class="label">名称：</span>
                  <span class="value">{{ member.name || '-' }}</span>
                </div>
                <div class="member-detail">
                  <span class="label">状态：</span>
                  <span class="value">{{ member.state || '-' }}</span>
                </div>
                <div class="member-detail">
                  <span class="label">IOM工单：</span>
                  <span class="value">{{ member.iom_order || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-drawer>

  </page-wrapper>
</template>
<script lang="ts" setup>
  import { onActivated, ref, onMounted, defineOptions, getCurrentInstance } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import { useGo } from '@/hooks/web/usePage';
  import { useMessage } from '@/hooks/web/useMessage';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { usePageQuery, useInfo } from '@/hooks/web/useRestAPI';
  import { default as uploadDialog } from './opt_road_group_management_upload.vue';
  import { useETLTask } from '@/hooks/web/useETLTask';
  import { default as NoteDialog } from './optRoadGroupNoteDialog.vue';
  import { default as EditDialog } from './opt_road_group_edit.vue';
  import { default as ElementEditDialog } from './opt_road_group_element_edit.vue';
  import { useUserStore } from '@/store/modules/user';
  import {
    dualRouteLogger,
    OperationType,
    ObjectType,
    recordSimpleLog
  } from '@/utils/operationLog';
  import {
    SearchOutlined,
    PlusOutlined,
    UploadOutlined,
    DownloadOutlined,
    ExperimentOutlined,
    EyeOutlined,
    EditOutlined,
    MoreOutlined,
    ExclamationCircleOutlined,
    UndoOutlined,
    DeleteOutlined,
    BugOutlined,
    InfoCircleOutlined,
    UserOutlined,
    TeamOutlined,
    FileTextOutlined,
    BarChartOutlined
  } from '@ant-design/icons-vue';
  import * as XLSX from 'xlsx';
  import { saveAs } from 'file-saver';

  const { proxy } = getCurrentInstance() as any; //光路保护组管理详情弹窗

  defineOptions({ name: 'OptRoadGroupManagement' });

  const go = useGo();
  const NoteDialogVisible = ref(false);
  const current = ref<any>({});
  const showNoteDialog = (record) => {
    current.value = record;
    NoteDialogVisible.value = true;
  };

  let staticDetailShow = ref(false); //统计详情是否展示

  const uploadDialogVisible = ref(false);
  const showUploadDialog = () => {
    uploadDialogVisible.value = true;
  };

  // 详细信息抽屉相关
  const detailDrawerVisible = ref(false);
  const currentDetailRecord = ref<any>(null);

  const showDetailDrawer = (record) => {
    // 将 object_code 和 object_name 信息传递给详情页面
    const detailRecord = {
      ...record,
      object_code: record.code,
      object_name: record.name,
      area_code: filter.value.ds
    };

    currentDetailRecord.value = detailRecord;
    detailDrawerVisible.value = true;

    // 查看详情的日志记录已在后端统一处理
  };

  // 清理检测结果文本，去除重复内容
  const cleanResultText = (result) => {
    if (!result) return '-';

    // 去除括号内容和重复文字
    let cleanText = result;

    // 移除括号及其内容
    cleanText = cleanText.replace(/\([^)]*\)/g, '');

    // 处理重复的文字，如"单路由单路由" -> "单路由"
    const words = ['同管道', '同光缆', '单路由', '无光路', '单光路', '缺管道', '同局前井', '正常', '局内光路'];
    words.forEach(word => {
      const regex = new RegExp(`(${word})\\1+`, 'g');
      cleanText = cleanText.replace(regex, word);
    });

    // 去除多余的空格和特殊字符
    cleanText = cleanText.trim().replace(/\s+/g, ' ');

    return cleanText || '-';
  };

  // 获取检测结果的颜色（温和色调）
  const getResultColor = (result) => {
    if (!result) return 'default';

    // 严重问题 - 温和的红色系
    if (result.includes('同管道')) return '#ff7875';  // 温和红色
    if (result.includes('同光缆')) return '#ff9c9c';  // 浅红色
    if (result.includes('单路由')) return '#ffaaa5';  // 粉红色

    // 一般问题 - 温和的橙色系
    if (result.includes('无光路')) return '#ffa940';  // 温和橙色
    if (result.includes('单光路')) return '#ffb366';  // 浅橙色
    if (result.includes('缺管道')) return '#ffc069';  // 淡橙色

    // 轻微问题 - 温和的黄色系
    if (result.includes('同局前井')) return '#ffd666'; // 温和黄色

    // 正常状态 - 温和的绿色系
    if (result.includes('正常')) return '#73d13d';     // 温和绿色
    if (result.includes('局内光路')) return '#95de64'; // 浅绿色

    return 'default';
  };

  // 获取检测结果的标签样式（内联样式）
  const getResultTagStyle = (result) => {
    if (!result) return {};

    const baseStyle = {
      fontWeight: '600',
      fontSize: '12px',
      padding: '4px 12px',
      borderRadius: '6px',
      border: '1px solid',
      minWidth: '70px',
      textAlign: 'center',
      display: 'inline-block',
      margin: '0',
      lineHeight: '1.2'
    };

    // 严重问题 - 添加特殊效果
    if (['同管道', '同光缆', '单路由'].some(item => result.includes(item))) {
      return {
        ...baseStyle,
        fontWeight: '700',
        border: '2px solid',
        boxShadow: '0 2px 8px rgba(255, 77, 79, 0.3)',
        animation: 'pulse-critical 2s infinite'
      };
    }

    // 一般问题
    if (['无光路', '单光路', '缺管道'].some(item => result.includes(item))) {
      return {
        ...baseStyle,
        border: '1px solid',
        boxShadow: '0 2px 6px rgba(250, 140, 22, 0.2)'
      };
    }

    // 轻微问题
    if (result.includes('同局前井')) {
      return {
        ...baseStyle,
        fontWeight: '500'
      };
    }

    // 正常状态
    if (['正常', '局内光路'].some(item => result.includes(item))) {
      return baseStyle;
    }

    return baseStyle;
  };

  // 获取检测结果的标签样式类
  const getResultTagClass = (result) => {
    if (!result) return 'result-tag-default';

    // 严重问题
    if (['同管道', '同光缆', '单路由'].some(item => result.includes(item))) {
      return 'result-tag-critical';
    }

    // 一般问题
    if (['无光路', '单光路', '缺管道'].some(item => result.includes(item))) {
      return 'result-tag-warning';
    }

    // 轻微问题
    if (result.includes('同局前井')) {
      return 'result-tag-minor';
    }

    // 正常状态
    if (['正常', '局内光路'].some(item => result.includes(item))) {
      return 'result-tag-success';
    }

    return 'result-tag-default';
  };

  // 获取检测结果的样式类（保留原有方法，用于抽屉）
  const getResultClass = (result) => {
    if (!result) return '';
    if (['同管道', '同光缆', '单光路', '单路由', '无光路', '缺管道'].some(item => result.includes(item))) {
      return 'result-error';
    }
    if (['同局前井'].some(item => result.includes(item))) {
      return 'result-warning';
    }
    if (['正常', '局内光路'].some(item => result.includes(item))) {
      return 'result-success';
    }
    return '';
  };

  // 优化地市选择事件处理
  const handleCityChange = async (v) => {
    // 地市选择不需要禁用查询按钮，只需要表格loading
    try {
      etlAreaCode.value = v;
      filter.value.area_name = null;
      await queryService.queryDictionary();
      await queryService.pageQuery();
    } catch (error) {
      createMessage.error('地市切换失败，请重试');
    }
  };

  // 通用筛选条件变化处理
  const handleFilterChange = async () => {
    // 筛选条件变化不需要禁用查询按钮，只需要表格loading
    try {
      await queryService.pageQuery();
    } catch (error) {
      createMessage.error('筛选失败，请重试');
    }
  };

  // 查询按钮loading状态
  const queryLoading = ref(false);

  // 导出loading状态
  const exportLoading = ref(false);

  // 跳转到操作日志页面
  const goToOperationLog = () => {
    // 记录查看日志操作
    recordSimpleLog({
      operationType: OperationType.VIEW,
      objectType: ObjectType.OPT_GROUP,
      operationDescription: '查看双路由管理操作日志',
      areaCode: filter.value.ds
    });

    go('/nrm/res-app/risk-manage/dual_route_operation_log');
  };

  // 跳转到日志分析页面
  const goToLogAnalytics = () => {
    // 记录查看日志分析操作
    recordSimpleLog({
      operationType: OperationType.VIEW,
      objectType: ObjectType.OPT_GROUP,
      operationDescription: '查看双路由日志分析',
      areaCode: filter.value.ds
    });

    go('/nrm/res-app/risk-manage/dual_route_log_analytics');
  };

  // 导出确认处理
  const handleExport = async () => {
    createConfirm({
      iconType: 'info',
      title: '确认导出',
      content: `即将导出当前筛选条件下的所有光路组数据。

📋 导出信息：
• 导出格式：Excel文件
• 数据范围：当前筛选结果
• 预计耗时：根据数据量而定
• 文件大小：可能较大，请耐心等待

是否确认导出？`,
      okText: '确认导出',
      cancelText: '取消',
      onOk: async () => {
        exportLoading.value = true;
        try {
          // 记录导出操作日志
          recordSimpleLog({
            operationType: OperationType.EXPORT,
            objectType: ObjectType.OPT_GROUP,
            operationDescription: '导出光路组数据',
            areaCode: filter.value.ds
          });

          await download();
          createMessage.success('导出成功！');
        } catch (error) {
          createMessage.error('导出失败，请重试');
        } finally {
          exportLoading.value = false;
        }
      },
    });
  };

  // 跳转到统计分析页面
  const goToStatistics = () => {
    // 传递当前筛选条件到统计分析页面
    const queryParams = {
      ds: filter.value.ds,
      area_name: filter.value.area_name,
      speciality: filter.value.speciality,
      create_start_date: filter.value.create_start_date,
      create_end_date: filter.value.create_end_date,
      source_type_id: filter.value.source_type_id
    };

    // 构建查询字符串
    const queryString = Object.entries(queryParams)
      .filter(([key, value]) => value !== null && value !== undefined && value !== '')
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');

    // 跳转到统计分析页面
    go(`./opt_road_group_statistics${queryString ? '?' + queryString : ''}`);
  };

  const columns_opt_static = ref([
    {
      title: '区县',
      dataIndex: 'leaf_region',
      key: 'leaf_region',
    },
    {
      title: '专业',
      dataIndex: 'speciality',
      key: 'speciality',
    },
    {
      title: '总量',
      dataIndex: 'total_count',
      key: 'total_count',
    },
    {
      title: '同管道',
      dataIndex: 'samepiple_count',
      key: 'samepiple_count',
    },
    {
      title: '同光缆',
      dataIndex: 'samecable_count',
      key: 'samecable_count',
    },
    {
      title: '单光路',
      dataIndex: 'singlefiber_count',
      key: 'singlefiber_count',
    },
    {
      title: '单路由',
      dataIndex: 'singleroute_count',
      key: 'singleroute_count',
    },
    {
      title: '无光路',
      dataIndex: 'nonefiber_count',
      key: 'nonefiber_count',
    },
    {
      title: '缺管道',
      dataIndex: 'lackpiple_count ',
      key: 'lackpiple_count ',
    },
    {
      title: '同局前井',
      dataIndex: 'samejqj_count',
      key: 'samejqj_count',
    },
    {
      title: '局内光路',
      dataIndex: 'jnfiber_count',
      key: 'jnfiber_count',
    },
    {
      title: '正常',
      dataIndex: 'normal',
      key: 'normal',
    },
  ]);

  const columns = ref([
    // {
    //   title: '更多',
    //   dataIndex: 'actionStart',
    //   key: 'actionStart',
    //   fixed: 'left',
    //   width: 40,
    // },
    {
      title: '地市',
      dataIndex: 'areaName',
      key: 'areaName',
      width: 50,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '区县',
      dataIndex: 'leafRegion',
      key: 'leafRegion',
      width: 100,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '专业',
      dataIndex: 'speciality',
      key: 'speciality',
      width: 80,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '光路组ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      ellipsis: false,
      className: 'wrap-text',
    },
    // {
    //   title: '光路组名称',
    //   dataIndex: 'name',
    //   key: 'name',
    //   width: 180,
    //   ellipsis: false,
    //   className: 'wrap-text',
    // },
    {
      title: '光路组编码',
      dataIndex: 'code',
      key: 'code',
      width: 180,
      ellipsis: false,
      className: 'wrap-text',
    },
    
    {
      title: '检测结果',
      dataIndex: 'checkResult',
      key: 'checkResult',
      width: 120,
      ellipsis: false,
      align: 'center',
      className: 'result-column',
    },
    {
      title: '上次检测结果',
      dataIndex: 'previousCheckResult',
      key: 'previousCheckResult',
      width: 130,
      ellipsis: false,
      align: 'center',
      className: 'result-column',
    },
    {
      title: '检测时间',
      key: 'checkTime',
      width: 120,
      ellipsis: false,
      className: 'wrap-text',
    },
    // {
    //   title: '相关客户',
    //   dataIndex: 'customer',
    //   key: 'customer',
    //   width: 60,
    //   ellipsis: false,
    //   className: 'wrap-text',
    // },
    {
      title: '处理结果',
      dataIndex: 'dealResult',
      key: 'dealResult',
      width: 100,
      ellipsis: false,
      className: 'wrap-text',
    },
    // {
    //   title: '集约化派单号',
    //   dataIndex: 'workOrderCode',
    //   key: 'workOrderCode',
    //   width: 60,
    //   ellipsis: false,
    //   className: 'wrap-text',
    // },
    // {
    //   title: '人工确认',
    //   dataIndex: 'confirmOp',
    //   key: 'confirmOp',
    //   width: 60,
    //   ellipsis: false,
    //   className: 'wrap-text',
    // },
    // {
    //   title: '维护人员',
    //   dataIndex: 'maintainers',
    //   key: 'maintainers',
    //   width: 60,
    //   ellipsis: false,
    //   className: 'wrap-text',
    // },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      key: 'createDate',
      width: 120,
      ellipsis: false,
      className: 'wrap-text',

    },
    {
      title: '备注',
      dataIndex: 'note',
      key: 'note',
      width: 800,
      ellipsis: false,
      className: 'wrap-text',

    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      width: 200,     
      className: 'wrap-text',

    },
  ]);

  const create_date_range = ref([])
  const queryService = usePageQuery({
    rootPath: '/graph-rest-api',
    queryUrl: '/api/opt-group-api',
    filter: {
      opt_code: '',
      ds: '南京',
      code: '',
      name: '',
      create_op: '',
      //create_date_range:undefined,
      create_start_date:'',
      create_end_date:'',
      source_type_id: null,
      result: undefined,
    },
    pagination: {
      pageSize: 10,
      hideOnSinglePage: false,
    },
  });

  const { filter, loading, dataSource, dictionary } = queryService;

  // 子表格相关
  const expandedRowKeys = ref<string[]>([]);
  const innerColumns = ref([
    {
      title: '光路编码',
      dataIndex: 'code',
      key: 'code',
      width: 140,
      ellipsis: true,
    },
    {
      title: '光路名称',
      dataIndex: 'name',
      key: 'name',
      width: 160,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 70,
      ellipsis: true,
    },
    {
      title: 'IOM工单',
      dataIndex: 'iom_order',
      key: 'iom_order',
      width: 120,
      ellipsis: true,
    },
    {
      title: '路由序号',
      dataIndex: 'route_no',
      key: 'route_no',
      width: 80,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      width: 120,
    },
  ]);

  // const query = async () => {
  //   await queryService.queryDictionary();
  //   await queryService.pageQuery();
  //   // await statusStatic();
  // };

  const query = async () => {
    console.log("查询时间段:",create_date_range,create_date_range.value[0])
    // 立即设置查询按钮loading状态
    queryLoading.value = true;
    try {
      queryService.filter.value.create_start_date = create_date_range.value[0];
      queryService.filter.value.create_end_date = create_date_range.value[1];
      await queryService.queryDictionary();
      await queryService.pageQuery();
      // await statusStatic();
    } catch (error) {
      createMessage.error('查询失败，请重试');
    } finally {
      // 确保查询按钮loading状态被重置
      queryLoading.value = false;
    }
  };

  onMounted(async () => {
    // 确保用户权限检查在页面挂载时执行，首次加载强制重置
    initUserPermissions(true);
    await query();
  });
  onActivated(async () => {
    // 页面激活时检查权限，但不强制重置地市选择
    initUserPermissions(false);
     // await query();
  });

  const infoService = useInfo({
    rootPath: '/graph-rest-api',
    info: {
      ds: '',
      id: '',
      group_name: '',
      speciality:'',
    },
    responseType: 'blob',
  });

  const { createMessage, createConfirm } = useMessage();
  const spinning = ref<boolean>(false);

  const check_analyze = async () => {
    createConfirm({
      iconType: 'warning',
      title: '确认一键检测',
      content: `即将对当前筛选条件下的所有光路组进行风险检测分析。

⚠️ 重要提醒：
• 此操作将消耗大量系统资源
• 预计耗时：5-30分钟（取决于数据量）
• 检测期间请勿关闭页面
• 建议在业务低峰期执行

是否确认开始检测？`,
      okText: '确认检测',
      cancelText: '取消',
      onOk: async () => {
        const ds = filter.value.ds;
        infoService.info.value.ds = ds;
        createMessage.info('一键检测开始！预计需要5-30分钟，请耐心等待...');
        etlStart();
      },
    });
  };

  const default_area_code: string = '无锡';
  const {
    areaCode: etlAreaCode,
    loading: etlLoading,
    start: etlStart,
  } = useETLTask({
    ruleCode: 'python_opt_pair_check',
    nodeGroup: 'node-group-k8s01',
    areaCode: default_area_code,
    onComplete: () => {
      query();
    },
  });
  const deleteRecord = async (record) => {
    createConfirm({
      iconType: 'warning',
      title: '请确认',
      content: '删除后将不可恢复',
      onOk: async () => {
        const ds = filter.value.ds;
        console.log('record.group_name', record);
        infoService.info.value.ds = ds;
        infoService.info.value.id = record.id;

        // 记录删除操作日志
        recordSimpleLog({
          operationType: OperationType.DELETE,
          objectType: ObjectType.OPT_GROUP,
          objectId: record.id,
          objectCode: record.code,
          objectName: record.name,
          operationDescription: `删除光路组：${record.name}`,
          areaCode: ds
        });

        let result = await infoService.doCreateNew(`/api/opt-group-api/deleteOptGroupRecord`);
        if (result > 0) {
          createMessage.info('删除成功！');
        } else {
          createMessage.warn('删除失败！');
        }
        await queryService.pageQuery();
      },
    });
  };
  const riskAnalyze = async (record) => {
    infoService.info.value.ds = filter.value.ds;
    infoService.info.value.id = record.id;
    infoService.info.value = record;

    // 确保传递 code 和 name 给后端，用于日志记录
    infoService.info.value.code = record.code;
    infoService.info.value.name = record.name;

    console.log('🎯 管理页面风险检测: id={}, code={}, name={}, ds={}',
                record.id, record.code, record.name, filter.value.ds);

    createMessage.info('开始检测，稍等！');

    const result = await infoService.doCreateNew('/api/opt-group-api/risk-analyze');

    // 移除前端的重复日志记录，只在后端记录一次
    // recordRiskDetectionLog 调用已移除，避免重复记录

    createMessage.info('检测完成');
    await queryService.pageQuery();
  };

  const showEditVisible = ref(false);
  const showEditDialog = async (record: any) => {
    record.area_name = record.areaName;

    // 保存修改前的数据，用于日志记录
    const beforeData = {
      code: record.code,
      name: record.name,
      note: record.note,
      area_name: record.area_name,
      leafRegion: record.leafRegion,
      speciality: record.speciality,
      customer: record.customer,
      maintainers: record.maintainers,
      last_update_time: record.update_time || new Date().toISOString()
    };

    // 将before_data添加到record中，传递给编辑弹窗
    record.before_data = beforeData;

    console.log('🎯 打开修改弹窗，保存修改前数据:', beforeData);

    current.value = record;
    showEditVisible.value = true;
  };

  const showNewDialog = () => {
    current.value = {};
    showEditVisible.value = true;
  };

  const showElementEditVisible = ref(false);
  const saveOpt = async (record) => {
    console.log('record', record);
    current.value = { group_id: record.id, ds: filter.value.ds, code: record.code };
    showElementEditVisible.value = true;
  };

  //光路详情列表修改
  const updateOpt = async (record) => {
    // 保存修改前的数据，用于日志记录
    const beforeData = {
      id: record.id,
      code: record.code,
      name: record.name,
      state: record.state,
      iom_order: record.iom_order,
      route_no: record.route_no,
      group_id: record.group_id,
      last_update_time: record.update_time || new Date().toISOString()
    };

    console.log('🎯 打开光路元素修改弹窗，保存修改前数据:', beforeData);

    current.value = record;
    current.value.areaName = filter.value.ds;
    current.value.opt_code = record.code;
    current.value.opt_name = record.name;
    current.value.before_data = beforeData; // 添加修改前数据
    showElementEditVisible.value = true;
  };
  const userStore = useUserStore();

  // 根据用户地点限制地市切换功能
  const cityUnchangable = ref(true);

  // 初始化用户权限检查函数
  const initUserPermissions = (forceReset = false) => {
    console.log('userStore', userStore.getAreaName, 'forceReset:', forceReset);
    if (userStore.getAreaCode == 'js') {
      cityUnchangable.value = false; // js用户可以切换地市
      // 只在强制重置或者地市为空时才设置默认地市
      if (forceReset || !filter.value.ds) {
        filter.value.ds = '无锡'; // 省级用户默认地市改成无锡
        console.log('省级用户地市设置为默认值：无锡');
      } else {
        console.log('省级用户保持当前地市：', filter.value.ds);
      }
    } else {
      filter.value.ds = userStore.getAreaName; // 设置用户所在地市
      cityUnchangable.value = true; // 地市用户不可以切换地市
    }
  };

  // 立即执行权限检查，不强制重置
  initUserPermissions(false);

  const confirmResult = async (record) => {
    createConfirm({
      iconType: 'warning',
      title: '请确认',
      content: '是否确认该检测结果',
      onOk: async () => {
        infoService.info.value.userName =
          userStore.getUserInfo.realName + '(' + userStore.getUserInfo.username + ')';
        infoService.info.value.ds = filter.value.ds;
        infoService.info.value.id = record.id;
        await infoService.doCreateNew(`/api/opt-group-api/confirm_result`);
        await query();
      },
    });
  };

  const cancelConfirmResult = async (id) => {
    infoService.info.value.ds = filter.value.ds;
    infoService.info.value.id = id;
    await infoService.doCreateNew(`/api/opt-group-api/cancel_confirm_result`);
    await query();
  };

  //光路列表删除
  const deleteOpt = async (record) => {
    createConfirm({
      iconType: 'warning',
      title: '请确认',
      content: '删除后将不可恢复',
      onOk: async () => {
        // 保存删除前的数据，用于日志记录
        const beforeData = {
          id: record.id,
          code: record.code,
          name: record.name,
          state: record.state,
          iom_order: record.iom_order,
          route_no: record.route_no,
          group_id: record.group_id,
          last_update_time: record.update_time || new Date().toISOString()
        };

        console.log('🎯 删除光路元素，保存删除前数据:', beforeData);

        infoService.info.value.id = record.id;
        infoService.info.value.ds = filter.value.ds;
        infoService.info.value.before_data = beforeData; // 添加删除前数据
        infoService.info.value.code = record.code; // 添加对象编码
        infoService.info.value.name = record.name; // 添加对象名称

        await infoService.doDownload(`/api/opt-group-api/deleteOpt`);
        await cancelConfirmResult(record.group_id);
        await query();

        // 关闭光路元素编辑弹窗（如果打开的话）
        showElementEditVisible.value = false;
      },
    });
  };

  const onFinish = async (current) => {
    let current_id_cancel_confirm = '';
    if (current.group_id) current_id_cancel_confirm = current.group_id;
    else current_id_cancel_confirm = current.id;
    await cancelConfirmResult(current_id_cancel_confirm);
    await queryService.pageQuery();

    // 关闭光路元素编辑弹窗
    showElementEditVisible.value = false;
  };

  // 处理表格展开
  const handleExpand = (expanded, record) => {
    if (expanded) {
      expandedRowKeys.value = [record.id];
    } else {
      expandedRowKeys.value = [];
    }
  };

  // 处理行展开点击
  const handleRowExpand = (record) => {
    console.log('当前row数据', record);
    const isExpanded = expandedRowKeys.value.includes(record.id);
    if (isExpanded) {
      expandedRowKeys.value = [];
    } else {
      expandedRowKeys.value = [record.id];
    }
  };

  // 子表格操作方法
  const handUpdateOpt = async (record) => {
    updateOpt(record);
  };

  const handleDeleteOpt = (record) => {
    deleteOpt(record);
  };





  //导出功能
  const downloadinfoService = useInfo({
    rootPath: '/graph-rest-api',
    info: {
      ds: '',
      area_name:'',
      speciality:'',
      result:'',
      deal_result:'',
      opt_code:'',
      name:'',
      code:'',
    },
    responseType: 'blob',
  });

  const download = async () => {
    downloadinfoService.info.value = filter.value
    const result = await downloadinfoService.doDownload(`/api/opt-group-api/opt_group_download`);
    const blob = new Blob([result], { type: 'application/vnd.ms-excel' });
    let fileName = filter.value.ds + '光路组隐患分析' + Date.parse(new Date().toString()) + '.xlsx';
    let a = document.createElement('a');
    a.download = fileName;
    a.href = window.URL.createObjectURL(blob);
    a.click();
    a.remove();
  };

  const ignorRisk_infoService = useInfo({
    rootPath: '/graph-rest-api',
    info: {
      ds: '',
    },
    responseType: 'blob',
  });
  const ignoreRisk = async (record) => {
    try {
      // 立即设置loading状态
      ignorRisk_infoService.loading.value = true;

      record.userCode = userStore.getUserInfo.realName + userStore.getUserInfo.username;
      // 确保传递 code 和 name 给后端
      record.code = record.code;
      record.name = record.name;
      record.ds = filter.value.ds;
      record.areaName = filter.value.ds; // 后端期望的字段名

      ignorRisk_infoService.info.value = record;

      console.log('🎯 忽略风险操作: id={}, code={}, name={}, ds={}, areaName={}',
                  record.id, record.code, record.name, record.ds, record.areaName);

      const result = await ignorRisk_infoService.doDownload(
        `/api/opt-group-api/deal_result_update_ignore`,
      );

      await query();
    } catch (error) {
      console.error('忽略风险操作失败:', error);
    } finally {
      // 确保loading状态被重置
      ignorRisk_infoService.loading.value = false;
    }
  };

  const restorRisk = async (record) => {
    try {
      // 立即设置loading状态
      ignorRisk_infoService.loading.value = true;

      record.userCode = userStore.getUserInfo.realName + userStore.getUserInfo.username;
      // 确保传递 code 和 name 给后端
      record.code = record.code;
      record.name = record.name;
      record.ds = filter.value.ds;
      record.areaName = filter.value.ds; // 后端期望的字段名

      ignorRisk_infoService.info.value = record;

      console.log('🎯 恢复风险操作: id={}, code={}, name={}, ds={}, areaName={}',
                  record.id, record.code, record.name, record.ds, record.areaName);

      const result = await ignorRisk_infoService.doDownload(`/api/opt-group-api/deal_result_update`);

      await query();
    } catch (error) {
      console.error('恢复风险操作失败:', error);
    } finally {
      // 确保loading状态被重置
      ignorRisk_infoService.loading.value = false;
    }
  };
  const exportExcel = () => {
    const data = dictionary.value.group_static;
    const exportData = data.map((item) => {
      const newItem = {};
      columns_opt_static.value.forEach((col) => {
        newItem[col.title] = item[col.dataIndex];
      });
      return newItem;
    });
    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
    saveAs(blob, '光路数据总览.xlsx');
  };
</script>

<style scoped lang="less">
  a-tag {
    display: block;
    /* 或者 inline-block */
    margin-bottom: 5px;
    /* 控制元素间距 */
  }

  .blue-line {
    display: inline-block;
    margin-right: 15px;
    /* 可以根据需要调整间距 */
    border-left: 5px solid #1890ff;
    /* 蓝色竖线的颜色 */
    padding-left: 5px;
    /* 左侧填充，确保文字不会紧贴竖线 */
  }

  /* 新的搜索容器样式 - 清新科技感蓝白配色 */
  .search-container {
    margin-bottom: 20px;
    background: url('@/assets/images/groupObstacle/search_bg.png') no-repeat;
    background-size: 100% 100%;
    border-radius: 12px;
    padding: 20px 24px;
    box-shadow: 0 4px 20px rgba(47, 108, 237, 0.08);
    border: 1px solid rgba(47, 108, 237, 0.1);
    position: relative;
    overflow: hidden;
    min-height: 140px; /* 适配新的筛选框高度 */

    /* 添加科技感光效 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(240, 248, 255, 0.95) 50%,
        rgba(230, 244, 255, 0.9) 100%);
      z-index: 1;
    }

    /* 内容层级 */
    > * {
      position: relative;
      z-index: 2;
    }
  }

  /* 筛选条件区域 */
  .filter-section {
    margin-bottom: 16px;
  }

  .filter-group {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .filter-label {
    font-weight: 600;
    color: #1e5bb8;
    margin-right: 12px;
    white-space: nowrap;
    font-size: 14px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  }

  .filter-select {
    border-radius: 8px;

    :deep(.ant-select-selector) {
      border-radius: 8px;
      border: 1px solid rgba(47, 108, 237, 0.2);
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(47, 108, 237, 0.05);

      &:hover {
        border-color: #2f6ced;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 4px 16px rgba(47, 108, 237, 0.1);
      }
    }

    :deep(.ant-select-focused .ant-select-selector) {
      border-color: #2f6ced;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 0 0 3px rgba(47, 108, 237, 0.1), 0 4px 16px rgba(47, 108, 237, 0.15);
    }
  }

  .filter-input {
    border-radius: 8px;
    border: 1px solid rgba(47, 108, 237, 0.2);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(47, 108, 237, 0.05);

    &:hover {
      border-color: #2f6ced;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 4px 16px rgba(47, 108, 237, 0.1);
    }

    &:focus {
      border-color: #2f6ced;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 0 0 3px rgba(47, 108, 237, 0.1), 0 4px 16px rgba(47, 108, 237, 0.15);
    }
  }

  /* 操作按钮区域 */
  .action-section {
    padding-top: 20px;
    border-top: 1px solid rgba(47, 108, 237, 0.1);
    display: flex;
    justify-content: flex-end;
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }

  .action-btn {
    height: 38px;
    border-radius: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);

    &.primary-btn {
      background: linear-gradient(135deg, #2f6ced 0%, #158ffe 100%);
      border: none;
      color: white;
      box-shadow: 0 4px 16px rgba(47, 108, 237, 0.2);

      &:hover {
        background: linear-gradient(135deg, #1e5bb8 0%, #0d7ce6 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(47, 108, 237, 0.3);
      }
    }

    &.secondary-btn {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(47, 108, 237, 0.3);
      color: #2f6ced;
      box-shadow: 0 2px 8px rgba(47, 108, 237, 0.1);

      &:hover {
        background: rgba(240, 248, 255, 1);
        border-color: #2f6ced;
        color: #1e5bb8;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(47, 108, 237, 0.15);
      }
    }

    &.warning-btn {
      background: rgba(255, 247, 230, 0.9);
      border: 1px solid rgba(255, 169, 64, 0.4);
      color: #fa8c16;
      box-shadow: 0 2px 8px rgba(250, 140, 22, 0.1);

      &:hover {
        background: rgba(255, 241, 184, 1);
        border-color: #fa8c16;
        color: #d46b08;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(250, 140, 22, 0.15);
      }
    }
  }

  .loading-text {
    color: #ff4d4f;
    font-size: 12px;
    margin-left: 4px;
    font-weight: 400;
  }

  /* 移除不需要的详细筛选样式 */

  /* 保留旧按钮样式以兼容其他地方使用 */
  .btnSearch {
    width: 90px;
    height: 32px;
    background-image: linear-gradient(270deg, #158ffe 0%, #2f6ced 100%);
    border-radius: 4px;
    font-size: 14px;
    color: #ffffff;
    border: 0 none;
    letter-spacing: 1px;
    &:hover {
      color: #fff;
    }
  }

  :deep(.table-striped) td {
    background-color: #fff;
  }
  :deep(.table-striped) td {
    background-color: #f0f6ff;
  }

  .btnSty {
    color: #1e85f2;
    border-radius: 0;
    border-right: 1px solid #d9d9d9;
    padding: 0 8px;
    height: 20px;
    line-height: 20px;
    &:first-child {
      padding-left: 0;
    }
    &:last-child {
      border-right: 0 none;
      padding-right: 3px;
    }
  }

  // 统计
  .staticBox {
    width: 100%;
    height: 36px;
    line-height: 36px;
    background: #ecf4ff;
    border: 1px solid #2f6ced;
    border-radius: 4px;
    cursor: pointer;
    // margin-top: 16px;
    span {
      &.stcTitle {
        margin-right: 2px;
        margin-left: 12px;
      }
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #246ad9;
      margin-right: 30px;
    }
  }

  /* 移除旧的btnBox样式，使用新的action-section布局 */

  // a-card 卡片样式自定义
  :deep(.ant-card .ant-card-body) {
    padding: 15px;
  }

  //表格高度修改
  .tableBox {
    :deep(.ant-table-thead tr th) {
      padding: 14px 16px;
    }
    :deep(.ant-table-body .ant-table-row td) {
      // height:40px;
      padding: 12px 16px;
    }
  }

  .tableBox .ant-table-cell {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    overflow: hidden;
  }

  .btnSty {
    margin: 2px 0;
    text-align: left;
    white-space: normal;
  }

  .wrap-text {
    white-space: normal;
    overflow: hidden;
    text-overflow: clip;
  }

  /* 响应式设计 - 清新科技感适配 */
  @media (max-width: 1200px) {
    .search-container {
      min-height: 160px; /* 小屏幕适配高度 */
    }

    .action-section {
      justify-content: center;
    }

    .action-buttons {
      justify-content: center;
    }

    :deep(.ant-space) {
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  @media (max-width: 992px) {
    .search-container {
      min-height: 160px; /* 中等屏幕适配高度 */
    }

    /* 中等屏幕下改为单列布局 */
    :deep(.ant-col) {
      flex: 0 0 100% !important;
      max-width: 100% !important;
    }
  }

  @media (max-width: 768px) {
    .search-container {
      padding: 16px 20px;
      min-height: 180px; /* 移动端适配高度 */
      border-radius: 8px;
    }

    .filter-group {
      flex-direction: column;
      align-items: flex-start;
      width: 100%;
      gap: 8px;
    }

    .filter-label {
      margin-bottom: 8px;
      margin-right: 0;
      width: 100%;
    }

    .filter-select,
    .filter-input {
      width: 100% !important;
    }

    .action-btn {
      width: 100%;
      justify-content: center;
      margin-bottom: 8px;
    }

    .action-section {
      padding-top: 16px;
    }

    :deep(.ant-space) {
      width: 100%;
      flex-direction: column;
    }

    :deep(.ant-col) {
      flex: 0 0 100% !important;
      max-width: 100% !important;
    }
  }

  /* 下拉菜单危险操作样式 */
  :deep(.ant-dropdown-menu-item.danger-item) {
    color: #ff4d4f !important;
  }

  :deep(.ant-dropdown-menu-item.danger-item:hover) {
    background-color: #fff2f0 !important;
    color: #ff4d4f !important;
  }

  /* 菜单项图标间距 */
  :deep(.ant-dropdown-menu-item .anticon) {
    margin-right: 8px;
  }

  /* 检测结果列样式 */
  :deep(.result-column) {
    text-align: center !important;
    padding: 8px 4px !important;
    vertical-align: middle !important;
  }

  :deep(.result-column .ant-table-cell) {
    padding: 8px 4px !important;
    text-align: center !important;
  }

  /* 基于检测结果内容的特殊样式 - 通过CSS类实现 */
  /* 严重问题 - 温和的红色系 + 轻微闪烁动画 */
  :deep(.result-column .result-tag-critical) {
    background-color: #ff7875 !important;
    color: white !important;
    border-color: #ff9c9c !important;
    font-weight: 600 !important;
    animation: pulse-critical-soft 3s infinite !important;
    box-shadow: 0 2px 6px rgba(255, 120, 117, 0.2) !important;
    border: 1px solid !important;
  }

  /* 一般问题 - 温和的橙色系 */
  :deep(.result-column .result-tag-warning) {
    background-color: #ffa940 !important;
    color: white !important;
    border-color: #ffb366 !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(255, 169, 64, 0.15) !important;
  }

  /* 轻微问题 - 温和的黄色系 */
  :deep(.result-column .result-tag-minor) {
    background-color: #ffd666 !important;
    color: #8c6e00 !important;
    border-color: #ffe58f !important;
    font-weight: 500 !important;
    box-shadow: 0 1px 3px rgba(255, 214, 102, 0.2) !important;
  }

  /* 正常状态 - 温和的绿色系 */
  :deep(.result-column .result-tag-success) {
    background-color: #73d13d !important;
    color: white !important;
    border-color: #95de64 !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(115, 209, 61, 0.15) !important;
  }

  /* 默认状态 */
  :deep(.result-column .result-tag-default) {
    background-color: #f0f0f0 !important;
    color: #666 !important;
    border-color: #d9d9d9 !important;
    font-weight: 400 !important;
  }

  /* 检测结果列中的标签特殊处理 */
  :deep(.result-column .ant-tag) {
    margin: 2px 0 !important;
    min-width: 70px !important;
    text-align: center !important;
    font-size: 12px !important;
    line-height: 1.2 !important;
    border-radius: 6px !important;
    padding: 4px 8px !important;
  }

  /* 检测结果容器样式 */
  .result-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 2px 0;
  }

  /* 检测结果标签基础样式 */
  .result-tag {
    margin: 0 !important;
    padding: 4px 12px !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    text-align: center !important;
    min-width: 70px !important;
    display: inline-block !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 120px !important;
  }

  /* 严重问题温和闪烁动画 */
  @keyframes pulse-critical-soft {
    0% {
      box-shadow: 0 2px 6px rgba(255, 120, 117, 0.2);
      opacity: 1;
    }
    50% {
      box-shadow: 0 3px 10px rgba(255, 120, 117, 0.3);
      opacity: 0.9;
    }
    100% {
      box-shadow: 0 2px 6px rgba(255, 120, 117, 0.2);
      opacity: 1;
    }
  }

  /* 保留原有动画作为备用 */
  @keyframes pulse-critical {
    0% {
      box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
      transform: scale(1);
    }
    50% {
      box-shadow: 0 4px 16px rgba(255, 77, 79, 0.5);
      transform: scale(1.02);
    }
    100% {
      box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
      transform: scale(1);
    }
  }

  /* 检测结果标签样式类 */
  .result-tag-critical {
    font-weight: 700 !important;
    border: 2px solid !important;
    box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3) !important;
    animation: pulse-critical 2s infinite !important;
  }

  .result-tag-warning {
    font-weight: 600 !important;
    border: 1px solid !important;
    box-shadow: 0 2px 6px rgba(250, 140, 22, 0.2) !important;
  }

  .result-tag-minor {
    font-weight: 500 !important;
    border: 1px solid !important;
  }

  .result-tag-success {
    font-weight: 600 !important;
    border: 1px solid !important;
  }

  .result-tag-default {
    font-weight: 400 !important;
    border: 1px solid #d9d9d9 !important;
    color: #666 !important;
  }

  /* 表格中的检测结果特殊处理 */
  :deep(.ant-table-tbody .result-container) {
    padding: 2px 0;
  }

  :deep(.ant-table-tbody .ant-tag) {
    margin: 0 !important;
  }

  /* 无数据显示 */
  .no-data {
    color: #bfbfbf;
    font-style: italic;
    font-size: 12px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .result-tag {
      min-width: 60px !important;
      max-width: 100px !important;
      font-size: 11px !important;
      padding: 3px 8px !important;
    }
  }

  /* 详细信息抽屉样式 */
  .detail-drawer {
    :deep(.ant-drawer-header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-bottom: none;
    }

    :deep(.ant-drawer-title) {
      color: white;
      font-weight: 600;
      font-size: 16px;
    }

    :deep(.ant-drawer-close) {
      color: white;
    }

    :deep(.ant-drawer-close:hover) {
      color: #f0f0f0;
    }
  }

  .detail-content {
    padding: 0;
  }

  .detail-section {
    margin-bottom: 24px;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
  }

  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 12px;
    border-bottom: 2px solid #f0f2f5;
  }

  .section-title .anticon {
    color: #1890ff;
    font-size: 18px;
  }

  .detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  .detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .detail-item.full-width {
    grid-column: 1 / -1;
  }

  .detail-item .label {
    font-size: 12px;
    color: #8c8c8c;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .detail-item .value {
    font-size: 14px;
    color: #262626;
    font-weight: 500;
    word-break: break-all;
    line-height: 1.4;
  }

  /* 检测结果颜色 */
  .result-error {
    color: #ff4d4f !important;
    font-weight: 600;
  }

  .result-warning {
    color: #fa8c16 !important;
    font-weight: 600;
  }

  .result-success {
    color: #52c41a !important;
    font-weight: 600;
  }

  /* 光路成员列表样式 */
  .members-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .member-item {
    background: #fafafa;
    border-radius: 6px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;
  }

  .member-item:hover {
    background: #f0f2f5;
    border-color: #d9d9d9;
  }

  .member-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px dashed #d9d9d9;
  }

  .member-index {
    background: #1890ff;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
  }

  .member-code {
    font-weight: 600;
    color: #262626;
    font-size: 14px;
  }

  .member-route {
    background: #e6f7ff;
    color: #1890ff;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }

  .member-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }

  .member-detail {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .member-detail .label {
    font-size: 12px;
    color: #8c8c8c;
    font-weight: 500;
    min-width: 60px;
  }

  .member-detail .value {
    font-size: 13px;
    color: #595959;
    font-weight: 500;
  }

  /* 抽屉响应式设计 */
  @media (max-width: 768px) {
    .detail-drawer {
      :deep(.ant-drawer-content-wrapper) {
        width: 100% !important;
      }
    }

    .detail-grid {
      grid-template-columns: 1fr;
    }

    .member-details {
      grid-template-columns: 1fr;
    }

    .member-header {
      flex-wrap: wrap;
    }
  }
</style>
