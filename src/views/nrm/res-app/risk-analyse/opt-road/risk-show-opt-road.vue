<template>
  <page-wrapper>
    <!-- 页面头部操作区域 -->
    <div class="page-header-actions" style="margin-bottom: 16px;">
      <a-space>
        <a-button type="primary" size="large" @click="handleSmartRouteRecommendation">
          <template #icon>
            <bulb-outlined />
          </template>
          智能路由推荐
        </a-button>
        <a-button @click="handleHistoryVersions">
          <template #icon>
            <history-outlined />
          </template>
          历史版本查看
        </a-button>
        <a-button @click="handleRefresh" :loading="loading">
          <template #icon>
            <reload-outlined />
          </template>
          重新检测
        </a-button>
      </a-space>
    </div>

    <!-- 第一模块：双路由保护组详情 -->
    <a-card title="双路由保护组详情" :bordered="false" style="margin-bottom: 16px;" :loading="protectionGroupLoading">
      <a-descriptions bordered :column="3" size="small">
        <a-descriptions-item label="保护组名称" :span="2">
          <a-typography-text strong>
            {{ protectionGroupDetails.name || data_gis.title || '未知保护组' }}
          </a-typography-text>
        </a-descriptions-item>
        <a-descriptions-item label="当前检测结果">
          <a-tag :color="getStatusColor(protectionGroupDetails.checkResult || data_gis.check_result)">
            {{ protectionGroupDetails.checkResult || data_gis.check_result || '检测中' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="保护组ID">
          {{ protectionGroupDetails.id || id.split('_')[0] }}
        </a-descriptions-item>
        <a-descriptions-item label="保护组编码">
          {{ protectionGroupDetails.code || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="专业">
          <a-tag color="blue">
            {{ protectionGroupDetails.speciality || '未知' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="地市">
          {{ protectionGroupDetails.areaName || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="区县">
          {{ protectionGroupDetails.leafRegion || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="上次检测结果">
          <a-tag :color="getStatusColor(protectionGroupDetails.previousCheckResult)">
            {{ protectionGroupDetails.previousCheckResult || '未检测' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="检测时间">
          {{ formatDateTime(protectionGroupDetails.checkTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ formatDateTime(protectionGroupDetails.createDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="确认状态">
          <a-tag :color="protectionGroupDetails.confirmStatus === '已确认' ? 'green' : 'orange'">
            {{ protectionGroupDetails.confirmStatus || '未确认' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="确认人">
          {{ protectionGroupDetails.confirmOp || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="处理结果">
          <a-tag :color="getDealResultColor(protectionGroupDetails.dealResult)">
            {{ protectionGroupDetails.dealResult || '未处理' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="异常信息" v-if="protectionGroupDetails.exceptionInfo" :span="3">
          <div style="white-space: pre-line; max-height: 100px; overflow-y: auto;">
            {{ protectionGroupDetails.exceptionInfo }}
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="备注信息" v-if="protectionGroupDetails.note" :span="3">
          <div style="white-space: pre-line; max-height: 100px; overflow-y: auto;">
            {{ protectionGroupDetails.note }}
          </div>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 保护组内光路详情 -->
      <a-divider orientation="left">
        <span style="font-size: 14px; color: #1890ff;">
          光路详情 ({{ opticalPathList.length }} 条)
        </span>
      </a-divider>
      <a-spin :spinning="opticalPathLoading" tip="正在加载光路详情...">
        <div style="margin-top: 16px;">
          <div v-if="opticalPathList.length === 0" class="empty-state">
            <a-empty description="暂无光路数据" />
          </div>
          <div v-else class="optical-path-tags">
            <a-tag
              v-for="(path, index) in opticalPathList"
              :key="path.code"
              color="blue"
              class="optical-path-tag"
              @click="highlightOpticalPath(path.code)"
            >
              <span class="path-index">{{ index + 1 }}</span>
              {{ path.code }}
            </a-tag>
          </div>
        </div>
      </a-spin>
    </a-card>

    <!-- 第二模块：相关问题展示 -->
    <a-card title="相关问题展示" :bordered="false" style="margin-bottom: 16px;">
      <!-- 检测进度和步骤 -->
      <div style="margin-bottom: 24px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h4 style="margin: 0;">检测进度</h4>
          <a-tag :color="progressPercent === 100 ? 'success' : 'processing'">
            {{ progressPercent }}% 完成
          </a-tag>
        </div>

        <a-progress
          :percent="progressPercent"
          :status="progressPercent === 100 ? 'success' : 'active'"
          stroke-linecap="round"
          style="margin-bottom: 16px;"
        />

        <!-- 检测步骤详情 -->
        <a-steps
          :current="currentStepIndex"
          size="small"
          direction="horizontal"
          style="margin-bottom: 16px;"
        >
          <a-step
            v-for="(step, index) in detectionSteps"
            :key="step.key"
            :title="step.title"
            :description="step.description"
            :status="step.status"
          />
        </a-steps>
      </div>

      <!-- 检测结果概览 -->
      <a-alert
        :message="data_gis.check_result || '检测中...'"
        :description="data_gis.exception_info || '正在进行风险检测，请稍候...'"
        :type="getAlertType(data_gis.check_result)"
        show-icon
        style="margin-bottom: 16px;"
      />

      <!-- 问题分类展示 -->
        <a-collapse v-model:activeKey="activeKey" :bordered="false" expandIconPosition="right">
        <!-- 端口光路错误 -->
        <a-collapse-panel
          key="1"
          v-if="
            (data_gis.sameCables && data_gis.sameCables.length > 0) ||
            (data_gis.count_sameCables && data_gis.count_sameCables > 0)
          "
        >
          <template #header>
            <a-row align="middle">
              <a-col :span="4">
                <a-space>
                  <exclamation-circle-outlined style="color: #ff4d4f;" />
                  <span style="font-weight: bold;">同光缆问题</span>
                </a-space>
              </a-col>
              <a-col :span="6">
                <a-tag color="error">
                  共 {{ data_gis.count_sameCables || 0 }} 个问题
                </a-tag>
              </a-col>
              <a-col :span="14">
                <span style="color: #666;">影响范围：光路保护失效</span>
              </a-col>
            </a-row>
          </template>
          <div class="problem-details">
            <a-row v-for="(item, index) in data_gis.sameCables_d" :key="index" style="margin-bottom: 12px;">
              <a-col :span="24">
                <a-card size="small" style="background: #fafafa;">
                  <a-row align="middle">
                    <a-col :span="5">
                      <a-typography-text type="primary" strong>{{ item.result.a_opt_code }}</a-typography-text>
                    </a-col>
                    <a-col :span="2" style="text-align: center;">
                      <arrow-right-outlined style="color: #666;" />
                    </a-col>
                    <a-col :span="5">
                      <a-typography-text type="success" strong>{{ item.result.z_opt_code }}</a-typography-text>
                    </a-col>
                    <a-col :span="12">
                      <a-space wrap>
                        <a-tag v-for="cable in item.sameCables" :key="cable.id" color="orange">
                          {{ cable.code }}
                        </a-tag>
                      </a-space>
                    </a-col>
                  </a-row>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-collapse-panel>

        <!-- 板卡错误 -->
        <a-collapse-panel key="2" v-if="data_gis.count_samePipeSegments >= 0">
          <template #header>
            <a-row align="middle">
              <a-col :span="4">
                <a-space>
                  <warning-outlined style="color: #faad14;" />
                  <span style="font-weight: bold;">同管道问题</span>
                </a-space>
              </a-col>
              <a-col :span="6">
                <a-tag color="warning">
                  共 {{ data_gis.count_samePipeSegments || 0 }} 个问题
                </a-tag>
              </a-col>
              <a-col :span="14">
                <span style="color: #666;">影响范围：物理路径冗余失效</span>
              </a-col>
            </a-row>
          </template>
          <div class="problem-details">
            <a-row v-for="(item, index) in data_gis.samePipeSegments_d" :key="index" style="margin-bottom: 12px;">
              <a-col :span="24">
                <a-card size="small" style="background: #fafafa;">
                  <a-row align="middle" style="margin-bottom: 8px;">
                    <a-col :span="5">
                      <a-typography-text type="primary" strong>{{ item.result.a_opt_code }}</a-typography-text>
                    </a-col>
                    <a-col :span="2" style="text-align: center;">
                      <arrow-right-outlined style="color: #666;" />
                    </a-col>
                    <a-col :span="5">
                      <a-typography-text type="success" strong>{{ item.result.z_opt_code }}</a-typography-text>
                    </a-col>
                    <a-col :span="12">
                      <a-tag color="error">
                        共计 {{ item.pipeSegments.filter((v) => !v.code.includes('忽略')).length }} 个同管道
                      </a-tag>
                    </a-col>
                  </a-row>

                  <!-- 管道段分类列表 -->
                  <div class="pipe-segments-classified">
                    <!-- 非局前井管道段 -->
                    <div v-if="getNonWellPipeSegments(item.pipeSegments).length > 0" class="pipe-category">
                      <div class="category-header">
                        <span class="category-title">同管道段</span>
                        <a-tag color="error" size="small">
                          {{ getNonWellPipeSegments(item.pipeSegments).length }} 个
                        </a-tag>
                      </div>
                      <div class="category-content">
                        <a-space wrap>
                          <a-tag
                            v-for="pipeSegment in getNonWellPipeSegments(item.pipeSegments)"
                            :key="pipeSegment.id"
                            :color="pipeSegment.code.includes('忽略') ? 'default' : 'red'"
                            class="clickable-text pipe-segment-tag"
                            @click="pipeSegmentClick(pipeSegment)"
                          >
                            {{ pipeSegment.code }}
                          </a-tag>
                        </a-space>
                      </div>
                    </div>

                    <!-- 局前井管道段 -->
                    <div v-if="getWellPipeSegments(item.pipeSegments).length > 0" class="pipe-category">
                      <div class="category-header">
                        <span class="category-title">同局前井</span>
                        <a-tag color="warning" size="small">
                          {{ getWellPipeSegments(item.pipeSegments).length }} 个
                        </a-tag>
                      </div>
                      <div class="category-content">
                        <a-space wrap>
                          <a-tag
                            v-for="pipeSegment in getWellPipeSegments(item.pipeSegments)"
                            :key="pipeSegment.id"
                            :color="pipeSegment.code.includes('忽略') ? 'default' : 'orange'"
                            class="clickable-text pipe-segment-tag"
                            @click="pipeSegmentClick(pipeSegment)"
                          >
                            {{ pipeSegment.code }}
                          </a-tag>
                        </a-space>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-collapse-panel>
      </a-collapse>

    </a-card>

    <!-- 第三模块：地图可视化 -->
    <a-card title="地图可视化" :bordered="false" style="margin-bottom: 16px;">
      <a-spin :spinning="riskAnalysisLoading" tip="正在加载地图数据...">
        <div class="map-container-full">
          <gisView ref="ref_map" @recheck="re_risk_analyse" />
        </div>
      </a-spin>
    </a-card>

    <!-- 历史版本查看区域 - 移除光盘功能，只保留顶部按钮打开弹窗 -->

    <!-- 历史版本对比弹窗 -->
    <HistoryVersionCompareModal
      v-model:visible="compareModalVisible"
      :protection-group-id="id"
      :current-version-data="data_gis"
      @close="handleCloseCompare"
    />

  </page-wrapper>
</template>
<script lang="ts"></script>
<script lang="ts" setup>
  import { ref, onMounted, onActivated, nextTick, defineOptions, computed } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import { useTabs } from '@/hooks/web/useTabs';
  import { useInfo } from '@/hooks/web/useRestAPI';
  import { useRouter } from 'vue-router';

  import { message } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import {
    BulbOutlined,
    HistoryOutlined,
    ReloadOutlined,
    ExclamationCircleOutlined,
    WarningOutlined,
    ArrowRightOutlined
  } from '@ant-design/icons-vue';
  import { default as gisView } from './gis_opt_road.vue';
  import { useUserStore } from '@/store/modules/user';
  import HistoryVersionCompareModal from './components/HistoryVersionCompareModal.vue';
  import { useUnifiedApi } from '@/hooks/web/useUnifiedApi';
  import { useGo } from '@/hooks/web/usePage';
  import { useMessage } from '@/hooks/web/useMessage';


  // go 用于跳转页面
  const router = useRouter();
  // console.log('id=', router.currentRoute.value.params);
  const id: any = router.currentRoute.value.params.id;

  const infoService = useInfo({
    rootPath: '/graph-rest-api',
  });
  const { info } = infoService;

  const data_gis = ref<any>({});
  const ref_map = ref<any>();
  const loading = ref(false);
  const compareModalVisible = ref(false);

  // 保护组详情数据
  const protectionGroupDetails = ref<any>({});
  const protectionGroupLoading = ref(false);
  const riskAnalysisLoading = ref(false);
  const opticalPathLoading = ref(false);

  // 管道段分组相关（保留用于其他功能）
  const pipeSegmentGroups = ref<any[]>([]);
  const activeGroupTab = ref<string>('0');
  const selectedSegmentId = ref<string>('');

  // 检测步骤配置
  const detectionSteps = ref([
    { key: 'init', title: '初始化检测', description: '准备检测环境和数据', status: 'wait' },
    { key: 'data_fetch', title: '数据获取', description: '获取保护组和光路数据', status: 'wait' },
    { key: 'route_analysis', title: '路由分析', description: '分析光路路由和连接关系', status: 'wait' },
    { key: 'risk_detection', title: '风险检测', description: '检测同光缆、同管道等风险', status: 'wait' },
    { key: 'result_generation', title: '结果生成', description: '生成检测报告和建议', status: 'wait' }
  ]);
  const currentStepIndex = ref(0);

  const confirmInfoService = useInfo({
    rootPath: '/graph-rest-api',
    info: {
      pipeSegment: '',
      ds: '',
      opt_road_list: '',
    },
  });
  const userStore = useUserStore();
  const ds = ref();
  const { setTitle } = useTabs();
  const go = useGo();

  // 初始化统一API调用
  const unifiedApi = useUnifiedApi({
    rootPath: '/graph-rest-api',
    defaultCityCode: userStore.getAreaCode === 'js' ? 'WX' : userStore.getAreaCode
  });

  // 计算光路列表 - 简化数据结构，只保留必要信息
  const opticalPathList = computed(() => {
    if (!data_gis.value.opt_code_list) {
      return [];
    }

    return data_gis.value.opt_code_list.map((code: string, index: number) => {
      return {
        id: index + 1,
        code: code,
        name: code // 使用编码作为名称，简化显示
      };
    });
  });

  // 查询保护组详情
  const fetchProtectionGroupDetails = async () => {
    if (!id) return;

    protectionGroupLoading.value = true;
    try {
      const groupId = id.split('_')[0];
      const cityCode = id.split('_')[1] || (userStore.getAreaCode === 'js' ? 'wx' : userStore.getAreaCode.toLowerCase());

      // 尝试从URL参数中获取 object_code 和 object_name
      const objectCode = router.currentRoute.value.query.objectCode as string;
      const objectName = router.currentRoute.value.query.objectName as string;

      console.log('🎯 调用查看保护组详情接口，记录查看日志: id={}, ds={}, objectCode={}, objectName={}',
                  groupId, cityCode, objectCode, objectName);

      // 使用docreatenew方式调用我们的详情接口，记录查看日志
      const detailService = useInfo({
        rootPath: '/graph-rest-api',
        info: {
          id: groupId,
          ds: cityCode,
          object_code: objectCode,  // 传递 object_code
          object_name: objectName   // 传递 object_name
        }
      });

      const result = await detailService.doCreateNew('/api/opt-group-api/detail');

      console.log('保护组详情API响应:', result);

      if (result && result.data && result.data.length > 0) {
        const groupData = result.data[0];
        protectionGroupDetails.value = {
          id: groupData.id,
          name: groupData.name,
          code: groupData.code,
          areaName: groupData.areaName,
          leafRegion: groupData.leafRegion,
          speciality: groupData.speciality,
          checkResult: groupData.checkResult,
          previousCheckResult: groupData.previousCheckResult,
          checkTime: groupData.checkTime,
          createDate: groupData.createDate,
          confirmStatus: groupData.confirmStatus,
          confirmOp: groupData.confirmOp,
          dealResult: groupData.dealResult,
          exceptionInfo: groupData.exceptionInfo,
          note: groupData.note,
          sourceTypeId: groupData.sourceTypeId,
          stateId: groupData.stateId
        };
        console.log('保护组详情:', protectionGroupDetails.value);
      } else {
        console.warn('未获取到保护组详情数据');
      }
    } catch (error) {
      console.error('查询保护组详情失败:', error);
      message.error('查询保护组详情失败');
    } finally {
      protectionGroupLoading.value = false;
    }
  };

  // 更新检测步骤状态
  const updateStepStatus = (stepKey: string, status: 'wait' | 'process' | 'finish' | 'error') => {
    const step = detectionSteps.value.find(s => s.key === stepKey);
    if (step) {
      step.status = status;
    }
  };

  // 计算当前进度百分比
  const progressPercent = computed(() => {
    const finishedSteps = detectionSteps.value.filter(step => step.status === 'finish').length;
    return Math.round((finishedSteps / detectionSteps.value.length) * 100);
  });
  const confirm = async (pipeSegment, opt_road_list) => {
    console.log('Click on Yes' + pipeSegment);
    confirmInfoService.info.value.creator =
      userStore.getUserInfo.realName + '(' + userStore.getUserInfo.username + ')';
    const ds = info.value.ds;
    confirmInfoService.info.value.ds = ds;
    confirmInfoService.info.value.pipeSegment = pipeSegment;
    confirmInfoService.info.value.opt_road_list = opt_road_list;
    console.log('Click on Yes', ds, opt_road_list, pipeSegment, confirmInfoService.info.value);
    await confirmInfoService.doCreateNew(`/api/opt-group-api/white-insert`);
    await getInfo();
  };

  const cancel = async (pipeSegment, opt_road_list) => {
    console.log('这里----');
    const ds = info.value.ds;
    confirmInfoService.info.value.ds = ds;
    confirmInfoService.info.value.pipeSegment = pipeSegment;
    confirmInfoService.info.value.opt_road_list = opt_road_list;
    console.log('Click on Yes', ds, opt_road_list, pipeSegment, confirmInfoService.info.value);
    await confirmInfoService.doCreateNew(`/api/opt-group-api/white-delete`);
    await getInfo();
  };

  const getInfo = async () => {
    // 开始loading
    riskAnalysisLoading.value = true;
    opticalPathLoading.value = true;

    // 重置所有步骤状态
    detectionSteps.value.forEach(step => step.status = 'wait');
    currentStepIndex.value = 0;

    try {
      // 步骤1: 初始化检测
      updateStepStatus('init', 'process');
      console.log('开始分析风险--');
      // ref_map.value.setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟初始化时间
      updateStepStatus('init', 'finish');

      // 步骤2: 数据获取
      updateStepStatus('data_fetch', 'process');
      await new Promise(resolve => setTimeout(resolve, 800)); // 模拟数据获取时间
      updateStepStatus('data_fetch', 'finish');

      // 步骤3: 路由分析
      updateStepStatus('route_analysis', 'process');
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟路由分析时间
      updateStepStatus('route_analysis', 'finish');

      // 步骤4: 风险检测
      updateStepStatus('risk_detection', 'process');
      let result = await infoService.doCreateNew('/api/opt-group-api/risk-analyze-previous');
      updateStepStatus('risk_detection', 'finish');

      // 步骤5: 结果生成
      updateStepStatus('result_generation', 'process');
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟结果生成时间

      console.log('result', result);
      data_gis.value = result;
      current_status.value = '';
      updateStepStatus('result_generation', 'finish');

      // 生成管道段分组（保留用于其他功能）
      generatePipeSegmentGroups();

      // 光路详情数据加载完成
      opticalPathLoading.value = false;

      console.log('gis_result:', result);
    } catch (error) {
      console.error('检测过程出错:', error);
      // 将当前步骤标记为错误
      const currentStep = detectionSteps.value.find(step => step.status === 'process');
      if (currentStep) {
        currentStep.status = 'error';
      }
      message.error('检测过程出错，请重试');
    } finally {
      // 结束loading
      riskAnalysisLoading.value = false;
      opticalPathLoading.value = false;
    }
  };

  const optRoadClick = async (code) => {
    // const pipeSegments = ref<any>([]);
    const pipeSegments = data_gis.value.pipeSegments.filter((item) =>
      item.opt_road_list.some((opt) => opt.code === code),
    );
    nextTick(() => {
      ref_map.value.hightlights_opt(pipeSegments);
    });
  };
  const pipeSegmentClick = async (pipeSegment) => {
    nextTick(() => {
      ref_map.value.hightlights([pipeSegment]);
    });
  };
  const current_status = ref<any>();

  const risk_analyse = async () => {
    current_status.value = '';
    info.value.id = id.split('_')[0];
    info.value.ds = id.split('_')[1];
    info.value.cached = true;
    console.log('info', info);

    // 并行查询保护组详情和风险分析
    await Promise.all([
      fetchProtectionGroupDetails(),
      getInfo()
    ]);

    setTitle(
      '双路由稽核:' +
        (data_gis.value.title || protectionGroupDetails.value.name || '未知保护组') +
        '(' +
        (info.value.ds ? info.value.ds : '') +
        ')',
    );
    nextTick(() => {
      ref_map.value.drawDatas(data_gis.value);
    });
  };

  const re_risk_analyse = async () => {
    current_status.value = '';
    info.value.id = id.split('_')[0];
    info.value.ds = id.split('_')[1];
    info.value.cached = false;
    console.log('info', info);
    await getInfo();
    setTitle(
      '双路由稽核:' +
        (data_gis.value.title ? data_gis.value.title : '') +
        '(' +
        (info.value.ds ? info.value.ds : '') +
        ')',
    );
    nextTick(() => {
      ref_map.value.drawDatas(data_gis.value);
    });
  };

  onMounted(async () => {
    risk_analyse();
    ds.value = id.split('_')[1];
  });
  onActivated(async () => {
    current_status.value = '';
  });

  const activeKey = ref(['1']);

  // 新增的方法函数
  const handleSmartRouteRecommendation = () => {
    const cityCode = id.split('_')[1] || ds.value;

    console.log('opticalPathList',opticalPathList.value);

    const opticalPathListString = opticalPathList.value
    .map((element: any) => element.code)      // 提取每个元素的code字段
    .filter((code: any) => code !== undefined && code !== null && code !== '')  // 过滤掉无效的code
    .join(',');

    go(`/nrm/res-app/risk-manage/smart-route-page/${opticalPathListString}/${cityCode}`);
  };

  // 打开历史版本对比弹窗
  const handleHistoryVersions = () => {
    compareModalVisible.value = true;
  };

  const handleRefresh = async () => {
    // 复用opt_road_group_management.vue中的检测逻辑
    const { createMessage, createConfirm } = useMessage();

    createConfirm({
      iconType: 'warning',
      title: '确认重新检测',
      content: `即将对当前保护组进行重新检测分析。

⚠️ 重要提醒：
• 此操作将重新分析当前保护组的风险状况
• 预计耗时：1-5分钟
• 检测期间请勿关闭页面
• 检测完成后页面将自动刷新

是否确认开始检测？`,
      okText: '确认检测',
      cancelText: '取消',
      onOk: async () => {
        await performRiskAnalyze();
      },
    });
  };

  // 执行风险检测 - 复用opt_road_group_management.vue中的riskAnalyze逻辑
  const performRiskAnalyze = async () => {
    try {
      loading.value = true;
      riskAnalysisLoading.value = true;
      opticalPathLoading.value = true;

      // 重置所有检测步骤状态
      detectionSteps.value.forEach(step => {
        step.status = 'wait';
      });

      const groupId = id.split('_')[0];
      const cityCode = id.split('_')[1] || ds.value;

      // 设置检测参数 - 与opt_road_group_management.vue保持一致
      infoService.info.value.ds = cityCode;
      infoService.info.value.id = groupId;
      infoService.info.value.group_name = protectionGroupDetails.value.name || data_gis.value.title;

      // 从URL参数或保护组详情中获取 object_code 和 object_name
      const objectCode = router.currentRoute.value.query.objectCode as string ||
                        protectionGroupDetails.value.code ||
                        data_gis.value.code;
      const objectName = router.currentRoute.value.query.objectName as string ||
                        protectionGroupDetails.value.name ||
                        data_gis.value.title;

      infoService.info.value.code = objectCode;
      infoService.info.value.name = objectName;

      console.log('🎯 详情页面风险检测: id={}, code={}, name={}, ds={}',
                  groupId, objectCode, objectName, cityCode);

      message.info('开始检测，稍等！');

      // 步骤1: 初始化检测
      updateStepStatus('init', 'process');
      await new Promise(resolve => setTimeout(resolve, 500));
      updateStepStatus('init', 'finish');

      // 步骤2: 数据获取
      updateStepStatus('data_fetch', 'process');
      await new Promise(resolve => setTimeout(resolve, 800));
      updateStepStatus('data_fetch', 'finish');

      // 步骤3: 路由分析
      updateStepStatus('route_analysis', 'process');

      // 调用风险检测API - 与opt_road_group_management.vue保持一致
      await infoService.doCreateNew('/api/opt-group-api/risk-analyze');

      updateStepStatus('route_analysis', 'finish');

      // 步骤4: 风险检测
      updateStepStatus('risk_detection', 'process');
      await new Promise(resolve => setTimeout(resolve, 1000));
      updateStepStatus('risk_detection', 'finish');

      // 步骤5: 结果生成
      updateStepStatus('result_generation', 'process');
      await new Promise(resolve => setTimeout(resolve, 500));
      updateStepStatus('result_generation', 'finish');

      message.success('检测完成');

      // 重新加载数据
      await re_risk_analyse();
      await fetchProtectionGroupDetails();

    } catch (error) {
      console.error('风险检测失败:', error);
      message.error('风险检测失败，请重试');

      // 将当前处理中的步骤标记为错误
      const currentStep = detectionSteps.value.find(step => step.status === 'process');
      if (currentStep) {
        currentStep.status = 'error';
      }
    } finally {
      loading.value = false;
      riskAnalysisLoading.value = false;
      opticalPathLoading.value = false;
    }
  };

  const highlightOpticalPath = (code: string) => {
    optRoadClick(code);
  };

  // 获取非局前井管道段
  const getNonWellPipeSegments = (pipeSegments: any[]) => {
    if (!pipeSegments) return [];
    return pipeSegments.filter(segment => !segment.code.includes('局前井'));
  };

  // 获取局前井管道段
  const getWellPipeSegments = (pipeSegments: any[]) => {
    if (!pipeSegments) return [];
    return pipeSegments.filter(segment => segment.code.includes('局前井'));
  };

  // 生成管道段分组
  const generatePipeSegmentGroups = () => {
    if (!data_gis.value.pipeSegments) {
      pipeSegmentGroups.value = [];
      return;
    }

    // 按光路组合分组
    const groupMap = new Map();

    data_gis.value.pipeSegments.forEach((segment: any) => {
      if (segment.opt_road_list && segment.opt_road_list.length > 0) {
        // 使用光路列表作为分组键
        const optRoadCodes = segment.opt_road_list.map((opt: any) => opt.code).sort().join(',');

        if (!groupMap.has(optRoadCodes)) {
          groupMap.set(optRoadCodes, {
            title: `光路组合: ${segment.opt_road_list.map((opt: any) => opt.code).join(' + ')}`,
            optRoadCodes: segment.opt_road_list.map((opt: any) => opt.code),
            segments: []
          });
        }

        groupMap.get(optRoadCodes).segments.push(segment);
      }
    });

    pipeSegmentGroups.value = Array.from(groupMap.values());

    // 默认选择第一个分组
    if (pipeSegmentGroups.value.length > 0) {
      activeGroupTab.value = '0';
    }
  };

  // 处理分组tab切换
  const handleGroupTabChange = (activeKey: string) => {
    activeGroupTab.value = activeKey;
    selectedSegmentId.value = '';
  };

  // 处理管道段点击
  const handleSegmentClick = (segment: any) => {
    selectedSegmentId.value = segment.id;
    pipeSegmentClick(segment);
  };

  // 在地图上定位管道段
  const locateSegmentOnMap = (segment: any) => {
    selectedSegmentId.value = segment.id;
    pipeSegmentClick(segment);

    // 可以添加地图缩放到该区域的逻辑
    if (ref_map.value && ref_map.value.zoomToSegment) {
      ref_map.value.zoomToSegment(segment);
    }
  };



  const getStatusColor = (status: string) => {
    if (!status) return 'default';

    switch (status) {
      case '正常':
        return 'green';
      case '同光缆':
        return 'orange';
      case '同管道':
        return 'red';
      case '同光缆同管道':
        return 'red';
      case '局内光路':
        return 'orange';
      case '未检测':
        return 'default';
      case '检测中':
        return 'processing';
      default:
        return 'default';
    }
  };

  const getAlertType = (checkResult: string) => {
    switch (checkResult) {
      case '正常':
        return 'success';
      case '同光缆':
      case '同管道':
        return 'error';
      case '局内光路':
        return 'warning';
      default:
        return 'info';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'S':
        return 'red';
      case 'A':
        return 'orange';
      case 'B':
        return 'blue';
      case 'C':
        return 'green';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '未知';
    return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
  };

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '未知';
    return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
  };

  // 获取处理结果颜色
  const getDealResultColor = (dealResult: string) => {
    if (!dealResult) return 'default';

    switch (dealResult) {
      case '已整改':
        return 'green';
      case '待整改':
        return 'orange';
      case '无需整改':
        return 'blue';
      case '整改中':
        return 'processing';
      default:
        return 'default';
    }
  };

  const handleCloseCompare = () => {
    compareModalVisible.value = false;
  };

  defineOptions({ name: 'RiskShowOptRoad' });
</script>
<style scoped>
  .clickable-text {
    cursor: pointer;
  }

  .page-header-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .problem-details {
    padding: 16px 0;
  }

  .pipe-segment-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .pipe-segments-list {
    margin-top: 12px;
  }

  .pipe-segments-scroll {
    margin-top: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 12px;
    background: #fafafa;
  }

  /* 光路详情标签样式 */
  .optical-path-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
  }

  .optical-path-tag {
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    padding: 4px 8px;
    border-radius: 4px;
  }

  .optical-path-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  }

  .path-index {
    display: inline-block;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    font-size: 11px;
    font-weight: bold;
    margin-right: 6px;
  }

  /* 管道段分类样式 */
  .pipe-segments-classified {
    margin-top: 12px;
  }

  .pipe-category {
    margin-bottom: 16px;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;
    border-left: 4px solid #d9d9d9;
  }

  .pipe-category:last-child {
    margin-bottom: 0;
  }

  .pipe-category:has(.category-title:contains("同管道段")) {
    border-left-color: #ff4d4f;
    background: #fff2f0;
  }

  .pipe-category:has(.category-title:contains("同局前井")) {
    border-left-color: #faad14;
    background: #fffbe6;
  }

  .category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .category-title {
    font-weight: 600;
    color: #262626;
    font-size: 14px;
  }

  .category-content {
    margin-top: 8px;
  }

  .pipe-segment-tag {
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 4px;
  }

  .pipe-segment-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
  }

  /* 全屏地图容器 */
  .map-container-full {
    width: 100%;
    height: 600px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    overflow: hidden;
    position: relative;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .map-container-full {
      height: 400px;
    }
  }

  /* 历史版本CD光盘样式 */
  .history-version-container {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .version-timeline {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 16px;
  }

  .cd-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #1890ff, #40a9ff);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
  }

  .cd-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }

  .cd-icon::before {
    content: '';
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: white;
    position: absolute;
  }

  .cd-icon.active {
    background: linear-gradient(45deg, #52c41a, #73d13d);
  }

  .version-label {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    text-align: center;
  }
</style>
