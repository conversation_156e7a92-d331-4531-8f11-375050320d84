<script setup lang="ts">
  import { defineEmits, defineProps, onActivated, onMounted, ref, watchEffect } from 'vue';
  import { useVModel } from '@/hooks/web/useModel';
  import { useInfo } from '@/hooks/web/useRestAPI';

  const props = defineProps<{ info: any; show: boolean }>();

  const emit = defineEmits(['update:show', 'onFinish']);
  const show = useVModel(props, emit, 'show');

  const infoService = useInfo({
    rootPath: '/graph-rest-api',
    info: {
      ds: '',
      id: '',
      opt_code: '',
      opt_name: '',
      group_id: '',
    },
    responseType: 'blob',
  });

  // eslint-disable-next-line vue/no-dupe-keys
  const { info } = infoService;

  /*
  const filter = ref({
    opt_code: '',
    opt_name: '',
  });
   */

  watchEffect(() => {
    console.log('props.info', props.info);
    if (props.info.group_id) {
      info.value.opt_code = props.info.opt_code;
      info.value.opt_name = props.info.opt_name;
      info.value.route_no = props.info.route_no;
      info.value.group_id = props.info.group_id;
      info.value.code = props.info.code;
      info.value.id = props.info.id;
    } else {
      info.value.group_id = props.info.id;
      info.value.opt_code = '';
      info.value.opt_name = '';
      info.value.route_no = '';
    }
  });

  onMounted(() => {});

  onActivated(async () => {});
  const onClose = async () => {
    show.value = false;
    emit('onFinish');
  };

  const onSubmit = async () => {
    if (props.info.areaName) {
      infoService.info.value.ds = props.info.areaName;
    } else {
      infoService.info.value.ds = props.info.ds;
    }

    // 如果有before_data，添加到提交数据中
    if (props.info.before_data) {
      infoService.info.value.before_data = props.info.before_data;
      console.log('🎯 光路元素修改包含修改前数据:', props.info.before_data);
    }

    await infoService.doCreateNew(`/api/opt-group-api/save_opt_group_element`);
    onClose();
  };

  // const query = async () => {};
</script>

<template>
  <a-modal v-model:visible="show" :title="'增加光路'" :width="800">
    <template #footer>
      <a-button key="back" @click="onSubmit">提交</a-button>
    </template>
    <a-card>
      <a-form :model="info" name="horizontal_login" layout="horizontal" autocomplete="off">
        <a-form-item :name="['opt_code']" label="光路编码" :rules="[{ required: true }]">
          <a-input v-model:value="info.opt_code" />
        </a-form-item>
        <a-form-item :name="['route_no']" label="路由号" :rules="[{ required: true }]">
          <a-input v-model:value="info.route_no" />
        </a-form-item>
      </a-form>
    </a-card>
  </a-modal>
</template>

<style scoped lang="less"></style>
