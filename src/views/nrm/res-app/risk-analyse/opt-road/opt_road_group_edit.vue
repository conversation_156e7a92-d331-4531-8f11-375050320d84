<script setup lang="ts">
  import { defineEmits, defineProps, onActivated, onMounted, ref, watchEffect } from 'vue';
  import { useVModel } from '@/hooks/web/useModel';
  import { useInfo } from '@/hooks/web/useRestAPI';
  import { useMessage } from '@/hooks/web/useMessage';

  const props = defineProps<{ dictionary: any; filter: any; info: any; show: boolean }>();

  const emit = defineEmits(['update:show', 'onFinish']);
  const show = useVModel(props, emit, 'show');

  // 表单引用和消息提示
  const formRef = ref();
  const { createMessage } = useMessage();

  // 提交loading状态
  const submitLoading = ref(false);

  watchEffect(() => {
    //note.value = props.info.note
  });

  onMounted(() => {
    // const dictionary = props.dictionary;
    info.value = props.info;
    // console.log('dic', dictionary);
    // query();
  });

  onActivated(async () => {
    // query();
  });
  const onClose = async () => {
    // 重置表单
    formRef.value?.resetFields();
    // 重置loading状态
    submitLoading.value = false;
    // 关闭弹窗
    show.value = false;
    emit('onFinish');
  };

  const infoService = useInfo({
    rootPath: '/graph-rest-api',
    info: {
      name: '',
      code: '',
      area_name: '',
      leafRegion: '',
      speciality: '',
      customer: '',
      maintainers: '',
      note: '',
      ds: '',
    },
    responseType: 'blob',
  });

  const { info } = infoService;
  /*
  const filter = ref({
    area_name: '',
    name: '',
    code: '',
    ds: '',
  });
   */
  // 表单校验规则
  const formRules = {
    area_name: [
      { required: true, message: '请选择地市', trigger: 'change' }
    ],
    name: [
      { required: true, message: '请输入光路组名称', trigger: 'blur' },
      { min: 2, max: 50, message: '光路组名称长度应在2-50个字符之间', trigger: 'blur' }
    ],
    code: [
      { required: true, message: '请输入光路组编码', trigger: 'blur' },
      { min: 2, max: 100, message: '光路组编码长度应在2-100个字符之间', trigger: 'blur' },
      // { pattern: /^[A-Za-z0-9_-]+$/, message: '光路组编码只能包含字母、数字、下划线和横线', trigger: 'blur' }
    ],
    leafRegion: [
      { max: 20, message: '区县名称不能超过20个字符', trigger: 'blur' }
    ],
    speciality: [
      { max: 20, message: '专业名称不能超过20个字符', trigger: 'blur' }
    ],
    customer: [
      { max: 100, message: '关联客户不能超过100个字符', trigger: 'blur' }
    ],
    maintainers: [
      { max: 100, message: '维护人员不能超过100个字符', trigger: 'blur' }
    ],
    note: [
      { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
    ]
  };

  const onSubmit = async () => {
    try {
      // 表单校验
      await formRef.value?.validate();

      // 设置提交loading状态
      submitLoading.value = true;

      // 设置地市信息
      info.value.ds = props.filter.ds;

      // 如果有before_data，添加到提交数据中
      if (props.info.before_data) {
        info.value.before_data = props.info.before_data;
        console.log('🎯 包含修改前数据:', props.info.before_data);
      }

      console.log('🔍 提交表单数据完整内容:', JSON.stringify(infoService.info.value, null, 2));
      console.log('🔍 各字段值检查:');
      console.log('  - code:', info.value.code);
      console.log('  - name:', info.value.name);
      console.log('  - note:', info.value.note);
      console.log('  - area_name:', info.value.area_name);
      console.log('  - leafRegion:', info.value.leafRegion);
      console.log('  - speciality:', info.value.speciality);
      console.log('  - customer:', info.value.customer);
      console.log('  - maintainers:', info.value.maintainers);

      // 提交数据
      await infoService.doCreateNew(`/api/opt-group-api/save_opt_group`);

      // 成功提示
      createMessage.success('光路组保存成功！');

      // 关闭弹窗
      await onClose();

    } catch (error) {
      console.error('表单校验或提交失败:', error);

      // 如果是校验错误，显示校验提示
      if (error?.errorFields) {
        createMessage.warning('请检查表单填写是否正确');
        return;
      }

      // 其他错误显示通用提示
      createMessage.error('保存失败，请重试');
    } finally {
      // 重置loading状态
      submitLoading.value = false;
    }
  };

  watchEffect(() => {
    info.value = props.info;
  });

  // const query = async () => {};
</script>

<template>
  <a-modal v-model:visible="show" :title="'新增/修改光路组'" :width="800" :confirmLoading="submitLoading">
    <template #footer>
      <a-button key="cancel" @click="show = false">取消</a-button>
      <a-button key="submit" type="primary" :loading="submitLoading" @click="onSubmit">
        提交
      </a-button>
    </template>
    <a-card>
      <a-form
        ref="formRef"
        :model="info"
        :rules="formRules"
        name="optGroupForm"
        layout="horizontal"
        autocomplete="off"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item name="area_name" label="地市">
          <a-select
            v-model:value="info.area_name"
            placeholder="请选择地市"
            :filterOption="
              (input: string, option: any) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }
            "
            show-search
            class="rounded-input"
          >
            <a-select-option
              v-for="item in props.dictionary.cityList"
              :key="item.name"
              :value="item.name"
              :label="item.name"
              >{{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item name="leafRegion" label="区县">
          <a-input v-model:value="info.leafRegion" placeholder="请输入区县名称" />
        </a-form-item>

        <a-form-item name="name" label="光路组名称">
          <a-input v-model:value="info.name" placeholder="请输入光路组名称" />
        </a-form-item>

        <a-form-item name="code" label="光路组编码">
          <a-input v-model:value="info.code" placeholder="请输入光路组编码" />
        </a-form-item>

        <a-form-item name="speciality" label="专业">
          <a-input v-model:value="info.speciality" placeholder="请输入专业" />
        </a-form-item>

        <a-form-item name="customer" label="关联客户">
          <a-input v-model:value="info.customer" placeholder="请输入关联客户" />
        </a-form-item>

        <a-form-item name="maintainers" label="维护人员">
          <a-input v-model:value="info.maintainers" placeholder="请输入维护人员" />
        </a-form-item>

        <a-form-item name="note" label="备注">
          <a-textarea
            v-model:value="info.note"
            placeholder="请输入备注信息"
            :rows="3"
            :maxlength="200"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-card>
  </a-modal>
</template>

<style scoped lang="less"></style>
