<template>
  <div class="dual-route-log-analytics">
    <!-- 精美页面头部 -->
    <div class="elegant-header">
      <div class="header-background">
        <div class="bg-pattern"></div>
      </div>
      <div class="header-content">
        <div class="title-section">
          <div class="title-icon">
            <bar-chart-outlined />
          </div>
          <div class="title-text">
            <h1 class="page-title">双路由操作日志分析</h1>
            <p class="page-subtitle">操作行为分析、热度统计、用户活跃度分析</p>
          </div>
        </div>

        <div class="control-section">
          <div class="time-control">
            <label class="control-label">
              <calendar-outlined />
              时间范围
            </label>
            <a-range-picker v-model:value="dateRange" format="YYYY-MM-DD" placeholder="选择时间范围"
              @change="onDateRangeChange" class="elegant-range-picker" />
          </div>

          <div class="city-control">
            <label class="control-label">
              <environment-outlined />
              地市选择
            </label>
            <a-select v-model:value="selectedCityCode" placeholder="选择地市" style="width: 150px" @change="onCityChange">
              <a-select-option value="">全省</a-select-option>
              <a-select-option value="wx">无锡市</a-select-option>
              <a-select-option value="nj">南京市</a-select-option>
              <a-select-option value="sz">苏州市</a-select-option>
              <a-select-option value="xz">徐州市</a-select-option>
              <a-select-option value="cz">常州市</a-select-option>
              <a-select-option value="nt">南通市</a-select-option>
              <a-select-option value="lyg">连云港市</a-select-option>
              <a-select-option value="ha">淮安市</a-select-option>
              <a-select-option value="yc">盐城市</a-select-option>
              <a-select-option value="yz">扬州市</a-select-option>
              <a-select-option value="zj">镇江市</a-select-option>
              <a-select-option value="tz">泰州市</a-select-option>
              <a-select-option value="sq">宿迁市</a-select-option>
            </a-select>
          </div>

          <div class="action-buttons">
            <a-button type="primary" @click="refreshAnalytics" :loading="loading.summary">
              <template #icon><reload-outlined /></template>
              刷新数据
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志分析主要内容 -->
    <div class="main-content">
      <!-- 加载状态 -->
      <div class="loading-section" v-if="loading.summary">
        <a-spin size="large" tip="正在加载日志分析数据...">
          <div class="loading-placeholder"></div>
        </a-spin>
      </div>

      <!-- 分析结果展示 -->
      <div class="analytics-content" v-else>
        <!-- 概览统计卡片 -->
        <div class="overview-cards">
          <div class="stat-card">
            <div class="stat-icon">
              <bar-chart-outlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ analyticsData.totalOperations || 0 }}</div>
              <div class="stat-label">总操作次数</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <user-outlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ analyticsData.activeUsers || 0 }}</div>
              <div class="stat-label">活跃用户数</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <fire-outlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ analyticsData.peakHour || '--' }}</div>
              <div class="stat-label">操作高峰时段</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <thunderbolt-outlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ analyticsData.mostActiveUser || '--' }}</div>
              <div class="stat-label">最活跃用户</div>
            </div>
          </div>
        </div>

        <!-- 图表分析区域 -->
        <div class="charts-section">
          <!-- 时间分布分析 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3 class="chart-title">
                <clock-circle-outlined />
                操作时间分布分析
              </h3>
              <div class="chart-controls">
                <a-radio-group v-model:value="timeAnalysisType" @change="refreshTimeAnalysis">
                  <a-radio-button value="hour">按小时</a-radio-button>
                  <a-radio-button value="day">按天</a-radio-button>
                  <a-radio-button value="week">按周</a-radio-button>
                </a-radio-group>
              </div>
            </div>
            <div class="chart-content">
              <div ref="timeDistributionChart" class="chart-container"></div>
            </div>
          </div>

          <!-- 操作类型分析 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3 class="chart-title">
                <pie-chart-outlined />
                操作类型分布
              </h3>
            </div>
            <div class="chart-content">
              <div ref="operationTypeChart" class="chart-container"></div>
            </div>
          </div>
        </div>

        <!-- 用户活跃度分析 -->
        <div class="user-analysis-section">
          <div class="chart-card full-width">
            <div class="chart-header">
              <h3 class="chart-title">
                <team-outlined />
                用户活跃度分析
              </h3>
              <div class="chart-controls">
                <a-select v-model:value="userAnalysisType" @change="refreshUserAnalysis" style="width: 120px">
                  <a-select-option value="activity">活跃度</a-select-option>
                  <a-select-option value="frequency">操作频次</a-select-option>
                  <a-select-option value="types">操作类型</a-select-option>
                </a-select>
              </div>
            </div>
            <div class="chart-content">
              <div ref="userActivityChart" class="chart-container"></div>
            </div>
          </div>
        </div>

        <!-- 热度分析 -->
        <div class="heatmap-section">
          <div class="chart-card full-width">
            <div class="chart-header">
              <h3 class="chart-title">
                <fire-outlined />
                操作热度分析
              </h3>
              <div class="chart-subtitle">按时间段和操作类型的热度分布</div>
            </div>
            <div class="chart-content">
              <div ref="heatmapChart" class="chart-container"></div>
            </div>
          </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="data-table-section">
          <div class="table-card">
            <div class="table-header">
              <h3 class="table-title">
                <table-outlined />
                操作日志详细数据
              </h3>
              <div class="table-controls">
                <a-input-search v-model:value="searchKeyword" placeholder="搜索操作描述、用户名等" style="width: 250px"
                  @search="onSearch" />
                <a-button @click="exportData" :loading="loading.export">
                  <template #icon><download-outlined /></template>
                  导出数据
                </a-button>
              </div>
            </div>
            <div class="table-content">
              <a-table :columns="tableColumns" :data-source="tableData" :pagination="tablePagination"
                :loading="loading.table" @change="onTableChange" size="small" :scroll="{ x: 1200 }">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'operation_time'">
                    {{ formatDateTime(record.operation_time) }}
                  </template>
                  <template v-else-if="column.key === 'operation_type'">
                    <a-tag :color="getOperationTypeColor(record.operation_type)">
                      {{ getOperationTypeName(record.operation_type) }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'object_type'">
                    <a-tag color="blue">{{ getObjectTypeName(record.object_type) }}</a-tag>
                  </template>
                  <template v-else-if="column.key === 'operation_result'">
                    <a-tag :color="record.operation_result === 'success' ? 'green' : 'red'">
                      {{ record.operation_result === 'success' ? '成功' : '失败' }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-button type="link" size="small" @click="viewLogDetail(record)">
                      查看详情
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-content">
      <div v-if="getCurrentCityData('statusDistribution')" class="status-full-grid">
        <div class="status-full-item" v-for="(item, index) in getCurrentCityData('statusDistribution')" :key="index">
          <div class="status-full-indicator" :style="{ backgroundColor: getStatusColor(item.status) }"></div>
          <div class="status-full-info">
            <div class="status-full-name">{{ item.status }}</div>
            <div class="status-full-count">{{ item.count }}个</div>
            <div class="status-full-percentage">{{ item.percentage }}%</div>
          </div>
          <div class="status-full-bar">
            <div class="status-bar-fill" :style="{
              width: item.percentage + '%',
              backgroundColor: getStatusColor(item.status)
            }"></div>
          </div>
        </div>
      </div>
      <div v-else class="no-data">暂无状态分布数据</div>
    </div>
  </div>

  <!-- 综合分析区域 -->
  <div class="comprehensive-analysis-section">
    <!-- 状态改善分析 -->
    <div class="improvement-analysis-card">
      <div class="card-header">
        <h4 class="card-title">
          <rise-outlined />
          状态改善分析
        </h4>
        <div class="card-subtitle">各维度转为正常状态统计</div>
      </div>
      <div class="card-content">
        <div class="improvement-charts-grid">
          <div class="chart-container">
            <div class="chart-title">专业改善统计</div>
            <div class="specialty-improvement-chart" ref="specialtyImprovementChart"></div>
          </div>
          <div class="chart-container">
            <div class="chart-title">区县改善统计</div>
            <div class="district-improvement-chart" ref="districtImprovementChart"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 问题状态分析 -->
    <div class="problem-analysis-card">
      <div class="card-header">
        <h4 class="card-title">
          <warning-outlined />
          问题状态分析
        </h4>
        <div class="card-subtitle">各维度问题状态分布与趋势</div>
      </div>
      <div class="card-content">
        <div class="problem-charts-grid">
          <div class="chart-container">
            <div class="chart-title">专业问题分布</div>
            <div class="specialty-problem-chart" ref="specialtyProblemChart"></div>
          </div>
          <div class="chart-container">
            <div class="chart-title">区县问题分布</div>
            <div class="district-problem-chart" ref="districtProblemChart"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 联合分析 -->
    <div class="joint-analysis-card">
      <div class="card-header">
        <h4 class="card-title">
          <apartment-outlined />
          专业-区县联合分析
        </h4>
        <div class="card-subtitle">问题状态在专业与区县维度的交叉分析</div>
      </div>
      <div class="card-content">
        <div class="joint-analysis-container">
          <div class="chart-container large">
            <div class="chart-title">专业-区县问题热力图</div>
            <div class="joint-heatmap-chart" ref="jointHeatmapChart"></div>
          </div>
          <div class="chart-container large">
            <div class="chart-title">状态变化趋势对比</div>
            <div class="trend-comparison-chart" ref="trendComparisonChart"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 状态转移图表 -->
  <div class="chart-section" v-if="!currentCityLoading && hasCurrentCityData()">
    <div class="section-header">
      <h3 class="section-title">
        <swap-outlined />
        {{ getCurrentCityName() }} 状态转移关系分析
      </h3>
      <div class="section-controls">
        <a-radio-group v-model:value="chartType" @change="onChartTypeChange">
          <a-radio-button value="graph">力导向图</a-radio-button>
          <a-radio-button value="circular">环形关系图</a-radio-button>
          <a-radio-button value="heatmap">热力图</a-radio-button>
        </a-radio-group>
        <a-button @click="refreshChartData" :loading="loading.chart">
          <template #icon><reload-outlined /></template>
          刷新图表
        </a-button>
      </div>
    </div>
    <div class="chart-container-wrapper">
      <div ref="statusTransitionChart" class="status-chart"></div>
    </div>
  </div>

  <!-- 变化统计清单表格 -->
  <div class="detail-table-section" v-if="!currentCityLoading && hasCurrentCityData()">
    <div class="section-header">
      <h3 class="section-title">
        <table-outlined />
        {{ getCurrentCityName() }} 状态变化详细清单
      </h3>
      <div class="section-controls">
        <span class="total-count">共 {{ getApiDataCount('changeStats') }} 条记录</span>
        <a-button @click="refreshCurrentCity" :loading="currentCityLoading">
          <template #icon><reload-outlined /></template>
          刷新数据
        </a-button>
      </div>
    </div>

    <!-- 状态筛选器 -->
    <div class="filter-section">
      <div class="filter-tabs">
        <a-radio-group v-model:value="filterMode" @change="onFilterModeChange" class="filter-mode-selector">
          <a-radio-button value="status">具体状态筛选</a-radio-button>
          <a-radio-button value="type">转移类型筛选</a-radio-button>
        </a-radio-group>
      </div>

      <!-- 具体状态筛选 -->
      <div class="filter-row" v-if="filterMode === 'status'">
        <div class="filter-group">
          <label class="filter-label">源状态：</label>
          <a-select v-model:value="statusFilter.fromStatus" placeholder="选择源状态" style="width: 120px" allowClear
            @change="onFilterChange">
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="正常">正常</a-select-option>
            <a-select-option value="单路由">单路由</a-select-option>
            <a-select-option value="同管道">同管道</a-select-option>
            <a-select-option value="同光缆">同光缆</a-select-option>
            <a-select-option value="无光路">无光路</a-select-option>
            <a-select-option value="缺管道">缺管道</a-select-option>
          </a-select>
        </div>

        <div class="filter-group">
          <label class="filter-label">目标状态：</label>
          <a-select v-model:value="statusFilter.toStatus" placeholder="选择目标状态" style="width: 120px" allowClear
            @change="onFilterChange">
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="正常">正常</a-select-option>
            <a-select-option value="单路由">单路由</a-select-option>
            <a-select-option value="同管道">同管道</a-select-option>
            <a-select-option value="同光缆">同光缆</a-select-option>
            <a-select-option value="无光路">无光路</a-select-option>
            <a-select-option value="缺管道">缺管道</a-select-option>
          </a-select>
        </div>

        <div class="filter-description">
          <a-tooltip title="筛选从指定源状态转移到指定目标状态的记录，可以精确定位特定的状态转移路径" placement="top">
            <question-circle-outlined class="help-icon" />
          </a-tooltip>
        </div>

        <div class="filter-group">
          <a-button type="primary" @click="applyFilter" :loading="filterLoading">
            <template #icon><search-outlined /></template>
            筛选
          </a-button>
          <a-button @click="resetFilter" style="margin-left: 8px">
            <template #icon><clear-outlined /></template>
            重置
          </a-button>
        </div>
      </div>

      <!-- 转移类型筛选 -->
      <div class="filter-row" v-if="filterMode === 'type'">
        <div class="filter-group">
          <label class="filter-label">转移类型：</label>
          <a-select v-model:value="statusFilter.transitionType" placeholder="选择转移类型" style="width: 150px" allowClear
            @change="onFilterChange">
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="improved">
              <span style="color: #52c41a;">✓ 状态改善</span>
              <div style="font-size: 11px; color: #999;">非正常 → 正常</div>
            </a-select-option>
            <a-select-option value="deteriorated">
              <span style="color: #ff4d4f;">✗ 状态恶化</span>
              <div style="font-size: 11px; color: #999;">正常 → 非正常</div>
            </a-select-option>
            <a-select-option value="maintained">
              <span style="color: #1890ff;">= 状态保持</span>
              <div style="font-size: 11px; color: #999;">状态无变化</div>
            </a-select-option>
            <a-select-option value="changed">
              <span style="color: #faad14;">⇄ 状态变化</span>
              <div style="font-size: 11px; color: #999;">任何状态变化</div>
            </a-select-option>
          </a-select>
        </div>

        <div class="filter-description">
          <a-tooltip title="按状态变化的性质筛选：改善（转为正常）、恶化（正常转为异常）、保持（无变化）、变化（任何变化）" placement="top">
            <question-circle-outlined class="help-icon" />
          </a-tooltip>
        </div>

        <div class="filter-group">
          <a-button type="primary" @click="applyFilter" :loading="filterLoading">
            <template #icon><search-outlined /></template>
            筛选
          </a-button>
          <a-button @click="resetFilter" style="margin-left: 8px">
            <template #icon><clear-outlined /></template>
            重置
          </a-button>
        </div>
      </div>
    </div>
    <div class="table-container">
      <a-table :columns="tableColumns" :data-source="getCurrentCityData('changeStats')" :loading="currentCityLoading"
        :pagination="tablePagination" size="middle" :scroll="{ x: 1200 }" class="detail-table" @change="onTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status_transition'">
            <div class="status-transition">
              <a-tag :color="getStatusColor(record.上一次检测结果)" size="small">
                {{ record.上一次检测结果 || '未知' }}
              </a-tag>
              <span class="transition-arrow">→</span>
              <a-tag :color="getStatusColor(record.最新检测结果)" size="small">
                {{ record.最新检测结果 || '未知' }}
              </a-tag>
            </div>
          </template>
          <template v-if="column.key === 'transition_type'">
            <a-tag :color="getTransitionTypeColor(record)" size="small">
              {{ getTransitionTypeText(record) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" size="small" @click="viewGroupDetail(record)" :loading="detailLoading[record.光路组id]">
              查看详情
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
  </div>


  <!-- 未选择地市提示 -->
  <div class="no-city-selected" v-if="!selectedCityCode">
    <div class="empty-state">
      <environment-outlined class="empty-icon" />
      <h3>请选择要查看的地市</h3>
      <p>默认推荐选择无锡市，数据相对完整</p>
    </div>
  </div>

  <!-- 光路组详情弹窗 -->
  <a-modal v-model:open="detailModalVisible" title="光路组详细信息" width="800px" :footer="null" class="group-detail-modal">
    <div class="detail-modal-content">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="detail-section-title">基本信息</h4>
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="保护组编码">
            {{ detailModalData.groupInfo.保护组编码 }}
          </a-descriptions-item>
          <a-descriptions-item label="保护组名称">
            {{ detailModalData.groupInfo.保护组名称 }}
          </a-descriptions-item>
          <a-descriptions-item label="地市">
            {{ detailModalData.groupInfo.地市名称 }}
          </a-descriptions-item>
          <a-descriptions-item label="区县">
            {{ detailModalData.groupInfo.区县 }}
          </a-descriptions-item>
          <a-descriptions-item label="专业">
            {{ detailModalData.groupInfo.专业 }}
          </a-descriptions-item>
          <a-descriptions-item label="保护组类型">
            {{ detailModalData.groupInfo.保护组类型 }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 状态变化历史 -->
      <div class="detail-section">
        <h4 class="detail-section-title">状态变化历史</h4>
        最新状态
        <a-tag :color="getStatusColor(detailModalData.historyData[0].最新检测结果)" size="small">
          {{ detailModalData.historyData[0].最新检测结果 || '未知' }}
        </a-tag>
        <a-table :columns="historyColumns" :data-source="detailModalData.historyData" :pagination="false" size="small"
          :scroll="{ y: 300 }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status_change'">
              <div class="status-transition">
                <a-tag :color="getStatusColor(record.上一次检测结果)" size="small">
                  {{ record.上一次检测结果 || '未知' }}
                </a-tag>

              </div>
            </template>
            <template v-if="column.key === 'action'">
              <a-button type="link" size="small" @click="handleViewMapWithHistory(record)" :loading="mapLoading">
                <template #icon>
                  <EnvironmentOutlined />
                </template>
                查看地图
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </a-modal>

  <!-- 日志详情弹窗 -->
  <a-modal v-model:open="logDetailModalVisible" title="操作日志详情" width="800px" :footer="null" class="log-detail-modal">
    <div class="log-detail-content" v-if="currentLogDetail">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="操作时间">
          {{ formatDateTime(currentLogDetail.operation_time) }}
        </a-descriptions-item>
        <a-descriptions-item label="操作人员">
          {{ currentLogDetail.operator_name }} ({{ currentLogDetail.operator_id }})
        </a-descriptions-item>
        <a-descriptions-item label="操作类型">
          <a-tag :color="getOperationTypeColor(currentLogDetail.operation_type)">
            {{ getOperationTypeName(currentLogDetail.operation_type) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="对象类型">
          <a-tag color="blue">{{ getObjectTypeName(currentLogDetail.object_type) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="对象信息" :span="2">
          {{ currentLogDetail.object_name }} ({{ currentLogDetail.object_code }})
        </a-descriptions-item>
        <a-descriptions-item label="操作描述" :span="2">
          {{ currentLogDetail.operation_description }}
        </a-descriptions-item>
        <a-descriptions-item label="操作结果">
          <a-tag :color="currentLogDetail.operation_result === 'success' ? 'green' : 'red'">
            {{ currentLogDetail.operation_result === 'success' ? '成功' : '失败' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="地市">
          {{ currentLogDetail.area_name }}
        </a-descriptions-item>
        <a-descriptions-item label="操作前数据" :span="2" v-if="currentLogDetail.before_data">
          <pre class="json-data">{{ formatJsonData(currentLogDetail.before_data) }}</pre>
        </a-descriptions-item>
        <a-descriptions-item label="操作后数据" :span="2" v-if="currentLogDetail.after_data">
          <pre class="json-data">{{ formatJsonData(currentLogDetail.after_data) }}</pre>
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="2" v-if="currentLogDetail.remark">
          {{ currentLogDetail.remark }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </a-modal>
  
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, defineOptions } from 'vue';

defineOptions({
  name: 'DualRouteLogAnalytics'
});
import { message } from 'ant-design-vue';
import * as echarts from 'echarts';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

// 配置dayjs
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

import {
  BarChartOutlined,
  ReloadOutlined,
  PieChartOutlined,
  TableOutlined,
  EnvironmentOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  FireOutlined,
  UserOutlined,
  ThunderboltOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue';
import {
  getCityName
} from '@/utils/jiangsu-sharding';
import { useInfo } from '@/hooks/web/useRestAPI';
import { useUserStore } from '@/store/modules/user';

// 用户store
const userStore = useUserStore();

// 响应式数据
const loading = reactive({
  summary: false,
  table: false,
  chart: false,
  export: false,
  heatmap: false,
});

// 地市选择
const selectedCityCode = ref<string>(''); // 默认全省

// 时间范围
const dateRange = ref<[dayjs.Dayjs, dayjs.Dayjs]>([
  dayjs().subtract(1, 'month'),
  dayjs()
]);

// 分析类型选择
const timeAnalysisType = ref<string>('hour'); // hour, day, week
const userAnalysisType = ref<string>('activity'); // activity, frequency, types

// 图表类型选择
const chartType = ref<string>('graph'); // graph, circular, heatmap

// 筛选器相关
const filterMode = ref<string>('status'); // status, type
const statusFilter = reactive({
  fromStatus: '',
  toStatus: '',
  transitionType: ''
});

// 加载状态
const currentCityLoading = ref<boolean>(false);
const currentProgress = ref<number>(0);
const currentProgressText = ref<string>('');
const filterLoading = ref<boolean>(false);
const detailLoading = reactive<Record<string, boolean>>({});
const mapLoading = ref<boolean>(false);

// 数据存储
const currentCityData = ref<any>(null);

// 弹窗相关
const detailModalVisible = ref<boolean>(false);
const detailModalData = reactive<any>({
  groupInfo: {},
  historyData: []
});
const compareModalVisible = ref<boolean>(false);

// 地图相关
const mapProtectionGroupId = ref<string>('');
const mapCurrentVersionData = ref<any>({});
const mapHistoryDate = ref<string>('');

// 辅助函数
const getCurrentCityName = () => {
  return getCityName(selectedCityCode.value) || '全省';
};

const getCurrentCityData = (key: string) => {
  return currentCityData.value?.[key] || null;
};

const hasCurrentCityData = () => {
  return currentCityData.value && Object.keys(currentCityData.value).length > 0;
};

const getApiDataCount = (key: string) => {
  const data = getCurrentCityData(key);
  return Array.isArray(data) ? data.length : 0;
};

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    '正常': '#52c41a',
    '无光路': '#ff4d4f',
    '单光路': '#fa8c16',
    '单路由': '#faad14',
    '缺管道': '#f759ab',
    '同管道': '#722ed1',
    '同光缆': '#13c2c2',
    '局内光路': '#1890ff'
  };
  return colorMap[status] || '#d9d9d9';
};

const getOperationTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'view': '查看',
    'update': '更新',
    'delete': '删除',
    'create': '创建',
    'risk_detection': '风险检测',
    'export': '导出',
    'import': '导入'
  };
  return typeMap[type] || type;
};

// API 调用函数
const getDualRouteChangeStatistics = async (cityCode: string, params: any) => {
  try {
    // 模拟API调用
    console.log('调用双路由变化统计API:', cityCode, params);
    return {
      success: true,
      data: [],
      message: '查询成功',
      pageInfo: {
        totalCount: 0,
        pageSize: params.pageSize || 10,
        currentPage: params.currentPage || 1
      }
    };
  } catch (error) {
    console.error('API调用失败:', error);
    return {
      success: false,
      data: [],
      message: '查询失败',
      pageInfo: {}
    };
  }
};

const getDualRouteMonthlyStatistics = async (cityCode: string, params: any) => {
  try {
    // 模拟API调用
    console.log('调用双路由月度统计API:', cityCode, params);
    return {
      success: true,
      data: {
        monthlyStats: [],
        statusDistribution: [],
        summary: {},
        areaStats: {}
      },
      message: '查询成功',
      pageInfo: {}
    };
  } catch (error) {
    console.error('API调用失败:', error);
    return {
      success: false,
      data: {
        monthlyStats: [],
        statusDistribution: [],
        summary: {},
        areaStats: {}
      },
      message: '查询失败',
      pageInfo: {}
    };
  }
};

// 搜索关键词
const searchKeyword = ref<string>('');

// 分析数据
const analyticsData = reactive({
  totalOperations: 0,
  activeUsers: 0,
  peakHour: '',
  mostActiveUser: '',
  timeDistribution: [] as Array<{ time: string; count: number }>,
  operationTypes: [] as Array<{ type: string; count: number; originalType: string }>,
  userActivity: [] as Array<{ name: string; count: number; typeCount: number }>,
  heatmapData: [] as Array<[number, string, number]>
});

// 表格数据
const tableData = ref<any[]>([]);

// 日志详情弹窗
const logDetailModalVisible = ref<boolean>(false);
const currentLogDetail = ref<any>(null);

// API服务
const logQueryService = useInfo({ rootPath: '/graph-rest-api' });
const logStatisticsService = useInfo({ rootPath: '/graph-rest-api' });

// 图表引用
const timeDistributionChart = ref<HTMLElement>();
const operationTypeChart = ref<HTMLElement>();
const userActivityChart = ref<HTMLElement>();
const heatmapChart = ref<HTMLElement>();

// 表格配置
const tablePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 表格列配置
const tableColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }: { index: number }) => index + 1
  },
  {
    title: '操作时间',
    dataIndex: 'operation_time',
    key: 'operation_time',
    width: 160,
    sorter: true
  },
  {
    title: '操作人员',
    dataIndex: 'operator_name',
    key: 'operator_name',
    width: 120
  },
  {
    title: '操作类型',
    dataIndex: 'operation_type',
    key: 'operation_type',
    width: 120
  },
  {
    title: '对象类型',
    dataIndex: 'object_type',
    key: 'object_type',
    width: 120
  },
  {
    title: '对象名称',
    dataIndex: 'object_name',
    key: 'object_name',
    width: 200,
    ellipsis: true
  },
  {
    title: '操作描述',
    dataIndex: 'operation_description',
    key: 'operation_description',
    width: 250,
    ellipsis: true
  },
  {
    title: '操作结果',
    dataIndex: 'operation_result',
    key: 'operation_result',
    width: 100
  },
  {
    title: '地市',
    dataIndex: 'area_name',
    key: 'area_name',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right'
  }
];

// 历史记录表格列定义
const historyColumns = [
  {
    title: '检测时间',
    dataIndex: '历史检测时间',
    key: 'check_time',
    width: 150,
    sorter: true,
    customRender: ({ text }: any) => {
      if (!text) return '-';
      // 如果是时间戳，转换为日期格式
      if (typeof text === 'number') {
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
      }
      // 如果已经是字符串格式，直接显示
      return text;
    }
  },
  {
    title: '状态变化',
    key: 'status_change',
    width: 200,
    align: 'center'
  },
  {
    title: '最新异常信息',
    dataIndex: '最新异常信息',
    key: 'exception_info',
    ellipsis: true
  },
  {
    title: '上次异常信息',
    dataIndex: '上一次检测异常信息',
    key: 'prev_exception_info',
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    align: 'center',
    fixed: 'right'
  }
];

// 删除不需要的图表配置变量

// 删除不需要的状态定义和变量

// 图表引用
const statusTransitionChart = ref<HTMLElement>();
const specialtyImprovementChart = ref<HTMLElement>();
const districtImprovementChart = ref<HTMLElement>();
const specialtyProblemChart = ref<HTMLElement>();
const districtProblemChart = ref<HTMLElement>();
const jointHeatmapChart = ref<HTMLElement>();
const trendComparisonChart = ref<HTMLElement>();

// 工具方法
const formatDateTime = (dateTime: string | number): string => {
  if (!dateTime) return '--';
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

const formatJsonData = (jsonStr: string): string => {
  if (!jsonStr) return '';
  try {
    const obj = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;
    return JSON.stringify(obj, null, 2);
  } catch (e) {
    return jsonStr;
  }
};



const getOperationTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    'view': 'blue',
    'update': 'orange',
    'delete': 'red',
    'create': 'green',
    'risk_detection': 'purple',
    'export': 'cyan',
    'import': 'magenta'
  };
  return colorMap[type] || 'default';
};

const getObjectTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    'opt_group': '光路组',
    'protection_group': '保护组',
    'optical_path': '光路',
    'system': '系统'
  };
  return typeMap[type] || type;
};

// 转移类型颜色
const getTransitionTypeColor = (record: any): string => {
  const current = record.最新检测结果;
  const previous = record.上一次检测结果;

  if (current === '正常' && previous !== '正常') return '#52c41a'; // 改善
  if (current !== '正常' && previous === '正常') return '#ff4d4f'; // 恶化
  if (current === previous) return '#1890ff'; // 保持
  return '#faad14'; // 其他变化
};

// 转移类型文本
const getTransitionTypeText = (record: any): string => {
  const current = record.最新检测结果;
  const previous = record.上一次检测结果;

  if (current === '正常' && previous !== '正常') return '状态改善';
  if (current !== '正常' && previous === '正常') return '状态恶化';
  if (current === previous) return '状态保持';
  return '状态变化';
};

// 查看光路组详情
const viewGroupDetail = async (record: any) => {
  const groupId = record.光路组id;
  if (!groupId) {
    message.warning('光路组ID不存在');
    return;
  }

  detailLoading[groupId] = true;

  try {
    console.log('🔍 [详情查询] 查询光路组详情:', groupId);

    // 调用详情查询API
    const response = await getDualRouteChangeStatistics(selectedCityCode.value, {
      groupId: groupId.toString(),
      startDate: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
      endDate: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'),
      historyLimit: 20, // 查询更多历史记录
      pageSize: 100,
      currentPage: 1
    });

    console.log('光路组查看详情response', response);

    if (response.success && response.data) {
      // 显示详情弹窗
      showGroupDetailModal(record, response.data);
    } else {
      message.error(response.message || '查询详情失败');
    }
  } catch (error) {
    console.error('❌ [详情查询] 查询失败:', error);
    message.error('查询详情失败');
  } finally {
    detailLoading[groupId] = false;
  }
};

// 显示详情弹窗
const showGroupDetailModal = (groupInfo: any, historyData: any[]) => {
  detailModalData.groupInfo = groupInfo;
  detailModalData.historyData = historyData;
  detailModalVisible.value = true;

  console.log('📋 [详情弹窗] 显示光路组详情:', {
    groupInfo,
    historyCount: historyData.length
  });
};

// 查看地图
const handleViewMap = async () => {
  if (!detailModalData.groupInfo || !detailModalData.groupInfo.光路组id) {
    message.warning('光路组ID不存在，无法查看地图');
    return;
  }

  mapLoading.value = true;

  try {
    // 构造保护组ID，格式为：光路组id_地市名称
    const groupId = detailModalData.groupInfo.光路组id;
    const cityName = detailModalData.groupInfo.地市名称 || getCurrentCityName();
    mapProtectionGroupId.value = `${groupId}_${cityName}`;

    // 准备当前版本数据（可以为空，让地图组件自己获取）
    mapCurrentVersionData.value = {};

    console.log('🗺️ [查看地图] 打开地图弹窗:', {
      protectionGroupId: mapProtectionGroupId.value,
      groupInfo: detailModalData.groupInfo
    });

    // 打开地图弹窗
    compareModalVisible.value = true;

  } catch (error) {
    console.error('❌ [查看地图] 打开地图失败:', error);
    message.error('打开地图失败');
  } finally {
    mapLoading.value = false;
  }
};

// 查看历史版本地图
const handleViewMapWithHistory = async (record: any) => {
  if (!detailModalData.groupInfo || !detailModalData.groupInfo.光路组id) {
    message.warning('光路组ID不存在，无法查看地图');
    return;
  }

  if (!record.历史检测时间) {
    message.warning('历史检测时间不存在，无法查看地图');
    return;
  }

  mapLoading.value = true;

  try {
    // 构造保护组ID，格式为：光路组id_地市名称
    const groupId = detailModalData.groupInfo.光路组id;
    const cityName = detailModalData.groupInfo.地市名称 || getCurrentCityName();
    mapProtectionGroupId.value = `${groupId}_${cityName}`;

    // 设置历史日期
    let historyDate = record.历史检测时间;
    if (typeof historyDate === 'number') {
      historyDate = dayjs(historyDate).format('YYYY-MM-DD HH:mm:ss');
    }
    mapHistoryDate.value = historyDate;

    // 准备当前版本数据（可以为空，让地图组件自己获取）
    mapCurrentVersionData.value = {};

    console.log('🗺️ [查看历史地图] 打开地图弹窗:', {
      protectionGroupId: mapProtectionGroupId.value,
      historyDate: mapHistoryDate.value,
      groupInfo: detailModalData.groupInfo,
      record
    });

    // 打开地图弹窗
    compareModalVisible.value = true;

  } catch (error) {
    console.error('❌ [查看历史地图] 打开地图失败:', error);
    message.error('打开地图失败');
  } finally {
    mapLoading.value = false;
  }
};

// 关闭地图弹窗
const handleCloseCompare = () => {
  compareModalVisible.value = false;
  mapProtectionGroupId.value = '';
  mapCurrentVersionData.value = {};
  mapHistoryDate.value = '';
  console.log('🗺️ [查看地图] 关闭地图弹窗');
};

// 筛选器变化事件
const onFilterChange = () => {
  console.log('🔍 [筛选器] 筛选条件变化:', statusFilter);
};

// 筛选模式切换
const onFilterModeChange = () => {
  // 切换模式时重置筛选条件
  statusFilter.fromStatus = '';
  statusFilter.toStatus = '';
  statusFilter.transitionType = '';
  console.log('🔄 [筛选器] 切换筛选模式:', filterMode.value);
};

// 应用筛选
const applyFilter = async () => {
  if (!selectedCityCode.value) {
    message.warning('请先选择地市');
    return;
  }

  filterLoading.value = true;

  try {
    console.log('🔍 [筛选器] 应用筛选条件:', statusFilter);

    // 调用带筛选条件的API
    const response = await getDualRouteChangeStatistics(selectedCityCode.value, {
      startDate: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
      endDate: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'),
      historyLimit: 2,
      pageSize: 10000,
      currentPage: 1,
      fromStatus: statusFilter.fromStatus || undefined,
      toStatus: statusFilter.toStatus || undefined,
      transitionType: statusFilter.transitionType || undefined
    });

    if (response.success && response.data) {
      // 更新当前地市数据中的变化统计
      if (currentCityData.value) {
        currentCityData.value.changeStats = response.data;
        currentCityData.value.changeInfo = response.pageInfo || {};

        // 更新表格分页
        tablePagination.total = response.data.length;
        tablePagination.current = 1;

        // 重新渲染图表
        await nextTick();
        setTimeout(async () => {
          await renderStatusTransitionChart();
        }, 100);

        message.success(`筛选完成，找到 ${response.data.length} 条记录`);
      }
    } else {
      message.error(response.message || '筛选失败');
    }
  } catch (error) {
    console.error('❌ [筛选器] 筛选失败:', error);
    message.error('筛选失败');
  } finally {
    filterLoading.value = false;
  }
};

// 重置筛选
const resetFilter = () => {
  statusFilter.fromStatus = '';
  statusFilter.toStatus = '';
  statusFilter.transitionType = '';

  console.log('🔄 [筛选器] 重置筛选条件');

  // 重新加载数据
  refreshCurrentCity();
};

// 表格分页、排序、筛选变化处理
const onTableChange = async (pagination: any, filters: any, sorter: any) => {
  console.log('📄 [表格] 分页变化:', pagination);

  if (!selectedCityCode.value) return;

  // 更新分页信息
  tablePagination.current = pagination.current;
  tablePagination.pageSize = pagination.pageSize;

  try {
    // 重新查询数据
    const response = await getDualRouteChangeStatistics(selectedCityCode.value, {
      startDate: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
      endDate: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'),
      historyLimit: 2,
      pageSize: pagination.pageSize,
      currentPage: pagination.current,
      fromStatus: statusFilter.fromStatus || undefined,
      toStatus: statusFilter.toStatus || undefined,
      transitionType: statusFilter.transitionType || undefined
    });

    if (response.success && response.data) {
      // 更新当前地市数据
      if (currentCityData.value) {
        currentCityData.value.changeStats = response.data;
        currentCityData.value.changeInfo = response.pageInfo || {};

        // 更新分页信息
        if (response.pageInfo) {
          tablePagination.total = response.pageInfo.totalCount || 0;
          tablePagination.current = response.pageInfo.currentPage || pagination.current;
          tablePagination.pageSize = response.pageInfo.pageSize || pagination.pageSize;
        }
      }
    }
  } catch (error) {
    console.error('❌ [表格] 分页查询失败:', error);
    message.error('分页查询失败');
  }
};

// 地市切换事件
const onCityChange = (cityCode: string) => {
  console.log('🏙️ [地市切换] 切换到:', getCityName(cityCode));
  selectedCityCode.value = cityCode;
  refreshCurrentCity();
};

// 刷新当前地市数据
const refreshCurrentCity = async () => {
  if (!selectedCityCode.value) return;

  currentCityLoading.value = true;
  currentProgress.value = 0;
  currentProgressText.value = '准备查询...';

  try {
    const cityName = getCurrentCityName();
    console.log(`🏙️ [${cityName}] 开始查询数据...`);

    // 模拟查询进度
    const progressSteps = [
      { progress: 20, text: '查询月度统计...' },
      { progress: 40, text: '查询趋势分析...' },
      { progress: 60, text: '查询变化统计...' },
      { progress: 80, text: '处理数据...' },
      { progress: 100, text: '完成' }
    ];

    for (const step of progressSteps) {
      currentProgress.value = step.progress;
      currentProgressText.value = step.text;
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    // 实际数据查询 - 两个基础接口（删除趋势分析接口）
    const [monthlyResult, changeResult] = await Promise.allSettled([
      getDualRouteMonthlyStatistics(selectedCityCode.value, {
        startDate: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
        endDate: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'),
      }),
      getDualRouteChangeStatistics(selectedCityCode.value, {
        startDate: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
        endDate: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'),
        historyLimit: 2,
        pageSize: 10000,
        currentPage: 1
      })
    ]);

    // 存储原始数据
    const rawData: any = {
      lastUpdate: Date.now()
    };

    // 月度统计数据
    if (monthlyResult.status === 'fulfilled' && monthlyResult.value?.data) {
      rawData.monthlyStats = monthlyResult.value.data.monthlyStats || [];
      rawData.statusDistribution = monthlyResult.value.data.statusDistribution || [];
      rawData.summary = monthlyResult.value.data.summary || {};
      rawData.areaStats = monthlyResult.value.data.areaStats || {};
      console.log(`📊 [${cityName}] 月度统计数据:`, rawData.monthlyStats.length, '条记录');
      console.log(`📊 [${cityName}] 状态分布:`, rawData.statusDistribution.length, '种状态');
      console.log(`📊 [${cityName}] 汇总信息:`, rawData.summary);
    }

    // 变化统计数据
    if (changeResult.status === 'fulfilled' && changeResult.value?.data) {
      rawData.changeStats = changeResult.value.data || [];
      rawData.changeInfo = changeResult.value.pageInfo || {};
      console.log(`🔧 [${cityName}] 变化统计数据:`, rawData.changeStats.length, '条记录');
      console.log(`🔧 [${cityName}] 分页信息:`, rawData.changeInfo);
    }

    currentCityData.value = rawData;

    // 更新表格分页总数
    tablePagination.total = rawData.changeStats ? rawData.changeStats.length : 0;
    tablePagination.current = 1; // 重置到第一页

    // 自动刷新图表
    await nextTick();
    setTimeout(async () => {
      await renderStatusTransitionChart();
      await renderAllAnalysisCharts();
      console.log(`✅ [${cityName}] 图表自动渲染完成`);
    }, 100);

    message.success(`${cityName}数据刷新完成`);
    console.log(`✅ [${cityName}] 数据查询完成，和弦图将自动更新`);

  } catch (error) {
    console.error(`❌ [${getCurrentCityName()}] 数据查询失败:`, error);
    message.error(`${getCurrentCityName()}数据查询失败`);
  } finally {
    currentCityLoading.value = false;
    currentProgress.value = 0;
    currentProgressText.value = '';
  }
};

// 删除不需要的函数，专注于当前地市

// 删除不需要的函数

// 删除不需要的函数

// 删除不需要的模拟数据

// 初始化
onMounted(() => {
  // 自动加载默认地市（无锡）数据
  refreshCurrentCity();
});

// 分析状态改善数据
const analyzeImprovementData = (changeStats: any[]) => {
  if (!changeStats || changeStats.length === 0) return { specialtyData: [], districtData: [] };

  // 筛选出转为正常状态的记录
  const improvementRecords = changeStats.filter(item =>
    item.最新检测结果 === '正常' && item.上一次检测结果 !== '正常'
  );

  console.log('🔍 [改善分析] 改善记录:', improvementRecords.length, '条');

  // 按专业统计
  const specialtyStats: Record<string, number> = {};
  // 按区县统计
  const districtStats: Record<string, number> = {};

  improvementRecords.forEach(record => {
    const specialty = record.专业 || '未知专业';
    const district = record.区县 || '未知区县';

    specialtyStats[specialty] = (specialtyStats[specialty] || 0) + 1;
    districtStats[district] = (districtStats[district] || 0) + 1;
  });

  // 转换为图表数据格式
  const specialtyData = Object.entries(specialtyStats)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value);

  const districtData = Object.entries(districtStats)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value);

  console.log('📊 [改善分析] 专业统计:', specialtyData);
  console.log('📊 [改善分析] 区县统计:', districtData);

  return { specialtyData, districtData };
};

// 渲染专业改善统计图表
const renderSpecialtyImprovementChart = (data: any[]) => {
  if (!specialtyImprovementChart.value) return;

  const chart = echarts.init(specialtyImprovementChart.value);

  if (data.length === 0) {
    chart.setOption({
      title: {
        text: '暂无改善数据',
        left: 'center',
        top: 'center',
        textStyle: { fontSize: 14, color: '#999' }
      }
    });
    return;
  }

  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '各专业状态改善统计',
      left: 'center',
      top: 10,
      textStyle: { fontSize: 14, fontWeight: 'bold', color: '#333' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: function (params: any) {
        const data = params[0];
        return `${data.name}<br/>改善次数: <strong>${data.value}</strong>`;
      }
    },
    grid: {
      left: 60,
      right: 20,
      top: 50,
      bottom: 60
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.name),
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '改善次数',
      nameTextStyle: { fontSize: 10 }
    },
    series: [{
      type: 'bar',
      data: data.map(item => ({
        value: item.value,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#52c41a' },
            { offset: 1, color: '#389e0d' }
          ])
        }
      })),
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        fontSize: 10
      }
    }]
  };

  chart.setOption(option);
  chart.resize();
};

// 渲染区县改善统计图表
const renderDistrictImprovementChart = (data: any[]) => {
  if (!districtImprovementChart.value) return;

  const chart = echarts.init(districtImprovementChart.value);

  if (data.length === 0) {
    chart.setOption({
      title: {
        text: '暂无改善数据',
        left: 'center',
        top: 'center',
        textStyle: { fontSize: 14, color: '#999' }
      }
    });
    return;
  }

  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '各区县状态改善统计',
      left: 'center',
      top: 10,
      textStyle: { fontSize: 14, fontWeight: 'bold', color: '#333' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: function (params: any) {
        const data = params[0];
        return `${data.name}<br/>改善次数: <strong>${data.value}</strong>`;
      }
    },
    grid: {
      left: 60,
      right: 20,
      top: 50,
      bottom: 60
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.name),
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '改善次数',
      nameTextStyle: { fontSize: 10 }
    },
    series: [{
      type: 'bar',
      data: data.map(item => ({
        value: item.value,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#1890ff' },
            { offset: 1, color: '#096dd9' }
          ])
        }
      })),
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        fontSize: 10
      }
    }]
  };

  chart.setOption(option);
  chart.resize();
};

// 渲染改善分析图表
const renderImprovementCharts = async () => {
  const changeStats = getCurrentCityData('changeStats');
  if (!changeStats) return;

  const { specialtyData, districtData } = analyzeImprovementData(changeStats);

  await nextTick();
  renderSpecialtyImprovementChart(specialtyData);
  renderDistrictImprovementChart(districtData);
};

// 分析问题状态数据
const analyzeProblemData = (changeStats: any[]) => {
  if (!changeStats || changeStats.length === 0) return { specialtyData: [], districtData: [] };

  // 定义问题状态（非正常状态）
  const problemStatuses = ['单路由', '同管道', '同光缆', '无光路', '缺管道', '异常'];

  // 筛选出当前为问题状态的记录
  const problemRecords = changeStats.filter(item =>
    problemStatuses.includes(item.最新检测结果)
  );

  console.log('🔍 [问题分析] 问题记录:', problemRecords.length, '条');

  // 按专业统计问题状态
  const specialtyStats: Record<string, Record<string, number>> = {};
  // 按区县统计问题状态
  const districtStats: Record<string, Record<string, number>> = {};

  problemRecords.forEach(record => {
    const specialty = record.专业 || '未知专业';
    const district = record.区县 || '未知区县';
    const status = record.最新检测结果;

    // 专业统计
    if (!specialtyStats[specialty]) specialtyStats[specialty] = {};
    specialtyStats[specialty][status] = (specialtyStats[specialty][status] || 0) + 1;

    // 区县统计
    if (!districtStats[district]) districtStats[district] = {};
    districtStats[district][status] = (districtStats[district][status] || 0) + 1;
  });

  // 转换为图表数据格式
  const specialtyData = Object.entries(specialtyStats).map(([name, statusCounts]) => {
    const total = Object.values(statusCounts).reduce((sum, count) => sum + count, 0);
    return { name, total, statusCounts };
  }).sort((a, b) => b.total - a.total);

  const districtData = Object.entries(districtStats).map(([name, statusCounts]) => {
    const total = Object.values(statusCounts).reduce((sum, count) => sum + count, 0);
    return { name, total, statusCounts };
  }).sort((a, b) => b.total - a.total);

  console.log('📊 [问题分析] 专业问题统计:', specialtyData);
  console.log('📊 [问题分析] 区县问题统计:', districtData);

  return { specialtyData, districtData };
};

// 渲染专业问题分布图表
const renderSpecialtyProblemChart = (data: any[]) => {
  if (!specialtyProblemChart.value) return;

  const chart = echarts.init(specialtyProblemChart.value);

  if (data.length === 0) {
    chart.setOption({
      title: {
        text: '暂无问题数据',
        left: 'center',
        top: 'center',
        textStyle: { fontSize: 14, color: '#999' }
      }
    });
    return;
  }

  // 获取所有问题状态类型
  const allStatuses = new Set<string>();
  data.forEach(item => {
    Object.keys(item.statusCounts).forEach(status => allStatuses.add(status));
  });
  const statusList = Array.from(allStatuses);

  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '各专业问题状态分布',
      left: 'center',
      top: 10,
      textStyle: { fontSize: 14, fontWeight: 'bold', color: '#333' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: function (params: any) {
        let result = `${params[0].name}<br/>`;
        params.forEach((param: any) => {
          result += `${param.seriesName}: <strong>${param.value}</strong><br/>`;
        });
        return result;
      }
    },
    legend: {
      top: 30,
      data: statusList,
      textStyle: { fontSize: 10 }
    },
    grid: {
      left: 60,
      right: 20,
      top: 60,
      bottom: 80
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.name),
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '问题数量',
      nameTextStyle: { fontSize: 10 }
    },
    series: statusList.map(status => ({
      name: status,
      type: 'bar',
      stack: 'total',
      data: data.map(item => item.statusCounts[status] || 0),
      itemStyle: {
        color: getStatusColor(status)
      },
      label: {
        show: true,
        position: 'inside',
        fontSize: 9,
        formatter: function (params: any) {
          return params.value > 0 ? params.value : '';
        }
      }
    }))
  };

  chart.setOption(option);
  chart.resize();
};

// 渲染区县问题分布图表
const renderDistrictProblemChart = (data: any[]) => {
  if (!districtProblemChart.value) return;

  const chart = echarts.init(districtProblemChart.value);

  if (data.length === 0) {
    chart.setOption({
      title: {
        text: '暂无问题数据',
        left: 'center',
        top: 'center',
        textStyle: { fontSize: 14, color: '#999' }
      }
    });
    return;
  }

  // 获取所有问题状态类型
  const allStatuses = new Set<string>();
  data.forEach(item => {
    Object.keys(item.statusCounts).forEach(status => allStatuses.add(status));
  });
  const statusList = Array.from(allStatuses);

  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '各区县问题状态分布',
      left: 'center',
      top: 10,
      textStyle: { fontSize: 14, fontWeight: 'bold', color: '#333' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: function (params: any) {
        let result = `${params[0].name}<br/>`;
        params.forEach((param: any) => {
          result += `${param.seriesName}: <strong>${param.value}</strong><br/>`;
        });
        return result;
      }
    },
    legend: {
      top: 30,
      data: statusList,
      textStyle: { fontSize: 10 }
    },
    grid: {
      left: 60,
      right: 20,
      top: 60,
      bottom: 80
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.name),
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '问题数量',
      nameTextStyle: { fontSize: 10 }
    },
    series: statusList.map(status => ({
      name: status,
      type: 'bar',
      stack: 'total',
      data: data.map(item => item.statusCounts[status] || 0),
      itemStyle: {
        color: getStatusColor(status)
      },
      label: {
        show: true,
        position: 'inside',
        fontSize: 9,
        formatter: function (params: any) {
          return params.value > 0 ? params.value : '';
        }
      }
    }))
  };

  chart.setOption(option);
  chart.resize();
};

// 分析专业-区县联合数据
const analyzeJointData = (changeStats: any[]) => {
  if (!changeStats || changeStats.length === 0) return { heatmapData: [], trendData: [] };

  // 定义问题状态
  const problemStatuses = ['单路由', '同管道', '同光缆', '无光路', '缺管道', '异常'];

  // 筛选出问题记录
  const problemRecords = changeStats.filter(item =>
    problemStatuses.includes(item.最新检测结果)
  );

  // 专业-区县交叉统计
  const jointStats: Record<string, Record<string, number>> = {};
  const specialtySet = new Set<string>();
  const districtSet = new Set<string>();

  problemRecords.forEach(record => {
    const specialty = record.专业 || '未知专业';
    const district = record.区县 || '未知区县';

    specialtySet.add(specialty);
    districtSet.add(district);

    if (!jointStats[specialty]) jointStats[specialty] = {};
    jointStats[specialty][district] = (jointStats[specialty][district] || 0) + 1;
  });

  const specialtyList = Array.from(specialtySet);
  const districtList = Array.from(districtSet);

  // 生成热力图数据
  const heatmapData: any[] = [];
  specialtyList.forEach((specialty, sIndex) => {
    districtList.forEach((district, dIndex) => {
      const value = jointStats[specialty]?.[district] || 0;
      heatmapData.push([dIndex, sIndex, value]);
    });
  });

  // 生成趋势对比数据（改善vs恶化）
  const improvementStats: Record<string, number> = {};
  const deteriorationStats: Record<string, number> = {};

  changeStats.forEach(record => {
    const specialty = record.专业 || '未知专业';
    const fromStatus = record.上一次检测结果;
    const toStatus = record.最新检测结果;

    // 改善：从问题状态到正常状态
    if (problemStatuses.includes(fromStatus) && toStatus === '正常') {
      improvementStats[specialty] = (improvementStats[specialty] || 0) + 1;
    }
    // 恶化：从正常状态到问题状态
    if (fromStatus === '正常' && problemStatuses.includes(toStatus)) {
      deteriorationStats[specialty] = (deteriorationStats[specialty] || 0) + 1;
    }
  });

  const trendData = specialtyList.map(specialty => ({
    specialty,
    improvement: improvementStats[specialty] || 0,
    deterioration: deteriorationStats[specialty] || 0
  }));

  console.log('📊 [联合分析] 热力图数据:', heatmapData.length, '个数据点');
  console.log('📊 [联合分析] 趋势数据:', trendData);

  return {
    heatmapData,
    trendData,
    specialtyList,
    districtList
  };
};

// 渲染专业-区县问题热力图
const renderJointHeatmapChart = (data: any) => {
  if (!jointHeatmapChart.value) return;

  const chart = echarts.init(jointHeatmapChart.value);

  if (data.heatmapData.length === 0) {
    chart.setOption({
      title: {
        text: '暂无联合分析数据',
        left: 'center',
        top: 'center',
        textStyle: { fontSize: 14, color: '#999' }
      }
    });
    return;
  }

  const maxValue = Math.max(...data.heatmapData.map((item: any) => item[2]));

  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '专业-区县问题分布热力图',
      left: 'center',
      top: 10,
      textStyle: { fontSize: 14, fontWeight: 'bold', color: '#333' }
    },
    tooltip: {
      position: 'top',
      formatter: function (params: any) {
        const specialty = data.specialtyList[params.data[1]];
        const district = data.districtList[params.data[0]];
        const value = params.data[2];
        return `${specialty} - ${district}<br/>问题数量: <strong>${value}</strong>`;
      }
    },
    grid: {
      left: 80,
      right: 20,
      top: 50,
      bottom: 80
    },
    xAxis: {
      type: 'category',
      data: data.districtList,
      splitArea: { show: true },
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'category',
      data: data.specialtyList,
      splitArea: { show: true },
      axisLabel: { fontSize: 10 }
    },
    visualMap: {
      min: 0,
      max: maxValue,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: 10,
      inRange: {
        color: ['#ffffff', '#ffebee', '#ffcdd2', '#ef5350', '#d32f2f']
      },
      textStyle: { fontSize: 10 }
    },
    series: [{
      name: '问题数量',
      type: 'heatmap',
      data: data.heatmapData,
      label: {
        show: true,
        fontSize: 10
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };

  chart.setOption(option);
  chart.resize();
};

// 渲染状态变化趋势对比图表
const renderTrendComparisonChart = (data: any) => {
  if (!trendComparisonChart.value) return;

  const chart = echarts.init(trendComparisonChart.value);

  if (data.trendData.length === 0) {
    chart.setOption({
      title: {
        text: '暂无趋势数据',
        left: 'center',
        top: 'center',
        textStyle: { fontSize: 14, color: '#999' }
      }
    });
    return;
  }

  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '各专业状态变化趋势对比',
      left: 'center',
      top: 10,
      textStyle: { fontSize: 14, fontWeight: 'bold', color: '#333' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: function (params: any) {
        let result = `${params[0].name}<br/>`;
        params.forEach((param: any) => {
          result += `${param.seriesName}: <strong>${param.value}</strong><br/>`;
        });
        return result;
      }
    },
    legend: {
      top: 30,
      data: ['状态改善', '状态恶化'],
      textStyle: { fontSize: 10 }
    },
    grid: {
      left: 60,
      right: 20,
      top: 60,
      bottom: 80
    },
    xAxis: {
      type: 'category',
      data: data.trendData.map((item: any) => item.specialty),
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '变化次数',
      nameTextStyle: { fontSize: 10 }
    },
    series: [
      {
        name: '状态改善',
        type: 'bar',
        data: data.trendData.map((item: any) => item.improvement),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#52c41a' },
            { offset: 1, color: '#389e0d' }
          ])
        },
        label: {
          show: true,
          position: 'top',
          fontSize: 10
        }
      },
      {
        name: '状态恶化',
        type: 'bar',
        data: data.trendData.map((item: any) => item.deterioration),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ff7875' },
            { offset: 1, color: '#f5222d' }
          ])
        },
        label: {
          show: true,
          position: 'top',
          fontSize: 10
        }
      }
    ]
  };

  chart.setOption(option);
  chart.resize();
};

// 渲染所有分析图表
const renderAllAnalysisCharts = async () => {
  const changeStats = getCurrentCityData('changeStats');
  if (!changeStats) return;

  console.log('📊 [综合分析] 开始渲染所有分析图表...');

  // 分析数据
  const { specialtyData: improvementSpecialtyData, districtData: improvementDistrictData } = analyzeImprovementData(changeStats);
  const { specialtyData: problemSpecialtyData, districtData: problemDistrictData } = analyzeProblemData(changeStats);
  const jointData = analyzeJointData(changeStats);

  await nextTick();

  // 渲染改善分析图表
  renderSpecialtyImprovementChart(improvementSpecialtyData);
  renderDistrictImprovementChart(improvementDistrictData);

  // 渲染问题分析图表
  renderSpecialtyProblemChart(problemSpecialtyData);
  renderDistrictProblemChart(problemDistrictData);

  // 渲染联合分析图表
  renderJointHeatmapChart(jointData);
  renderTrendComparisonChart(jointData);

  console.log('✅ [综合分析] 所有分析图表渲染完成');
};

// 渲染状态转移图表 - 支持多种图表类型
const renderStatusTransitionChart = async () => {
  if (!statusTransitionChart.value) return;

  const chart = echarts.init(statusTransitionChart.value);

  // 清理之前的图表配置，确保背景干净
  chart.clear();

  // 收集当前地市的变化统计数据
  const changeStats = getCurrentCityData('changeStats');
  if (!changeStats || !Array.isArray(changeStats) || changeStats.length === 0) {
    chart.setOption({
      title: {
        text: '暂无状态转移数据',
        subtext: '请先刷新当前地市数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 18,
          color: '#8c8c8c'
        },
        subtextStyle: {
          fontSize: 14,
          color: '#bfbfbf'
        }
      },
      series: []
    });
    return;
  }

  // 统计所有状态和转移
  const statusSet = new Set<string>();
  const transitionMap: Record<string, number> = {};

  changeStats.forEach((item: any) => {
    const fromStatus = item.上一次检测结果 || '未知';
    const toStatus = item.最新检测结果 || '未知';

    statusSet.add(fromStatus);
    statusSet.add(toStatus);

    if (fromStatus !== toStatus) {
      const key = `${fromStatus}->${toStatus}`;
      transitionMap[key] = (transitionMap[key] || 0) + 1;
    }
  });

  const statusList = Array.from(statusSet);
  const statusColorMap: Record<string, string> = {
    '正常': '#52c41a',
    '单路由': '#faad14',
    '同管道': '#ff7875',
    '同光缆': '#ff4d4f',
    '无光路': '#8c8c8c',
    '缺管道': '#722ed1',
    '异常': '#f5222d',
    '未知': '#d9d9d9'
  };

  const totalTransitions = Object.values(transitionMap).reduce((sum, count) => sum + count, 0);

  if (chartType.value === 'circular') {
    // 环形关系图
    renderCircularChart(chart, statusList, transitionMap, statusColorMap, totalTransitions);
  } else if (chartType.value === 'heatmap') {
    // 热力图
    renderHeatmapChart(chart, statusList, transitionMap, statusColorMap, totalTransitions);
  } else {
    // 力导向图（默认）
    renderGraphChart(chart, statusList, transitionMap, statusColorMap, totalTransitions);
  }
};

// 渲染美化的环形关系图
const renderCircularChart = (chart: any, statusList: string[], transitionMap: Record<string, number>, statusColorMap: Record<string, string>, totalTransitions: number) => {
  // 检查是否有转移数据
  if (Object.keys(transitionMap).length === 0) {
    chart.setOption({
      backgroundColor: 'transparent',
      title: {
        text: '暂无状态转移数据',
        subtext: '当前地市没有状态变化记录',
        left: 'center',
        top: 'center',
        textStyle: { fontSize: 18, color: '#8c8c8c' },
        subtextStyle: { fontSize: 14, color: '#bfbfbf' }
      },
      series: []
    });
    return;
  }

  // 计算转移次数范围
  const transitionValues = Object.values(transitionMap);
  const maxTransitions = Math.max(...transitionValues);
  const minTransitions = Math.min(...transitionValues);

  // 美化的环形布局 - 更大的半径和更好的间距
  const angleStep = (2 * Math.PI) / statusList.length;
  const radius = 180; // 稍微减小半径，让图表更紧凑
  const centerX = 300;
  const centerY = 320; // 稍微下移，为标题留空间

  // 计算每个状态的转移活跃度，用于调整节点大小
  const nodeActivity: Record<string, number> = {};
  Object.entries(transitionMap).forEach(([key, value]) => {
    const [source, target] = key.split('->');
    nodeActivity[source] = (nodeActivity[source] || 0) + value;
    nodeActivity[target] = (nodeActivity[target] || 0) + value;
  });

  const maxActivity = Math.max(...Object.values(nodeActivity));

  const nodes = statusList.map((status, index) => {
    const angle = index * angleStep - Math.PI / 2; // 从顶部开始
    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);

    // 根据活跃度调整节点大小
    const activity = nodeActivity[status] || 0;
    const sizeRatio = activity / (maxActivity || 1);
    const nodeSize = Math.max(35, Math.min(65, 35 + sizeRatio * 30));

    return {
      id: status,
      name: status,
      x: x,
      y: y,
      fixed: true,
      symbolSize: nodeSize,
      itemStyle: {
        color: statusColorMap[status] || '#d9d9d9',
        borderColor: '#ffffff',
        borderWidth: 3,
        shadowBlur: 8,
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        shadowOffsetX: 2,
        shadowOffsetY: 2
      },
      label: {
        show: true,
        fontSize: Math.max(10, Math.min(13, 10 + sizeRatio * 3)),
        fontWeight: 'bold',
        color: '#ffffff',
        textShadowColor: 'rgba(0, 0, 0, 0.5)',
        textShadowBlur: 2,
        position: 'inside'
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 15,
          shadowColor: 'rgba(0, 0, 0, 0.4)',
          borderWidth: 4,
          borderColor: '#ffffff'
        },
        label: {
          fontSize: 14,
          fontWeight: 'bold'
        }
      }
    };
  });

  // 美化的连接线 - 渐变色和更好的弧度
  const links = Object.entries(transitionMap).map(([key, value]) => {
    const [source, target] = key.split('->');

    // 计算线宽和透明度
    const widthRatio = (value - minTransitions) / (maxTransitions - minTransitions || 1);
    const lineWidth = Math.max(2, Math.min(8, 2 + widthRatio * 6));
    const opacity = Math.max(0.4, Math.min(0.9, 0.4 + widthRatio * 0.5));

    // 计算弧度 - 根据节点距离调整
    const sourceIndex = statusList.indexOf(source);
    const targetIndex = statusList.indexOf(target);
    const indexDiff = Math.abs(sourceIndex - targetIndex);
    const curveness = Math.min(0.6, 0.2 + (indexDiff / statusList.length) * 0.4);

    return {
      source: source,
      target: target,
      value: value,
      lineStyle: {
        width: lineWidth,
        opacity: opacity,
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 1, y2: 0,
          colorStops: [
            { offset: 0, color: statusColorMap[source] || '#666' },
            { offset: 1, color: statusColorMap[target] || '#666' }
          ]
        },
        curveness: curveness,
        shadowBlur: 3,
        shadowColor: 'rgba(0, 0, 0, 0.2)'
      },
      symbol: ['none', 'arrow'],
      symbolSize: [0, Math.max(8, Math.min(16, 8 + widthRatio * 8))],
      emphasis: {
        lineStyle: {
          width: lineWidth + 2,
          shadowBlur: 8,
          shadowColor: 'rgba(0, 0, 0, 0.4)'
        }
      }
    };
  });

  console.log('🔄 [美化环形图] 状态列表:', statusList);
  console.log('🔄 [美化环形图] 节点活跃度:', nodeActivity);
  console.log('🔄 [美化环形图] 连接数据:', links.length);

  const option = {
    backgroundColor: 'transparent',
    title: {
      text: `${getCurrentCityName()} 双路由状态转移分析`,
      subtext: `环形关系图 · ${statusList.length} 种状态 · ${totalTransitions} 次转移`,
      left: 'center',
      top: 15,
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2c3e50',
        textShadowColor: 'rgba(0, 0, 0, 0.1)',
        textShadowBlur: 1
      },
      subtextStyle: {
        fontSize: 13,
        color: '#7f8c8d',
        fontWeight: '500'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      borderWidth: 1,
      textStyle: { color: '#fff', fontSize: 12 },
      formatter: function (params: any) {
        if (params.dataType === 'edge') {
          return `
            <div style="padding: 5px;">
              <div style="color: #74b9ff; font-weight: bold; margin-bottom: 3px;">状态转移</div>
              <div>${params.data.source} → ${params.data.target}</div>
              <div style="color: #00b894; font-weight: bold;">转移次数: ${params.data.value}</div>
              <div style="color: #fdcb6e; font-size: 11px;">线宽: ${params.data.lineStyle.width.toFixed(1)}px</div>
            </div>
          `;
        } else {
          const activity = nodeActivity[params.data.name] || 0;
          return `
            <div style="padding: 5px;">
              <div style="color: #74b9ff; font-weight: bold; margin-bottom: 3px;">状态节点</div>
              <div style="color: #fff; font-weight: bold;">${params.data.name}</div>
              <div style="color: #00b894;">活跃度: ${activity} 次</div>
              <div style="color: #fdcb6e; font-size: 11px;">节点大小: ${params.data.symbolSize}px</div>
            </div>
          `;
        }
      }
    },
    series: [{
      type: 'graph',
      layout: 'none',
      data: nodes,
      links: links,
      roam: false,
      emphasis: {
        focus: 'adjacency',
        scale: 1.1
      },
      edgeSymbol: ['none', 'arrow'],
      edgeSymbolSize: [0, 10],
      lineStyle: {
        curveness: 0.3,
        opacity: 0.7
      },
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut'
    }]
  };

  chart.setOption(option);
  chart.resize();
};



// 渲染力导向图
const renderGraphChart = (chart: any, statusList: string[], transitionMap: Record<string, number>, statusColorMap: Record<string, string>, totalTransitions: number) => {
  // 检查是否有转移数据
  if (Object.keys(transitionMap).length === 0) {
    chart.setOption({
      title: {
        text: '暂无状态转移数据',
        subtext: '当前地市没有状态变化记录',
        left: 'center',
        top: 'center',
        textStyle: { fontSize: 18, color: '#8c8c8c' },
        subtextStyle: { fontSize: 14, color: '#bfbfbf' }
      },
      series: []
    });
    return;
  }

  // 计算转移次数的范围，用于调整线宽
  const transitionValues = Object.values(transitionMap);
  const maxTransitions = Math.max(...transitionValues);
  const minTransitions = Math.min(...transitionValues);

  // 构建节点数据 - 根据涉及的转移次数调整节点大小
  const nodeTransitionCount: Record<string, number> = {};
  Object.entries(transitionMap).forEach(([key, value]) => {
    const [source, target] = key.split('->');
    nodeTransitionCount[source] = (nodeTransitionCount[source] || 0) + value;
    nodeTransitionCount[target] = (nodeTransitionCount[target] || 0) + value;
  });

  const nodes = statusList.map(status => {
    const transitionCount = nodeTransitionCount[status] || 0;
    const nodeSize = Math.max(30, Math.min(80, 30 + (transitionCount / maxTransitions) * 50));

    return {
      id: status,
      name: status,
      symbolSize: nodeSize,
      itemStyle: {
        color: statusColorMap[status] || '#d9d9d9',
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        fontSize: Math.max(10, Math.min(14, 10 + nodeSize / 20)),
        fontWeight: 'bold',
        color: '#333'
      }
    };
  });

  // 构建连接数据 - 显著调整线宽差异并添加箭头
  const links = Object.entries(transitionMap).map(([key, value]) => {
    const [source, target] = key.split('->');

    // 计算线宽：最小2px，最大12px，根据转移次数比例调整
    const widthRatio = (value - minTransitions) / (maxTransitions - minTransitions || 1);
    const lineWidth = Math.max(2, Math.min(12, 2 + widthRatio * 10));

    // 根据转移次数调整透明度
    const opacity = Math.max(0.4, Math.min(0.9, 0.4 + widthRatio * 0.5));

    return {
      source: source,
      target: target,
      value: value,
      lineStyle: {
        width: lineWidth,
        opacity: opacity,
        color: statusColorMap[source] || '#666'
      },
      // 添加箭头
      symbol: ['none', 'arrow'],
      symbolSize: [0, Math.max(8, Math.min(16, 8 + widthRatio * 8))]
    };
  });

  console.log('🔄 [力导向图] 节点数据:', nodes);
  console.log('🔄 [力导向图] 连接数据:', links);
  console.log('🔄 [力导向图] 转移次数范围:', minTransitions, '-', maxTransitions);

  const option = {
    backgroundColor: '#fff', // 明确设置白色背景
    title: {
      text: `${getCurrentCityName()} 双路由状态转移分析 (力导向图)`,
      subtext: `共 ${statusList.length} 种状态，${totalTransitions} 次转移`,
      left: 'center',
      top: 20,
      textStyle: { fontSize: 16, fontWeight: 'bold', color: '#1a1a1a' },
      subtextStyle: { fontSize: 12, color: '#666666' }
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params: any) {
        if (params.dataType === 'edge') {
          return `${params.data.source} → ${params.data.target}<br/>转移次数: <strong>${params.data.value}</strong><br/>线宽: ${params.data.lineStyle.width.toFixed(1)}px`;
        } else {
          const transitionCount = nodeTransitionCount[params.data.name] || 0;
          return `${params.data.name}<br/>涉及转移: <strong>${transitionCount}</strong> 次<br/>节点大小: ${params.data.symbolSize}px`;
        }
      }
    },
    series: [{
      type: 'graph',
      layout: 'force',
      data: nodes,
      links: links,
      roam: true,
      draggable: true,
      force: {
        repulsion: 1200,
        edgeLength: [100, 200],
        gravity: 0.1
      },
      emphasis: {
        focus: 'adjacency',
        itemStyle: { borderWidth: 3, borderColor: '#333' },
        lineStyle: { width: 4 }
      },
      edgeSymbol: ['none', 'arrow'],
      edgeSymbolSize: [0, 10],
      lineStyle: {
        curveness: 0.2,
        opacity: 0.7
      }
    }]
  };

  chart.setOption(option);
  chart.resize();
};

// 渲染热力图
const renderHeatmapChart = (chart: any, statusList: string[], transitionMap: Record<string, number>, statusColorMap: Record<string, string>, totalTransitions: number) => {
  // 彻底清理图表，确保背景干净
  chart.clear();

  // 检查是否有转移数据
  if (Object.keys(transitionMap).length === 0) {
    chart.setOption({
      title: {
        text: '暂无状态转移数据',
        subtext: '当前地市没有状态变化记录',
        left: 'center',
        top: 'center',
        textStyle: { fontSize: 18, color: '#8c8c8c' },
        subtextStyle: { fontSize: 14, color: '#bfbfbf' }
      },
      backgroundColor: '#fff', // 明确设置白色背景
      series: []
    });
    return;
  }

  // 构建热力图数据
  const heatmapData: any[] = [];
  const maxValue = Math.max(...Object.values(transitionMap));

  statusList.forEach((fromStatus, fromIndex) => {
    statusList.forEach((toStatus, toIndex) => {
      const key = `${fromStatus}->${toStatus}`;
      const value = transitionMap[key] || 0;

      if (value > 0) {
        heatmapData.push([fromIndex, toIndex, value]);
      }
    });
  });

  console.log('🔄 [热力图] 状态列表:', statusList);
  console.log('🔄 [热力图] 热力图数据:', heatmapData);
  console.log('🔄 [热力图] 最大值:', maxValue);

  const option = {
    backgroundColor: '#fff', // 明确设置白色背景
    title: {
      text: `${getCurrentCityName()} 双路由状态转移分析 (热力图)`,
      subtext: `共 ${statusList.length} 种状态，${totalTransitions} 次转移`,
      left: 'center',
      top: 20,
      textStyle: { fontSize: 16, fontWeight: 'bold', color: '#1a1a1a' },
      subtextStyle: { fontSize: 12, color: '#666666' }
    },
    tooltip: {
      position: 'top',
      formatter: function (params: any) {
        const fromStatus = statusList[params.data[0]];
        const toStatus = statusList[params.data[1]];
        const value = params.data[2];
        return `${fromStatus} → ${toStatus}<br/>转移次数: <strong>${value}</strong>`;
      }
    },
    grid: {
      left: 100,
      right: 50,
      top: 100,
      bottom: 100
    },
    xAxis: {
      type: 'category',
      data: statusList,
      splitArea: { show: true },
      axisLabel: {
        rotate: 45,
        fontSize: 11,
        color: '#333'
      },
      name: '目标状态',
      nameLocation: 'middle',
      nameGap: 50
    },
    yAxis: {
      type: 'category',
      data: statusList,
      splitArea: { show: true },
      axisLabel: {
        fontSize: 11,
        color: '#333'
      },
      name: '源状态',
      nameLocation: 'middle',
      nameGap: 80
    },
    visualMap: {
      min: 0,
      max: maxValue,
      calculable: true,
      orient: 'vertical',
      right: 20,
      bottom: 60,
      inRange: {
        color: ['#ffffff', '#ffeb3b', '#ff9800', '#f44336', '#9c27b0']
      },
      text: ['高', '低'],
      textStyle: { color: '#333' }
    },
    series: [{
      name: '状态转移',
      type: 'heatmap',
      data: heatmapData,
      label: {
        show: true,
        fontSize: 10,
        color: '#333'
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };

  chart.setOption(option);
  chart.resize();
};



// 图表类型切换
const onChartTypeChange = () => {
  console.log('🔄 [图表] 切换图表类型:', chartType.value);
  // 确保图表完全清理后再重新渲染
  if (statusTransitionChart.value) {
    const chart = echarts.init(statusTransitionChart.value);
    chart.clear();
  }
  setTimeout(() => {
    renderStatusTransitionChart();
  }, 100);
};

// 刷新图表数据
const refreshChartData = async () => {
  loading.chart = true;
  console.log('🔄 [图表] 开始刷新状态转移图...');

  try {
    // 重新渲染图表
    await renderStatusTransitionChart();
    message.success('状态转移图刷新完成');
    console.log('✅ [图表] 状态转移图刷新完成');
  } catch (error) {
    console.error('❌ [图表] 刷新失败:', error);
    message.error('状态转移图刷新失败');
  } finally {
    loading.chart = false;
  }
};

// 主要方法
const refreshAnalytics = async () => {
  loading.summary = true;
  try {
    await Promise.all([
      loadLogStatistics(),
      loadLogData()
    ]);
    await nextTick();
    initCharts();
  } catch (error) {
    console.error('刷新分析数据失败:', error);
    message.error('刷新数据失败');
  } finally {
    loading.summary = false;
  }
};

// 加载日志统计数据
const loadLogStatistics = async () => {
  try {
    const queryParams = {
      start_time: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
      end_time: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'),
      area_code: selectedCityCode.value || userStore.getAreaCode
    };

    logStatisticsService.info.value = queryParams;
    const result = await logStatisticsService.doCreateNew('/api/dual-route-log-simple/statistics');

    if (result.success && result.data) {
      processStatisticsData(result.data);
    }
  } catch (error) {
    console.error('加载统计数据失败:', error);
  }
};

// 加载日志详细数据
const loadLogData = async () => {
  loading.table = true;
  try {
    const queryParams = {
      start_time: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
      end_time: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'),
      area_code: selectedCityCode.value || userStore.getAreaCode,
      keyword: searchKeyword.value,
      pageSize: tablePagination.pageSize,
      currentPage: tablePagination.current
    };

    logQueryService.info.value = queryParams;
    const result = await logQueryService.doCreateNew('/api/dual-route-log-simple/query');

    if (result.success) {
      tableData.value = result.data || [];
      tablePagination.total = result.pageInfo?.totalCount || 0;
    }
  } catch (error) {
    console.error('加载日志数据失败:', error);
  } finally {
    loading.table = false;
  }
};

// 事件处理函数
const onDateRangeChange = () => {
  message.info('时间范围已更新，正在刷新数据...');
  refreshCurrentCity();
};

// 处理统计数据
const processStatisticsData = (data: any[]) => {
  if (!Array.isArray(data)) return;

  // 计算总操作次数
  analyticsData.totalOperations = data.reduce((sum, item) => sum + (item.count || 0), 0);

  // 计算活跃用户数
  const uniqueUsers = new Set(data.map(item => item.operator_id)).size;
  analyticsData.activeUsers = uniqueUsers;

  // 计算操作高峰时段
  const hourStats = data.reduce((acc, item) => {
    const hour = item.operation_hour || 0;
    acc[hour] = (acc[hour] || 0) + (item.count || 0);
    return acc;
  }, {} as Record<number, number>);

  const peakHour = Object.entries(hourStats).reduce((max, [hour, count]) =>
    (count as number) > max.count ? { hour: parseInt(hour), count: count as number } : max,
    { hour: 0, count: 0 }
  );
  analyticsData.peakHour = `${peakHour.hour}:00-${peakHour.hour + 1}:00`;

  // 计算最活跃用户
  const userStats = data.reduce((acc, item) => {
    const userId = item.operator_name || item.operator_id;
    acc[userId] = (acc[userId] || 0) + (item.count || 0);
    return acc;
  }, {} as Record<string, number>);

  const mostActiveUser = Object.entries(userStats).reduce((max, [user, count]) =>
    (count as number) > max.count ? { user, count: count as number } : max,
    { user: '', count: 0 }
  );
  analyticsData.mostActiveUser = mostActiveUser.user;

  // 处理时间分布数据
  analyticsData.timeDistribution = processTimeDistribution(data);

  // 处理操作类型数据
  analyticsData.operationTypes = processOperationTypes(data);

  // 处理用户活跃度数据
  analyticsData.userActivity = processUserActivity(data);

  // 处理热度图数据
  analyticsData.heatmapData = processHeatmapData(data);
};

// 处理时间分布数据
const processTimeDistribution = (data: any[]) => {
  const timeStats = data.reduce((acc, item) => {
    let key = '';
    if (timeAnalysisType.value === 'hour') {
      key = `${item.operation_hour || 0}时`;
    } else if (timeAnalysisType.value === 'day') {
      key = item.operation_date || '';
    } else {
      // 按周处理
      const date = dayjs(item.operation_date);
      key = `第${date.week()}周`;
    }

    acc[key] = (acc[key] || 0) + (item.count || 0);
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(timeStats).map(([time, count]) => ({ time, count: count as number }));
};

// 处理操作类型数据
const processOperationTypes = (data: any[]) => {
  const typeStats = data.reduce((acc, item) => {
    const type = item.operation_type || 'unknown';
    acc[type] = (acc[type] || 0) + (item.count || 0);
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(typeStats).map(([type, count]) => ({
    type: getOperationTypeName(type),
    count: count as number,
    originalType: type
  }));
};

// 处理用户活跃度数据
const processUserActivity = (data: any[]) => {
  const userStats = data.reduce((acc, item) => {
    const user = item.operator_name || item.operator_id || 'unknown';
    if (!acc[user]) {
      acc[user] = { name: user, count: 0, types: new Set() };
    }
    acc[user].count += (item.count || 0);
    acc[user].types.add(item.operation_type);
    return acc;
  }, {} as Record<string, any>);

  return Object.values(userStats).map((user: any) => ({
    name: user.name,
    count: user.count,
    typeCount: user.types.size
  })).sort((a, b) => b.count - a.count);
};

// 处理热度图数据
const processHeatmapData = (data: any[]): Array<[number, string, number]> => {
  const heatmapStats = data.reduce((acc, item) => {
    const hour = item.operation_hour || 0;
    const type = item.operation_type || 'unknown';
    const key = `${hour}-${type}`;
    acc[key] = (acc[key] || 0) + (item.count || 0);
    return acc;
  }, {} as Record<string, number>);

  const result: Array<[number, string, number]> = [];
  for (let hour = 0; hour < 24; hour++) {
    const operationTypes = ['view', 'update', 'delete', 'create', 'risk_detection'];
    operationTypes.forEach(type => {
      const key = `${hour}-${type}`;
      result.push([hour, type, heatmapStats[key] || 0]);
    });
  }
  return result;
};

// 初始化图表
const initCharts = async () => {
  await nextTick();
  initTimeDistributionChart();
  initOperationTypeChart();
  initUserActivityChart();
  initHeatmapChart();
};

// 初始化时间分布图表
const initTimeDistributionChart = () => {
  if (!timeDistributionChart.value) return;

  const chart = echarts.init(timeDistributionChart.value);
  const option = {
    title: {
      text: '操作时间分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: analyticsData.timeDistribution.map(item => item.time)
    },
    yAxis: {
      type: 'value',
      name: '操作次数'
    },
    series: [{
      data: analyticsData.timeDistribution.map(item => item.count),
      type: 'bar',
      itemStyle: {
        color: '#1890ff'
      }
    }]
  };
  chart.setOption(option);
};

// 初始化操作类型图表
const initOperationTypeChart = () => {
  if (!operationTypeChart.value) return;

  const chart = echarts.init(operationTypeChart.value);
  const option = {
    title: {
      text: '操作类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: '60%',
      data: analyticsData.operationTypes.map(item => ({
        name: item.type,
        value: item.count
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  chart.setOption(option);
};

// 初始化用户活跃度图表
const initUserActivityChart = () => {
  if (!userActivityChart.value) return;

  const chart = echarts.init(userActivityChart.value);
  const topUsers = analyticsData.userActivity.slice(0, 10);

  const option = {
    title: {
      text: '用户活跃度排行',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: topUsers.map(item => item.name),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '操作次数'
    },
    series: [{
      data: topUsers.map(item => item.count),
      type: 'bar',
      itemStyle: {
        color: '#52c41a'
      }
    }]
  };
  chart.setOption(option);
};

// 初始化热度图
const initHeatmapChart = () => {
  if (!heatmapChart.value) return;

  const chart = echarts.init(heatmapChart.value);
  const hours = Array.from({ length: 24 }, (_, i) => i + '时');
  const operationTypes = ['查看', '修改', '删除', '创建', '风险检测'];

  const option = {
    title: {
      text: '操作热度分析',
      left: 'center'
    },
    tooltip: {
      position: 'top'
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: hours,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: operationTypes,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: Math.max(...analyticsData.heatmapData.map(item => item[2])),
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%'
    },
    series: [{
      name: '操作次数',
      type: 'heatmap',
      data: analyticsData.heatmapData,
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  chart.setOption(option);
};

// 事件处理方法



const refreshTimeAnalysis = () => {
  loadLogStatistics().then(() => {
    initTimeDistributionChart();
  });
};

const refreshUserAnalysis = () => {
  loadLogStatistics().then(() => {
    initUserActivityChart();
  });
};

const onSearch = () => {
  loadLogData();
};



const viewLogDetail = async (record: any) => {
  currentLogDetail.value = record;
  logDetailModalVisible.value = true;
};

const exportData = async () => {
  loading.export = true;
  try {
    // 导出逻辑
    message.success('数据导出成功');
  } catch (error) {
    message.error('数据导出失败');
  } finally {
    loading.export = false;
  }
};

// 初始化
onMounted(() => {
  refreshAnalytics();
});
</script>

<style scoped>
/* 精美双路由分析页面样式 */
.dual-route-analytics-modern {
  min-height: 100vh;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* 确保背景覆盖整个页面高度 */
.dual-route-analytics-modern::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    /* 纹理层 */
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    /* 噪点纹理 */
    repeating-linear-gradient(45deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.02) 2px,
      rgba(255, 255, 255, 0.02) 4px),
    /* 主背景 */
    linear-gradient(135deg, #f8faff 0%, #e6f1ff 100%);
  z-index: -2;
  pointer-events: none;
}

/* 精美页面头部 */
.elegant-header {
  position: relative;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  color: white;
  padding: 40px 0;
  margin-bottom: 0;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  z-index: 1;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.15) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.2) 2px, transparent 2px);
  background-size: 60px 60px;
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 32px;
  position: relative;
  z-index: 1;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 32px;
}

.title-icon {
  width: 64px;
  height: 64px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.title-text .page-title {
  font-size: 36px;
  font-weight: 800;
  margin: 0 0 8px 0;
  background: linear-gradient(45deg, #ffffff 0%, #ffd700 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.title-text .page-subtitle {
  font-size: 18px;
  opacity: 0.95;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.control-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.time-control {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 16px 24px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
}

.elegant-range-picker {
  min-width: 280px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.9);
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.elegant-button {
  height: 48px;
  padding: 0 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.elegant-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.elegant-button.primary {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-color: #ff6b6b;
  color: white;
}

.elegant-button.primary:hover {
  background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
  border-color: #ff5252;
}

/* 主要内容区域 */
.main-content {
  background: rgba(255, 255, 255, 0.98);
  margin: -20px 32px 32px 32px;
  border-radius: 24px 24px 0 0;
  box-shadow: 0 -4px 20px rgba(30, 58, 138, 0.1);
  position: relative;
  z-index: 2;
  padding: 32px;
  backdrop-filter: blur(20px);
  min-height: calc(100vh - 200px);
}

/* 地市选择器 */
.city-selector {
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 255, 0.9) 100%);
  border-radius: 16px;
  border: 1px solid rgba(30, 58, 138, 0.1);
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.08);
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selector-title {
  font-size: 20px;
  font-weight: 700;
  color: #1e3a8a;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.selector-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 当前地市数据 */
.current-city-data {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.city-info-card {
  padding: 20px;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #f0f2f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.query-progress-card {
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e8f4fd;
}

.query-progress-card h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.progress-text {
  margin-top: 12px;
  font-size: 14px;
  color: #1890ff;
}

/* 四个接口数据网格 */
.api-data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.api-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #f0f2f5;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.api-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.12);
}

.api-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-bottom: 1px solid #f0f2f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-count {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
  background: #e6f7ff;
  padding: 4px 8px;
  border-radius: 6px;
}

.api-content {
  padding: 20px;
}

.data-preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #f0f2f5;
}

.item-label {
  font-size: 13px;
  color: #8c8c8c;
  flex: 1;
}

.item-value {
  font-size: 13px;
  font-weight: 600;
  color: #1a1a1a;
  text-align: right;
}

.more-indicator {
  font-size: 12px;
  color: #8c8c8c;
  text-align: center;
  padding: 8px;
  font-style: italic;
}

.no-data {
  text-align: center;
  color: #8c8c8c;
  font-size: 14px;
  padding: 20px;
}

/* 状态转移图表区域 */
.chart-section {
  margin-top: 24px;
  padding: 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 20px;
  border: 2px solid #e8f4fd;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.chart-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 20%);
  pointer-events: none;
  z-index: 0;
}

.chart-section>* {
  position: relative;
  z-index: 1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f2f5;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.section-controls .ant-radio-group {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.section-controls .ant-radio-button-wrapper {
  border: none !important;
  background: transparent !important;
  color: #64748b !important;
  font-weight: 500 !important;
  padding: 8px 16px !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.section-controls .ant-radio-button-wrapper:hover {
  color: #3b82f6 !important;
  background: rgba(59, 130, 246, 0.1) !important;
}

.section-controls .ant-radio-button-wrapper-checked {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3) !important;
}

.section-controls .ant-radio-button-wrapper-checked:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
  color: white !important;
}

.chart-container-wrapper {
  margin-top: 24px;
}

.status-chart {
  height: 600px;
  width: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.status-chart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(120, 119, 198, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 119, 198, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

/* 未选择地市状态 */
.no-city-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-state {
  text-align: center;
  color: #8c8c8c;
}

.empty-state .empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

.empty-state h3 {
  font-size: 20px;
  margin: 0 0 8px 0;
  color: #595959;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
  color: #8c8c8c;
}

/* 详细表格区域 */
.detail-table-section {
  margin-top: 24px;
  padding: 32px;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  border-radius: 20px;
  border: 2px solid #f0f2f5;
}

.total-count {
  font-size: 14px;
  color: #666666;
  margin-right: 16px;
}

.table-container {
  margin-top: 24px;
}

.detail-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.status-transition {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.transition-arrow {
  color: #666666;
  font-weight: bold;
}

/* 筛选器样式 */
.filter-section {
  margin: 16px 0;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid #e8f4fd;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #666666;
  font-weight: 500;
  white-space: nowrap;
}

.filter-tabs {
  margin-bottom: 16px;
}

.filter-mode-selector {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 2px;
}

.filter-description {
  flex: 1;
  margin: 0 16px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 2px solid #f0f2f5;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.dashboard-summary {
  display: flex;
  gap: 32px;
  align-items: center;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.summary-label {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 500;
}

.summary-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
}

.summary-value.improved {
  color: #52c41a;
}

.summary-value.deteriorated {
  color: #ff4d4f;
}

/* 删除不需要的样式 */

/* 删除不需要的复杂样式，保持简洁 */

/* 城市底部 */
.city-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
  margin-top: 4px;
}

.update-time {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #8c8c8c;
}

/* 无数据状态 */
.city-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: #d9d9d9;
}

.empty-text {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.title-text .page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
}

.title-text .page-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-subtitle {
  margin: 0 0 8px 0;
  font-size: 14px;
  opacity: 0.9;
}

.sharding-info {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.sharding-info .ant-tag {
  margin: 0;
  font-size: 12px;
  border-radius: 4px;
}

.header-controls {
  flex-shrink: 0;
}

/* 现代化状态概览 */
.status-overview {
  margin-bottom: 32px;
  padding: 0 24px;
}

.status-cards {
  margin-bottom: 32px;
}

.status-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.status-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--status-color), var(--status-color-light));
}

.status-card.success {
  --status-color: #52c41a;
  --status-color-light: #73d13d;
}

.status-card.warning {
  --status-color: #faad14;
  --status-color-light: #ffc53d;
}

.status-card.error {
  --status-color: #ff4d4f;
  --status-color-light: #ff7875;
}

.status-card.default {
  --status-color: #8c8c8c;
  --status-color-light: #bfbfbf;
}

.status-card.processing {
  --status-color: #722ed1;
  --status-color-light: #9254de;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.status-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.status-info p {
  margin: 0;
  font-size: 12px;
  color: #8c8c8c;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-value {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.current-count {
  font-size: 32px;
  font-weight: 700;
  color: #262626;
  line-height: 1;
}

.unit {
  font-size: 14px;
  color: #8c8c8c;
}

.status-trend {
  display: flex;
  align-items: center;
  gap: 8px;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.trend-indicator.trend-up {
  color: #52c41a;
  background: #f6ffed;
}

.trend-indicator.trend-down {
  color: #ff4d4f;
  background: #fff2f0;
}

.trend-label {
  font-size: 12px;
  color: #8c8c8c;
}

.status-percentage {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.percentage-text {
  font-size: 12px;
  color: #8c8c8c;
}

.percentage-bar {
  height: 4px;
  background: #f5f5f5;
  border-radius: 2px;
  overflow: hidden;
}

.percentage-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.metric-card {
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.metric-card.remediation {
  --primary-color: #52c41a;
  --secondary-color: #73d13d;
}

.metric-card.confirmation {
  --primary-color: #1890ff;
  --secondary-color: #40a9ff;
}

.metric-card.improvement {
  --primary-color: #faad14;
  --secondary-color: #ffc53d;
}

.metric-card.deterioration {
  --primary-color: #f5222d;
  --secondary-color: #ff4d4f;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #262626;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-value {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.trend-value.positive {
  color: #52c41a;
  background: #f6ffed;
}

.trend-value.negative {
  color: #f5222d;
  background: #fff2f0;
}

.trend-label {
  font-size: 12px;
  color: #8c8c8c;
}

/* 现代化图表区域 */
.charts-section {
  margin-bottom: 32px;
  padding: 0 24px;
}

.chart-group {
  margin-bottom: 32px;
}

.chart-group.primary {
  margin-bottom: 40px;
}

.chart-card-modern {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-card-modern:hover {
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
}

.card-header {
  padding: 24px 24px 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-left p {
  margin: 0;
  font-size: 14px;
  color: #8c8c8c;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-selector {
  min-width: 100px;
}

.card-content {
  padding: 0 24px 24px 24px;
}

.chart-container {
  height: 320px;
  width: 100%;
}

.chart-container-large {
  height: 480px;
  width: 100%;
}

.sankey-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  font-size: 12px;
  color: #595959;
}

/* 现代化详细数据区域 */
.detail-section {
  margin-bottom: 32px;
  padding: 0 24px;
}

.detail-card-modern {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #595959;
  white-space: nowrap;
}

.action-group {
  display: flex;
  gap: 12px;
}

.data-summary {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-label {
  font-size: 14px;
  color: #8c8c8c;
}

.summary-value {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.modern-table-container {
  border-radius: 8px;
  overflow: hidden;
}

.modern-table {
  border-radius: 8px;
}

.status-transition {
  display: flex;
  align-items: center;
  gap: 8px;
}

.transition-arrow {
  color: #8c8c8c;
  font-size: 12px;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-main {
  font-size: 14px;
  color: #262626;
}

.time-sub {
  font-size: 12px;
  color: #8c8c8c;
}

.text-gray {
  color: #8c8c8c;
}

/* 转移汇总样式 */
.transition-summary {
  margin-bottom: 32px;
}

.summary-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.summary-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-period {
  font-size: 14px;
  color: #8c8c8c;
}

.transition-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.metric-item.positive {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border: 1px solid #b7eb8f;
}

.metric-item.negative {
  background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
  border: 1px solid #ffa39e;
}

.metric-item.neutral {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  border: 1px solid #91d5ff;
}

.metric-item.info {
  background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
  border: 1px solid #d3adf7;
}

.metric-item .metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.metric-item.positive .metric-icon {
  background: #52c41a;
}

.metric-item.negative .metric-icon {
  background: #ff4d4f;
}

.metric-item.neutral .metric-icon {
  background: #1890ff;
}

.metric-item.info .metric-icon {
  background: #722ed1;
}

.metric-data {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #262626;
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .cities-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .dashboard-summary {
    justify-content: center;
    gap: 24px;
  }

  .control-section {
    flex-direction: column;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .dual-route-analytics-modern {
    padding: 0;
  }

  .elegant-header {
    padding: 24px 0;
  }

  .header-content {
    padding: 0 16px;
  }

  .title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 24px;
  }

  .title-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .title-text .page-title {
    font-size: 28px;
  }

  .title-text .page-subtitle {
    font-size: 16px;
  }

  .control-section {
    flex-direction: column;
    gap: 16px;
  }

  .time-control {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .elegant-range-picker {
    min-width: auto;
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .elegant-button {
    width: 100%;
    justify-content: center;
  }

  .cities-dashboard {
    margin: -20px 16px 16px 16px;
    padding: 20px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .dashboard-title {
    font-size: 20px;
  }

  .dashboard-summary {
    flex-direction: column;
    gap: 16px;
  }

  .cities-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .city-card {
    padding: 20px;
  }

  .city-name {
    font-size: 18px;
  }

  .metric-row {
    gap: 8px;
  }

  .metric-item {
    padding: 10px;
  }

  .metric-value {
    font-size: 16px;
  }

  .trend-item {
    padding: 10px 12px;
  }

  .trend-value {
    font-size: 14px;
  }

  .city-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    text-align: center;
  }
}

/* 动画效果 */
.status-card {
  animation: fadeInUp 0.6s ease-out;
}

.status-card:nth-child(1) {
  animation-delay: 0.1s;
}

.status-card:nth-child(2) {
  animation-delay: 0.2s;
}

.status-card:nth-child(3) {
  animation-delay: 0.3s;
}

.status-card:nth-child(4) {
  animation-delay: 0.4s;
}

.chart-card-modern {
  animation: fadeInUp 0.8s ease-out;
}

.chart-card-modern:nth-child(1) {
  animation-delay: 0.2s;
}

.chart-card-modern:nth-child(2) {
  animation-delay: 0.4s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 动画效果 */
.city-card {
  animation: fadeInUp 0.6s ease-out;
}

.city-card:nth-child(1) {
  animation-delay: 0.1s;
}

.city-card:nth-child(2) {
  animation-delay: 0.2s;
}

.city-card:nth-child(3) {
  animation-delay: 0.3s;
}

.city-card:nth-child(4) {
  animation-delay: 0.4s;
}

.city-card:nth-child(5) {
  animation-delay: 0.5s;
}

.city-card:nth-child(6) {
  animation-delay: 0.6s;
}

.city-card:nth-child(7) {
  animation-delay: 0.7s;
}

.city-card:nth-child(8) {
  animation-delay: 0.8s;
}

.city-card:nth-child(9) {
  animation-delay: 0.9s;
}

.city-card:nth-child(10) {
  animation-delay: 1.0s;
}

.city-card:nth-child(11) {
  animation-delay: 1.1s;
}

.city-card:nth-child(12) {
  animation-delay: 1.2s;
}

.city-card:nth-child(13) {
  animation-delay: 1.3s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .dual-route-analytics-modern {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .elegant-header {
    background: linear-gradient(135deg, #434343 0%, #000000 100%);
  }

  .cities-dashboard {
    background: #1f1f1f;
    border: 1px solid #303030;
  }

  .city-card {
    background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
    border-color: #404040;
  }

  .city-card:hover {
    border-color: #667eea;
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
  }

  .dashboard-title,
  .city-name,
  .summary-value {
    color: #ffffff;
  }

  .summary-label,
  .city-code,
  .update-time {
    color: #8c8c8c;
  }

  .city-chart,
  .city-progress {
    background: #262626;
    border-color: #404040;
  }

  .progress-bar {
    background: #404040;
  }

  .metric-item.normal {
    background: linear-gradient(135deg, #1f3a1f 0%, #2d4a2d 100%);
    border-color: #52c41a;
    color: #73d13d;
  }

  .metric-item.warning {
    background: linear-gradient(135deg, #3a3a1f 0%, #4a4a2d 100%);
    border-color: #faad14;
    color: #ffc53d;
  }

  .metric-item.error {
    background: linear-gradient(135deg, #3a1f1f 0%, #4a2d2d 100%);
    border-color: #ff4d4f;
    color: #ff7875;
  }

  .metric-item.danger {
    background: linear-gradient(135deg, #3a1a1a 0%, #4a2a2a 100%);
    border-color: #f5222d;
    color: #ff4d4f;
  }

  .trend-item.improved {
    background: linear-gradient(135deg, #1f3a1f 0%, #2d4a2d 100%);
    border-color: #52c41a;
    color: #73d13d;
  }

  .trend-item.deteriorated {
    background: linear-gradient(135deg, #3a1f1f 0%, #4a2d2d 100%);
    border-color: #ff4d4f;
    color: #ff7875;
  }

  .city-footer {
    border-top-color: #404040;
  }
}

/* Ant Design 组件样式覆盖 */
:deep(.ant-card) {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px 16px 24px;
  border-radius: 16px 16px 0 0;
}

:deep(.ant-card-head-title) {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

:deep(.ant-card-body) {
  padding: 24px;
}

:deep(.ant-table) {
  border-radius: 8px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
  color: #262626;
}

:deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f5f5f5;
  padding: 16px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f8f9fa;
}

:deep(.ant-tag) {
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border: none;
}

:deep(.ant-progress-line) {
  border-radius: 4px;
}

:deep(.ant-progress-bg) {
  border-radius: 4px;
}

:deep(.ant-select) {
  border-radius: 8px;
}

:deep(.ant-input) {
  border-radius: 8px;
}

:deep(.ant-btn) {
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.ant-btn:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #3b82f6 0%, #1e3a8a 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

:deep(.ant-pagination) {
  margin-top: 24px;
}

:deep(.ant-pagination-item) {
  border-radius: 6px;
}

:deep(.ant-pagination-item-active) {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-color: #1890ff;
}

:deep(.ant-modal) {
  border-radius: 16px;
  overflow: hidden;
}

:deep(.ant-modal-header) {
  border-radius: 16px 16px 0 0;
  padding: 20px 24px;
}

:deep(.ant-modal-body) {
  padding: 24px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 600;
  color: #595959;
}

:deep(.ant-descriptions-item-content) {
  color: #262626;
}

/* 帮助图标样式 */
.help-icon {
  color: #8c8c8c;
  font-size: 14px;
  cursor: help;
  margin-left: 8px;
}

.help-icon:hover {
  color: #1890ff;
}

/* 异常信息样式 */
.exception-text {
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  color: #333;
}

.exception-text:hover {
  color: #1890ff;
}

/* 核心数据展示区域样式 */
.core-data-section {
  margin-bottom: 24px;
}

/* 状态分布卡片样式 - 占据整行 */
.status-distribution-full-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 255, 0.95) 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 6px 16px rgba(30, 58, 138, 0.08);
  border: 1px solid rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(10px);
}

.status-distribution-full-card .card-header {
  margin-bottom: 20px;
  text-align: center;
}

.status-distribution-full-card .card-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1e3a8a;
}

.status-distribution-full-card .card-subtitle {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.status-full-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-full-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.status-full-item:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.status-full-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--status-color);
}

.status-full-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.status-full-info {
  margin-bottom: 16px;
}

.status-full-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.status-full-count {
  font-size: 24px;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 4px;
}

.status-full-percentage {
  font-size: 14px;
  color: #999;
  font-weight: 500;
}

.status-full-bar {
  height: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.status-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s ease;
  position: relative;
}

.status-bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* 综合分析区域 */
.comprehensive-analysis-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

/* 改善分析卡片 */
.improvement-analysis-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 249, 255, 0.95) 100%);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 6px 16px rgba(30, 58, 138, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.2);
  backdrop-filter: blur(10px);
}

/* 问题分析卡片 */
.problem-analysis-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 248, 240, 0.95) 100%);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 6px 16px rgba(30, 58, 138, 0.08);
  border: 1px solid rgba(255, 215, 0, 0.3);
  backdrop-filter: blur(10px);
}

/* 联合分析卡片 */
.joint-analysis-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 255, 0.95) 100%);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 6px 16px rgba(30, 58, 138, 0.08);
  border: 1px solid rgba(30, 58, 138, 0.15);
  backdrop-filter: blur(10px);
}

.card-header {
  margin-bottom: 20px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e3a8a;
}

.card-subtitle {
  font-size: 13px;
  color: #666;
  margin-top: 6px;
}

/* 图表网格布局 */
.improvement-charts-grid,
.problem-charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.joint-analysis-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-container {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.chart-container.large {
  min-height: 350px;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

/* 图表容器尺寸 */
.specialty-improvement-chart,
.district-improvement-chart,
.specialty-problem-chart,
.district-problem-chart {
  height: 280px;
  width: 100%;
}

.joint-heatmap-chart,
.trend-comparison-chart {
  height: 300px;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .joint-analysis-container {
    grid-template-columns: 1fr;
  }

  .joint-heatmap-chart,
  .trend-comparison-chart {
    height: 280px;
  }
}

@media (max-width: 1200px) {

  .improvement-charts-grid,
  .problem-charts-grid {
    grid-template-columns: 1fr;
  }

  .specialty-improvement-chart,
  .district-improvement-chart,
  .specialty-problem-chart,
  .district-problem-chart {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .status-full-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    padding: 12px;
  }

  .specialty-improvement-chart,
  .district-improvement-chart,
  .specialty-problem-chart,
  .district-problem-chart {
    height: 220px;
  }

  .joint-heatmap-chart,
  .trend-comparison-chart {
    height: 200px;
  }

  .status-full-item {
    padding: 16px;
  }

  .status-full-count {
    font-size: 20px;
  }
}

/* 全局优化样式 */
:deep(.ant-select-selector) {
  border-color: rgba(30, 58, 138, 0.2) !important;
}

:deep(.ant-select-selector:hover) {
  border-color: rgba(59, 130, 246, 0.4) !important;
}

:deep(.ant-select-focused .ant-select-selector) {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

:deep(.ant-radio-button-wrapper) {
  border-color: rgba(30, 58, 138, 0.2);
}

:deep(.ant-radio-button-wrapper-checked) {
  background: linear-gradient(135deg, #3b82f6 0%, #1e3a8a 100%);
  border-color: #3b82f6;
  color: white;
}

:deep(.ant-table-thead > tr > th) {
  background: linear-gradient(135deg, rgba(248, 250, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 100%);
  color: #1e3a8a;
  font-weight: 600;
}

/* 确保页面背景在所有情况下都能覆盖 */
html,
body {
  background:
    /* 纹理层 */
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
    /* 主背景 */
    linear-gradient(135deg, #f8faff 0%, #e6f1ff 100%) !important;
  background-attachment: fixed !important;
}

/* 为超长页面添加额外的背景保护 */
.dual-route-analytics-modern::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 100%;
  background:
    /* 微妙的噪点 */
    repeating-conic-gradient(from 0deg at 50% 50%,
      transparent 0deg,
      rgba(255, 255, 255, 0.01) 1deg,
      transparent 2deg),
    /* 织物纹理 */
    repeating-linear-gradient(0deg,
      transparent,
      transparent 1px,
      rgba(30, 58, 138, 0.02) 1px,
      rgba(30, 58, 138, 0.02) 2px),
    repeating-linear-gradient(90deg,
      transparent,
      transparent 1px,
      rgba(30, 58, 138, 0.02) 1px,
      rgba(30, 58, 138, 0.02) 2px),
    /* 主背景 */
    linear-gradient(135deg, #f8faff 0%, #e6f1ff 100%);
  z-index: -3;
  pointer-events: none;
}

/* 动画效果 */
@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-10px) rotate(1deg);
  }

  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}
</style>
