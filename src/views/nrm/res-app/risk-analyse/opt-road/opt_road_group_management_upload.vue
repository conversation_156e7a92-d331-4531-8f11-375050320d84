<template>
  <a-modal v-model:visible="show" :title="'导入光路文件'" :width="800" :confirmLoading="uploadLoading">
    <template #footer>
      <a-button key="cancel" @click="onCancel">取消</a-button>
      <a-button key="submit" type="primary" :loading="uploadLoading" @click="onConfirm" :disabled="!hasSelectedFile">
        确定导入
      </a-button>
    </template>
    <a-card>
      <a-form
        ref="formRef"
        :layout="'horizontal'"
        :labelCol="{ span: 4 }"
        :wrapperCol="{ span: 14 }"
      >
        <a-form-item label="选择文件">
          <a-upload
            v-model:file-list="fileList"
            name="file"
            :before-upload="beforeUpload"
            @change="handleChange"
            @remove="handleRemove"
            :multiple="false"
            accept=".xlsx,.xls"
          >
            <a-button>
              <UploadOutlined />
              选择Excel文件
            </a-button>
          </a-upload>
          <div style="margin-top: 8px; color: #666; font-size: 12px;">
            支持格式：.xlsx、.xls，文件大小不超过10MB
          </div>
        </a-form-item>
        <a-form-item>
          <a-button size="large" type="link"
            ><a
              href="/resource/光路风险分析导入模板.xlsx"
              download="/resource/光路风险分析导入模板.xlsx"
              target="/resource/光路风险分析导入模板.xlsx"
              >下载模板(光路风险分析导入模板.xlsx)</a
            >
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </a-modal>
</template>
<script lang="ts" setup>
  import { defineProps, defineEmits, ref, onMounted, onActivated, computed } from 'vue';
  import { FormInstance } from 'ant-design-vue';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
  import { useVModel } from '@/hooks/web/useModel';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useUserStore } from '@/store/modules/user';
  import axios from 'axios';

  const { createMessage } = useMessage();
  const userStore = useUserStore();
  const formRef = ref<FormInstance>();

  // 状态管理
  const fileList = ref<UploadFile[]>([]);
  const uploadLoading = ref(false);
  const selectedFile = ref<File | null>(null);

  // 计算属性：是否已选择文件
  const hasSelectedFile = computed(() => {
    return fileList.value.length > 0 && selectedFile.value !== null;
  });

  // 文件选择前的校验
  const beforeUpload = (file: File) => {
    // 文件类型校验
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                   file.type === 'application/vnd.ms-excel' ||
                   file.name.endsWith('.xlsx') ||
                   file.name.endsWith('.xls');

    if (!isExcel) {
      createMessage.error('只能上传Excel文件(.xlsx, .xls)！');
      return false;
    }

    // 文件大小校验（10MB）
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      createMessage.error('文件大小不能超过10MB！');
      return false;
    }

    // 保存选中的文件
    selectedFile.value = file;
    return false; // 阻止自动上传
  };

  // 文件列表变化处理
  const handleChange = (info: UploadChangeParam) => {
    fileList.value = info.fileList.slice(-1); // 只保留最后一个文件
  };

  // 移除文件处理
  const handleRemove = () => {
    selectedFile.value = null;
    fileList.value = [];
  };

  const props = defineProps<{ filter: any; show: boolean }>();

  const emit = defineEmits(['update:show', 'onFinish']);
  const show = useVModel(props, emit, 'show');



  onMounted(() => {});

  onActivated(async () => {});

  // 确定导入
  const onConfirm = async () => {
    if (!selectedFile.value) {
      createMessage.warning('请先选择要导入的文件！');
      return;
    }

    try {
      uploadLoading.value = true;

      // 创建FormData
      const formData = new FormData();
      formData.append('file', selectedFile.value);

      // 添加筛选参数
      Object.keys(props.filter).forEach(key => {
        if (props.filter[key] !== null && props.filter[key] !== undefined) {
          formData.append(key, props.filter[key]);
        }
      });

      // 添加用户信息作为备用
      const userId = userStore.userId;
      const username = userStore.username;
      const areaCode = userStore.getAreaCode;

      console.log('🔍 前端用户信息检查:');
      console.log('  - userStore.userId:', userId);
      console.log('  - userStore.username:', username);
      console.log('  - userStore.getAreaCode:', areaCode);
      console.log('  - userStore完整信息:', userStore.getUserInfo);

      // 地市映射表
      const areaCodeMap = {
        'js': '江苏',
        'nj': '南京',
        'wx': '无锡',
        'xz': '徐州',
        'cz': '常州',
        'sz': '苏州',
        'nt': '南通',
        'lyg': '连云港',
        'ha': '淮安',
        'yc': '盐城',
        'yz': '扬州',
        'zj': '镇江',
        'tz': '泰州',
        'sq': '宿迁'
      };

      // 尝试多种方式获取用户信息
      const userInfo = userStore.getUserInfo;
      let finalUserId = userId || userInfo?.id || userInfo?.userId;
      let finalUsername = username || userInfo?.username || userInfo?.realName || userInfo?.account;
      let finalAreaCode = areaCode || userInfo?.areaCode;

      console.log('🔍 最终用户信息:');
      console.log('  - finalUserId:', finalUserId);
      console.log('  - finalUsername:', finalUsername);
      console.log('  - finalAreaCode:', finalAreaCode);

      if (finalUserId && finalUsername) {
        const operatorId = finalUserId;
        const areaName = areaCodeMap[finalAreaCode] || finalAreaCode || '未知地区';
        const operatorName = `${finalUsername}@@@${areaName}`;

        formData.append('operator_id', operatorId);
        formData.append('operator_name', operatorName);

        console.log('🎯 添加用户信息到导入请求:');
        console.log('  - operatorId:', operatorId);
        console.log('  - operatorName:', operatorName);
      } else {
        console.warn('⚠️ 无法获取完整的用户信息，将依赖后端JWT获取');
      }

      console.log('开始上传文件:', selectedFile.value.name);

      // 上传文件
      const response = await axios.post('/graph-rest-api/api/opt-group-api/upload_opt_group', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('上传响应:', response);

      // 成功提示
      createMessage.success(`文件 ${selectedFile.value.name} 导入成功！`);

      // 关闭弹窗并刷新数据
      onClose();

    } catch (error) {
      console.error('文件上传失败:', error);
      createMessage.error('文件导入失败，请检查文件格式或重试！');
    } finally {
      uploadLoading.value = false;
    }
  };

  // 取消操作
  const onCancel = () => {
    // 重置状态
    selectedFile.value = null;
    fileList.value = [];
    uploadLoading.value = false;
    // 关闭弹窗
    show.value = false;
  };

  // 关闭弹窗
  const onClose = () => {
    // 重置状态
    selectedFile.value = null;
    fileList.value = [];
    uploadLoading.value = false;
    // 关闭弹窗并触发刷新
    show.value = false;
    emit('onFinish');
  };
</script>
