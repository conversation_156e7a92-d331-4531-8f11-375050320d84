<template>
  <PageWrapper>
    <a-card :bordered="true" ref="container" style="width: 100%; height: 100%">
      <a-checkbox-group v-model:value="checkedList" @change="onCheckAllChange">
        <a-checkbox
          v-for="item in plainOptions_color"
          :key="item.value"
          :value="item.value"
          :style="{ color: item.color }"
        >
          <span>{{ item.value }}</span>
        </a-checkbox>
      </a-checkbox-group>

      <span style="float: right">
        <!-- <a-button
          @click="
            () => {
              emits('recheck');
            }
          "
          :loading="loading"
          >重新检测</a-button
        > -->
        <a-button @click="compdis">测距</a-button>
      </span>
      <div id="mapDomRef" style="width: 100%; height: 550px"></div>
    </a-card>

    <!-- 当前设备 -->
    <!-- 当前链路信息 -->
    <div style="display: none">
      <div ref="dom_pipe_segment_info_ref">
        <span>管道段：{{ data_current_pipe_segment.code }}</span>
        <br />
        <span>电路编号:</span>
        <li v-for="(item, index) in data_current_pipe_segment.mstp_circuits" :key="index">
          {{ item.code }}
        </li>
        <span>光路编号:</span>
        <li v-for="(item, index) in data_current_pipe_segment.opt_road_list" :key="index">
          {{ item.code }}
        </li>
      </div>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup>
  import {
    ref,
    onMounted,
    nextTick,
    watchEffect,
    onActivated,
    defineExpose,
    defineEmits,
  } from 'vue';
  import { PageWrapper } from '../../../../../components/Page';
  import { useGis } from '../../../../../hooks/gis/useGisV2';
  import { d3_category437 } from '@/views/nrm/res-app/risk-analyse/color_lib';

  const emits = defineEmits(['recheck']);
  const loading = ref(false);

  const setLoading = (value: any) => {
    loading.value = value;
  };

  declare const Ai: any;
  // const { info, dictionary, loading } = infoService;
  const data_gis = ref<any>({});
  const getdevIcon = (d) => {
    /*let divIconHTML = `<div style="text-align: center;background-color:rgba(0, 0, 0, 0)">
              <span style="color:black;">`+d.position+`</span>
              </div>`;*/
    /**
     * 1020200001
     * 1020200002
     * 1020280003
     * 1020300002
     * 1020300001
     */
    let divIconHTML = `<img src="/resource/img/B.png" style="width: 30px;height: 30px"/>` + d.name;
    if (
      d.spec_id == 1020200001 ||
      d.spec_id == 1020200002 ||
      d.spec_id == 1020200003 ||
      d.spec_id == 1020300002 ||
      d.spec_id == 1020300001
    ) {
      divIconHTML = `<img src="/resource/gis图标/1010200004.png" style="width: 10px;height: 10px"/>`;
    }

    const divIcon = new Ai.DivIcon({
      html: divIconHTML,
      iconSize: null,
      className: 'no-border-icon',
    });
    return divIcon;
  };

  const compdis = async () => {
    distince();
  };

  const data_current_pipe_segment = ref<any>({});
  const dom_pipe_segment_info_ref = ref<any>();

  const {
    drawData,
    clearHightLights,
    hightLightDatas,
    popup,
    flyToBounds,
    flyToBoundsByLatLng,
    distince,
  } = useGis('mapDomRef', {
    room_layer: {
      type: 'point',
      events: {
        click: (d) => {
          //console.log(d)
          popup(d);
        },
      },
    },
    device_layer: {
      type: 'point',
      style: (d) => {
        console.log(d.id, 'd.name');
        return {
          icon: getdevIcon(d),
        };
      },
      popup: (d) => {
        console.log('d.name:', d.name);
        return {
          content: d.name,
          latLng: { lat: parseFloat(d.pos_y), lng: parseFloat(d.pos_x) },
        };
      },
      events: {
        click: (d) => {
          //console.log(d)
          popup(d);
        },
      },
    },
    jxgx_layer: {
      type: 'line',
      style: (d) => {
        {
          return {
            weight: 1,
            color: 'gray',
            opacity: 0.7,
          };
        }
      },
      highLightStyle: (d) => {
        return {
          weight: 1,
          color: '#d6de0f',
        };
      },
      popup: (d) => {
        console.log('d:', d);
      },
    },
    default: {
      type: 'line',
      style: (d) => {
        {
          return {
            weight: 4,
            color: d.color,
            opacity: 0.7,
          };
        }
      },
      highLightStyle: (d) => {
        return {
          weight: 10,
          color: '#d6de0f',
        };
      },
      popup: (d) => {
        console.log('d:', d);
        data_current_pipe_segment.value = d;
        return {
          content: dom_pipe_segment_info_ref.value,
          latLng: {
            lat: (parseFloat(d.a_pos_y) + parseFloat(d.z_pos_y)) / 2,
            lng: (parseFloat(d.a_pos_x) + parseFloat(d.z_pos_x)) / 2,
          },
        };
      },
    },
  });
  const plainOptions = ref([]);
  const checkedList = ref([]);
  // let aa=defineEmits(['customEvent'])
  const pipeSegments: any = ref([]);
  declare const d3: any;
  const colorScale = d3_category437;
  const plainOptions_color: any = ref([]);

  const setColors = async (optCodeList: any) => {
    plainOptions_color.value = [];
    optCodeList.forEach((current_optCode) => {
      const opt_index = plainOptions.value.findIndex((opt) => opt === current_optCode); //确定颜色
      let color = colorScale[opt_index];
      const current_Optioin = {
        value: current_optCode,
        color: color,
      };
      plainOptions_color.value.push(current_Optioin);
    });
  };

  const showOptRoads = async (optCodeList: any) => {
    let optRoadColors: any = {};
    optCodeList.forEach((current_optCode) => {
      const opt_index = plainOptions.value.findIndex((opt) => opt === current_optCode); //确定颜色
      let color = colorScale[opt_index];
      optRoadColors[current_optCode] = color;
    });
    const pipeSegmentList = data_gis.value.pipeSegments;
    for (let i = 0; i < pipeSegmentList.length; i++) {
      let pipeSegment = pipeSegmentList[i];
      let opt_road_list = pipeSegment['opt_road_list'];
      if (opt_road_list && opt_road_list.length > 1) {
        pipeSegment.color = 'red';
      } else if (opt_road_list && opt_road_list.length == 1) {
        let optCode = opt_road_list[0].code;
        pipeSegment.color = optRoadColors[optCode];
      }
      pipeSegments.value.push(pipeSegment);
      // todo
    }
  };

  const onCheckAllChange = async () => {
    showOptRoads(checkedList.value);
    drawData(pipeSegments.value, 'default');
    flyToBounds('default');
  };

  const drawDatas = (data) => {
    console.log('绘制数据', data);
    data_gis.value = data;
    plainOptions.value = data_gis.value.opt_code_list;
    setColors(plainOptions.value);
    showOptRoads(plainOptions.value);
    console.log('绘制管道段:', pipeSegments.value);
    drawData(pipeSegments.value, 'default');
    //  drawData(data_gis.value.localFiberOpts, 'logicfiber_layer');
    drawData(data_gis.value['devices'], 'device_layer');

    // drawData(data_gis.value['jxgxList'], 'jxgx_layer');
    flyToBounds('default');
  };
  const hightlights = (l) => {
    clearHightLights();
    hightLightDatas(l, 'default');
    const d = l[0];
    flyToBoundsByLatLng(d.a_pos_y, d.a_pos_x, d.z_pos_y, d.z_pos_x);
    console.log('click', d.a_pos_y, d.a_pos_x, d.z_pos_y, d.z_pos_x, '这里');
  };

  const hightlights_opt = (l) => {
    clearHightLights();
    hightLightDatas(l, 'default');
    flyToBounds('default');
  };

  defineExpose({ drawDatas, hightlights, hightlights_opt, setLoading });

  watchEffect(async () => {
    // current_circuits.value = props.current_circuits;
    // console.log(current_circuits.value,"props")
  });

  onMounted(async () => {
    // aa('customEvent',data_gis.value)
    nextTick(() => {
      // drawMap(AK.ak);
      // console.log('layers' ,layers)
      // drawMap()
    });
  });

  onActivated(async () => {
    // setTitle('异常风险检测');
    // await getInfo()
  });
</script>

<style scoped>
  .axis path,
  .axis line {
    fill: none;
    stroke: black;
    shape-rendering: crispEdges;
  }

  #mapRef {
    width: 100%;
    height: 100%;
    margin: 0;
  }
  .axis text {
    font-family: sans-serif;
    font-size: 11px;
  }
  html,
  body,
  #map {
    width: 100%;
    height: 100%;
    margin: 0;
  }
  span {
    all: unset;
  }
  .aimap-marker-icon {
    border-radius: initial;
  }
  .transparent-bg {
    background-color: transparent;
  }
</style>
