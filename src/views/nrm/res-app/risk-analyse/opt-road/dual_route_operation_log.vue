<template>
  <page-wrapper>
    <!-- 搜索区域 -->
    <div class="search-container">
      <div class="filter-section">
        <a-row :gutter="[24, 16]">
          <!-- 第一行：时间范围 + 操作人 -->
          <a-col :span="12">
            <div class="filter-group">
              <span class="filter-label">时间范围：</span>
              <a-space size="small">
                <a-range-picker
                  v-model:value="timeRange"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="['开始时间', '结束时间']"
                  style="width: 300px"
                  class="filter-input"
                  show-time
                />
              </a-space>
            </div>
          </a-col>

          <a-col :span="12">
            <div class="filter-group">
              <span class="filter-label">操作人：</span>
              <a-space size="small">
                <a-input
                  v-model:value="filter.operator_name"
                  placeholder="操作人姓名"
                  style="width: 140px"
                  class="filter-input"
                />
                <a-input
                  v-model:value="filter.operator_id"
                  placeholder="操作人ID"
                  style="width: 140px"
                  class="filter-input"
                />
              </a-space>
            </div>
          </a-col>

          <!-- 第二行：操作类型 + 对象类型 -->
          <a-col :span="12">
            <div class="filter-group">
              <span class="filter-label">操作类型：</span>
              <a-space size="small">
                <a-select
                  v-model:value="filter.operation_type"
                  style="width: 140px"
                  placeholder="操作类型"
                  class="filter-select"
                  allow-clear
                >
                  <a-select-option value="view">查看详情</a-select-option>
                  <a-select-option value="create">新增</a-select-option>
                  <a-select-option value="update">修改</a-select-option>
                  <a-select-option value="delete">删除</a-select-option>
                  <a-select-option value="risk_detect">风险检测</a-select-option>
                  <a-select-option value="export">导出</a-select-option>
                  <a-select-option value="import">导入</a-select-option>
                  <a-select-option value="ignore_risk">忽略风险</a-select-option>
                  <a-select-option value="restore_risk">恢复风险</a-select-option>
                </a-select>
                
                <a-select
                  v-model:value="filter.object_type"
                  style="width: 140px"
                  placeholder="对象类型"
                  class="filter-select"
                  allow-clear
                >
                  <a-select-option value="opt_group">光路组</a-select-option>
                  <a-select-option value="opt_element">光路元素</a-select-option>
                  <a-select-option value="risk_check">风险检测</a-select-option>
                </a-select>
              </a-space>
            </div>
          </a-col>

          <a-col :span="12">
            <div class="filter-group">
              <span class="filter-label">对象信息：</span>
              <a-space size="small">
                <a-input
                  v-model:value="filter.object_code"
                  placeholder="对象编码"
                  style="width: 140px"
                  class="filter-input"
                />
                <a-input
                  v-model:value="filter.object_name"
                  placeholder="对象名称"
                  style="width: 140px"
                  class="filter-input"
                />
              </a-space>
            </div>
          </a-col>

          <!-- 第三行：地市 + 关键字搜索 -->
          <a-col :span="12">
            <div class="filter-group">
              <span class="filter-label">地市：</span>
              <a-space size="small">
                <a-select
                  v-if="canSwitchCity"
                  v-model:value="selectedCityCode"
                  style="width: 140px"
                  placeholder="选择地市"
                  class="filter-select"
                  allow-clear
                >
                  <a-select-option value="">全省</a-select-option>
                  <a-select-option value="wx">无锡市</a-select-option>
                  <a-select-option value="nj">南京市</a-select-option>
                  <a-select-option value="sz">苏州市</a-select-option>
                  <a-select-option value="xz">徐州市</a-select-option>
                  <a-select-option value="cz">常州市</a-select-option>
                  <a-select-option value="nt">南通市</a-select-option>
                  <a-select-option value="lyg">连云港市</a-select-option>
                  <a-select-option value="ha">淮安市</a-select-option>
                  <a-select-option value="yc">盐城市</a-select-option>
                  <a-select-option value="yz">扬州市</a-select-option>
                  <a-select-option value="zj">镇江市</a-select-option>
                  <a-select-option value="tz">泰州市</a-select-option>
                  <a-select-option value="sq">宿迁市</a-select-option>
                </a-select>
                <span v-else class="city-display-text">
                  {{ getCurrentCityName() }}
                </span>
              </a-space>
            </div>
          </a-col>

          <a-col :span="12">
            <div class="filter-group">
              <span class="filter-label">区域编码：</span>
              <a-space size="small">
                <a-input
                  v-model:value="filter.area_code"
                  placeholder="区域编码过滤"
                  style="width: 120px"
                  class="filter-input"
                />
                <a-input
                  v-model:value="filter.keyword"
                  placeholder="搜索操作描述、对象名称等"
                  style="width: 180px"
                  class="filter-input"
                />
              </a-space>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <div class="action-buttons">
          <a-space size="middle">
            <a-button type="primary" @click="query()" :loading="loading" class="action-btn primary-btn">
              <SearchOutlined />
              查询
            </a-button>
            <a-button @click="resetFilter()" class="action-btn secondary-btn">
              <ReloadOutlined />
              重置
            </a-button>
            <a-button @click="exportLogs()" :loading="exportLoading" class="action-btn secondary-btn">
              <DownloadOutlined />
              导出日志
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 日志表格 -->
    <a-card>
      <a-table
        :dataSource="dataSource"
        :columns="columns"
        size="small"
        :pagination="pagination"
        @change="handleTableChange"
        :loading="loading"
        :row-key="(record) => record.id"
        bordered
        :scroll="{ y: 500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation_time'">
            {{ formatToDateTime(record.operation_time) }}
          </template>

          <template v-else-if="column.key === 'operation_type'">
            <a-tag :color="getOperationTypeColor(record.operation_type)">
              {{ getOperationTypeName(record.operation_type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'object_type'">
            <a-tag color="blue">
              {{ getObjectTypeName(record.object_type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'operation_result'">
            <a-tag :color="record.operation_result === 'success' ? 'green' : 'red'">
              {{ record.operation_result === 'success' ? '成功' : '失败' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-button type="link" @click="showLogDetail(record)">
              <EyeOutlined />
              详情
            </a-button>
          </template>

          <template v-else>
            <a-tooltip :title="record[column.key]" placement="topLeft">
              <span>{{ record[column.key] || '-' }}</span>
            </a-tooltip>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 日志详情抽屉 -->
    <a-drawer
      v-model:visible="detailDrawerVisible"
      title="操作日志详情"
      placement="right"
      :width="800"
      :closable="true"
      :mask-closable="true"
      class="log-detail-drawer"
    >
      <div class="log-detail-content" v-if="currentLogRecord">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <InfoCircleOutlined />
            基本信息
          </h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">日志ID：</span>
              <span class="value">{{ currentLogRecord.id }}</span>
            </div>
            <div class="detail-item">
              <span class="label">操作时间：</span>
              <span class="value">{{ formatToDateTime(currentLogRecord.operation_time) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">操作人：</span>
              <span class="value">{{ currentLogRecord.operator_name }}({{ currentLogRecord.operator_id }})</span>
            </div>
            <div class="detail-item">
              <span class="label">操作IP：</span>
              <span class="value">{{ currentLogRecord.operator_ip || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">操作类型：</span>
              <span class="value">
                <a-tag :color="getOperationTypeColor(currentLogRecord.operation_type)">
                  {{ getOperationTypeName(currentLogRecord.operation_type) }}
                </a-tag>
              </span>
            </div>
            <div class="detail-item">
              <span class="label">对象类型：</span>
              <span class="value">
                <a-tag color="blue">
                  {{ getObjectTypeName(currentLogRecord.object_type) }}
                </a-tag>
              </span>
            </div>
            <div class="detail-item">
              <span class="label">对象ID：</span>
              <span class="value">{{ currentLogRecord.object_id || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">对象编码：</span>
              <span class="value">{{ currentLogRecord.object_code || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">对象名称：</span>
              <span class="value">{{ currentLogRecord.object_name || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">操作结果：</span>
              <span class="value">
                <a-tag :color="currentLogRecord.operation_result === 'success' ? 'green' : 'red'">
                  {{ currentLogRecord.operation_result === 'success' ? '成功' : '失败' }}
                </a-tag>
              </span>
            </div>
            <div class="detail-item">
              <span class="label">操作模块：</span>
              <span class="value">{{ currentLogRecord.operation_module || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">地市编码：</span>
              <span class="value">{{ currentLogRecord.area_code || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">地市名称：</span>
              <span class="value">{{ currentLogRecord.area_name || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">专业类型：</span>
              <span class="value">{{ currentLogRecord.speciality || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">会话ID：</span>
              <span class="value">{{ currentLogRecord.session_id || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">执行耗时：</span>
              <span class="value">{{ currentLogRecord.execution_time_ms ? currentLogRecord.execution_time_ms + 'ms' : '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatToDateTime(currentLogRecord.create_time) }}</span>
            </div>
          </div>
        </div>

        <!-- 操作描述 -->
        <div class="detail-section">
          <h3 class="section-title">
            <FileTextOutlined />
            操作描述
          </h3>
          <div class="description-content">
            {{ currentLogRecord.operation_description || '-' }}
          </div>
        </div>

        <!-- 数据变更（如果有） -->
        <div class="detail-section" v-if="currentLogRecord.before_data || currentLogRecord.after_data">
          <h3 class="section-title">
            <DiffOutlined />
            数据变更
          </h3>
          <div class="data-change-content">
            <div class="data-section" v-if="currentLogRecord.before_data">
              <h4>修改前：</h4>
              <pre class="json-content">{{ formatJSON(currentLogRecord.before_data) }}</pre>
            </div>
            <div class="data-section" v-if="currentLogRecord.after_data">
              <h4>修改后：</h4>
              <pre class="json-content">{{ formatJSON(currentLogRecord.after_data) }}</pre>
            </div>
            <div class="data-section" v-if="currentLogRecord.changed_fields">
              <h4>变更字段：</h4>
              <div class="changed-fields">
                <a-tag v-for="field in parseChangedFields(currentLogRecord.changed_fields)" :key="field" color="orange">
                  {{ field }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 错误信息 -->
        <div class="detail-section" v-if="currentLogRecord.error_message">
          <h3 class="section-title">
            <ExclamationCircleOutlined />
            错误信息
          </h3>
          <div class="error-content">
            <a-alert
              :message="currentLogRecord.error_message"
              type="error"
              show-icon
            />
          </div>
        </div>

        <!-- 请求参数 -->
        <div class="detail-section" v-if="currentLogRecord.request_params">
          <h3 class="section-title">
            <CodeOutlined />
            请求参数
          </h3>
          <div class="json-content-wrapper">
            <pre class="json-content">{{ formatJSON(currentLogRecord.request_params) }}</pre>
          </div>
        </div>

        <!-- 响应数据 -->
        <div class="detail-section" v-if="currentLogRecord.response_data">
          <h3 class="section-title">
            <ApiOutlined />
            响应数据
          </h3>
          <div class="json-content-wrapper">
            <pre class="json-content">{{ formatJSON(currentLogRecord.response_data) }}</pre>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="detail-section" v-if="currentLogRecord.remark">
          <h3 class="section-title">
            <CommentOutlined />
            备注信息
          </h3>
          <div class="remark-content">
            {{ currentLogRecord.remark }}
          </div>
        </div>
      </div>
    </a-drawer>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { onMounted, ref, reactive, computed } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { useMessage } from '@/hooks/web/useMessage';
  import { dualRouteLogger } from '@/utils/operationLog';
  import { useUserStore } from '@/store/modules/user';
  import { useInfo } from '@/hooks/web/useRestAPI';
  import { getShardingCode, getAllCityCodes, getCityName } from '@/utils/jiangsu-sharding';
  import {
    SearchOutlined,
    ReloadOutlined,
    DownloadOutlined,
    EyeOutlined,
    InfoCircleOutlined,
    FileTextOutlined,
    DiffOutlined,
    CommentOutlined,
    ExclamationCircleOutlined,
    CodeOutlined,
    ApiOutlined
  } from '@ant-design/icons-vue';

  defineOptions({ name: 'DualRouteOperationLog' });

  const { createMessage } = useMessage();
  const userStore = useUserStore();

  // API服务初始化
  const logQueryService = useInfo({
    rootPath: '/graph-rest-api',
  });

  // 响应式数据
  const loading = ref(false);
  const exportLoading = ref(false);
  const dataSource = ref<any[]>([]);
  const timeRange = ref([]);
  const detailDrawerVisible = ref(false);
  const currentLogRecord = ref<any>(null);

  // 地市选择（只影响shardingCode，不影响查询条件）
  const selectedCityCode = ref<string>('wx'); // 默认无锡

  // 筛选条件（这里的area_code是查询条件，不是地市选择）
  const filter = reactive({
    operator_name: '',
    operator_id: '',
    operation_type: '',
    object_type: '',
    object_code: '',
    object_name: '',
    area_code: '', // 这是查询条件中的area_code，用于过滤数据
    keyword: ''
  });

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
  });

  // 字典数据
  const dictionary = reactive({
    cityList: [] as Array<{ name: string }>
  });

  // 权限控制：判断用户是否可以切换地市
  const canSwitchCity = computed(() => {
    return userStore.getAreaCode === 'js'; // 只有省级用户(js)可以切换地市
  });

  // 获取当前地市名称
  const getCurrentCityName = (): string => {
    if (canSwitchCity.value) {
      return selectedCityCode.value ? getCityName(selectedCityCode.value) : '全省';
    } else {
      return getCityName(userStore.getAreaCode) || '未知地市';
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '操作时间',
      dataIndex: 'operation_time',
      key: 'operation_time',
      width: 150,
      sorter: true
    },
    {
      title: '操作人',
      dataIndex: 'operator_name',
      key: 'operator_name',
      width: 100
    },
    {
      title: '操作类型',
      dataIndex: 'operation_type',
      key: 'operation_type',
      width: 100
    },
    {
      title: '对象类型',
      dataIndex: 'object_type',
      key: 'object_type',
      width: 100
    },
    {
      title: '对象名称',
      dataIndex: 'object_name',
      key: 'object_name',
      width: 200,
      ellipsis: true
    },
    {
      title: '操作描述',
      dataIndex: 'operation_description',
      key: 'operation_description',
      width: 300,
      ellipsis: true
    },
    {
      title: '操作结果',
      dataIndex: 'operation_result',
      key: 'operation_result',
      width: 80
    },
    {
      title: '地市',
      dataIndex: 'area_name',
      key: 'area_name',
      width: 80
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right'
    }
  ];

  // 查询日志
  const query = async () => {
    loading.value = true;
    try {
      // 根据用户权限确定分库编码（只影响shardingCode，不影响查询条件）
      let shardingCode = '';
      if (canSwitchCity.value) {
        // 省级用户，如果选择了具体地市则查询该地市，否则查询所有地市
        if (selectedCityCode.value && selectedCityCode.value.trim() !== '') {
          // 选择了具体地市
          shardingCode = getShardingCode(selectedCityCode.value);
        } else {
          // 选择全省，需要查询所有地市并汇总
          await queryAllCities();
          return;
        }
      } else {
        // 地市用户，使用用户所属地市
        shardingCode = getShardingCode(userStore.getAreaCode);
      }

      // 构建查询参数，所有参数都放在请求体中
      // 注意：这里的filter.area_code是查询条件，不是地市选择
      const queryParams = {
        ...filter,
        start_time: timeRange.value?.[0],
        end_time: timeRange.value?.[1],
        pageSize: pagination.pageSize,
        currentPage: pagination.current,
        shardingCode: shardingCode
      };

      console.log('🔍 [操作日志] 开始查询日志数据');
      console.log('📋 [操作日志] 查询参数:', queryParams);
      console.log('👤 [操作日志] 用户信息:', {
        userAreaCode: userStore.getAreaCode,
        userAreaName: userStore.getAreaName,
        selectedCityCode: selectedCityCode.value,
        canSwitchCity: canSwitchCity.value
      });
      console.log('📄 [操作日志] 分页信息:', {
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        shardingCode: queryParams.shardingCode
      });

      // 设置请求参数
      logQueryService.info.value = queryParams;

      // 调用API，所有参数都在请求体中
      const result = await logQueryService.doCreateNew('/api/dual-route-log/query');

      console.log('📊 [操作日志] 查询响应:', result);
      console.log('📊 [操作日志] 响应结构:', {
        success: result?.success,
        dataLength: result?.data?.length,
        pageInfo: result?.pageInfo,
        message: result?.message,
        hasData: !!result?.data,
        hasPageInfo: !!result?.pageInfo
      });

      // 适配后端返回的数据结构：有data和pageInfo就认为成功
      if (result && (result.success === true || (result.data && result.pageInfo))) {
        dataSource.value = result.data || [];
        pagination.total = result.pageInfo?.totalCount || 0;
        pagination.current = result.pageInfo?.currentPage || pagination.current;

        console.log('✅ [操作日志] 查询成功，数据条数:', dataSource.value.length);
        console.log('📄 [操作日志] 更新分页信息:', {
          total: pagination.total,
          current: pagination.current,
          pageSize: pagination.pageSize,
          totalPage: result.pageInfo?.totalPage
        });
        console.log('📋 [操作日志] 数据样例:', dataSource.value.slice(0, 2));
      } else {
        console.warn('⚠️ [操作日志] 查询失败:', result?.message);
        console.warn('⚠️ [操作日志] 完整响应:', result);
        createMessage.error('查询失败：' + (result?.message || '未知错误'));
      }
    } catch (error) {
      console.error('❌ [操作日志] 查询异常:', error);
      console.error('❌ [操作日志] 错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      createMessage.error('查询失败，请重试: ' + error.message);
    } finally {
      loading.value = false;
    }
  };

  // 查询所有地市数据并汇总（省级用户选择全省时）
  const queryAllCities = async () => {
    try {
      console.log('🌐 [操作日志] 开始查询全省数据（所有地市）');

      const allCityCodes = getAllCityCodes();
      console.log('🏙️ [操作日志] 所有地市编码:', allCityCodes);

      const allResults: any[] = [];
      let totalCount = 0;

      // 并行查询所有地市
      const promises = allCityCodes.map(async (cityCode) => {
        try {
          const queryParams = {
            ...filter,
            start_time: timeRange.value?.[0],
            end_time: timeRange.value?.[1],
            pageSize: pagination.pageSize,
            currentPage: pagination.current,
            shardingCode: getShardingCode(cityCode)
          };

          console.log(`🔍 [操作日志] 查询地市 ${cityCode}:`, queryParams.shardingCode);

          // 创建独立的服务实例
          const cityQueryService = useInfo({ rootPath: '/graph-rest-api' });
          cityQueryService.info.value = queryParams;

          const result = await cityQueryService.doCreateNew('/api/dual-route-log/query');

          // 适配后端返回的数据结构：有data和pageInfo就认为成功
          if (result && (result.success === true || (result.data && result.pageInfo))) {
            console.log(`✅ [操作日志] 地市 ${cityCode} 查询成功，数据条数:`, result.data.length);
            return {
              cityCode,
              data: result.data,
              total: result.pageInfo?.totalCount || 0
            };
          } else {
            console.warn(`⚠️ [操作日志] 地市 ${cityCode} 查询失败:`, result?.message);
            return {
              cityCode,
              data: [],
              total: 0
            };
          }
        } catch (error) {
          console.error(`❌ [操作日志] 地市 ${cityCode} 查询异常:`, error);
          return {
            cityCode,
            data: [],
            total: 0
          };
        }
      });

      const results = await Promise.all(promises);

      // 汇总所有地市的数据
      results.forEach(result => {
        if (result.data && result.data.length > 0) {
          allResults.push(...result.data);
          totalCount += result.total;
        }
      });

      // 按时间倒序排序
      allResults.sort((a, b) => new Date(b.operation_time).getTime() - new Date(a.operation_time).getTime());

      // 分页处理（前端分页）
      const startIndex = (pagination.current - 1) * pagination.pageSize;
      const endIndex = startIndex + pagination.pageSize;
      const paginatedData = allResults.slice(startIndex, endIndex);

      dataSource.value = paginatedData;
      pagination.total = totalCount;

      console.log('🌐 [操作日志] 全省数据汇总完成:', {
        totalRecords: allResults.length,
        totalCount: totalCount,
        currentPageData: paginatedData.length,
        pagination: {
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total
        }
      });

    } catch (error) {
      console.error('❌ [操作日志] 查询全省数据失败:', error);
      createMessage.error('查询全省数据失败: ' + error.message);
    }
  };

  // 重置筛选条件
  const resetFilter = () => {
    Object.keys(filter).forEach(key => {
      filter[key] = '';
    });
    timeRange.value = [];
    pagination.current = 1;
    query();
  };

  // 表格变化处理
  const handleTableChange = (pag, filters, sorter) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    query();
  };

  // 显示日志详情
  const showLogDetail = async (record) => {
    try {
      console.log('🔍 [操作日志] 查看详情，记录ID:', record.id);
      console.log('🔍 [操作日志] 记录信息:', {
        id: record.id,
        area_code: record.area_code,
        area_name: record.area_name,
        operation_type: record.operation_type
      });

      // 不传递area_code，让工具类使用当前用户的分库编码
      const detail = await dualRouteLogger.queryOperationLogDetail(record.id);

      console.log('✅ [操作日志] 详情查询成功:', detail);
      currentLogRecord.value = detail;
      detailDrawerVisible.value = true;
    } catch (error) {
      console.error('❌ [操作日志] 获取日志详情失败:', error);
      createMessage.error('获取日志详情失败: ' + error.message);
    }
  };

  // 导出日志
  const exportLogs = async () => {
    exportLoading.value = true;
    try {
      // 这里可以实现导出功能
      createMessage.info('导出功能开发中...');
    } catch (error) {
      createMessage.error('导出失败');
    } finally {
      exportLoading.value = false;
    }
  };

  // 根据地市编码获取分库编码
  const getShardingCodeByAreaCode = (areaCode?: string): string => {
    const code = areaCode || userStore.getAreaCode;

    const areaCodeMap: Record<string, string> = {
      'wx': 'ds_bc_o3_wx',    // 无锡
      'nj': 'ds_bc_o3_nj',    // 南京
      'sz': 'ds_bc_o3_sz',    // 苏州
      'xz': 'ds_bc_o3_xz',    // 徐州
      'cz': 'ds_bc_o3_cz',    // 常州
      'nt': 'ds_bc_o3_nt',    // 南通
      'lyg': 'ds_bc_o3_lyg',  // 连云港
      'ha': 'ds_bc_o3_ha',    // 淮安
      'yc': 'ds_bc_o3_yc',    // 盐城
      'yz': 'ds_bc_o3_yz',    // 扬州
      'zj': 'ds_bc_o3_zj',    // 镇江
      'tz': 'ds_bc_o3_tz',    // 泰州
      'sq': 'ds_bc_o3_sq'     // 宿迁
    };

    return areaCodeMap[code] || 'ds_bc_o3_wx'; // 默认无锡
  };

  // 获取操作类型颜色
  const getOperationTypeColor = (type) => {
    const colorMap = {
      view: 'blue',
      create: 'green',
      update: 'orange',
      delete: 'red',
      risk_detect: 'purple',
      export: 'cyan',
      import: 'lime',
      ignore_risk: 'volcano',
      restore_risk: 'gold'
    };
    return colorMap[type] || 'default';
  };

  // 获取操作类型名称
  const getOperationTypeName = (type) => {
    const nameMap = {
      view: '查看详情',
      create: '新增',
      update: '修改',
      delete: '删除',
      risk_detect: '风险检测',
      export: '导出',
      import: '导入',
      ignore_risk: '忽略风险',
      restore_risk: '恢复风险',
      query: '查询',
      statistics: '统计分析'
    };
    return nameMap[type] || type;
  };

  // 获取对象类型名称
  const getObjectTypeName = (type) => {
    const nameMap = {
      opt_group: '光路组',
      opt_element: '光路元素',
      risk_check: '风险检测',
      batch_operation: '批量操作'
    };
    return nameMap[type] || type;
  };

  // 格式化JSON
  const formatJSON = (jsonStr) => {
    try {
      if (typeof jsonStr === 'string') {
        return JSON.stringify(JSON.parse(jsonStr), null, 2);
      }
      return JSON.stringify(jsonStr, null, 2);
    } catch (error) {
      return jsonStr;
    }
  };

  // 解析变更字段
  const parseChangedFields = (fieldsStr) => {
    try {
      if (typeof fieldsStr === 'string') {
        return JSON.parse(fieldsStr);
      }
      return fieldsStr || [];
    } catch (error) {
      return [];
    }
  };

  // 初始化字典数据
  const initDictionary = async () => {
    try {
      // 这里可以调用获取字典数据的接口
      dictionary.cityList = [
        { name: '南京' },
        { name: '无锡' },
        { name: '徐州' },
        { name: '常州' },
        { name: '苏州' },
        { name: '南通' },
        { name: '连云港' },
        { name: '淮安' },
        { name: '盐城' },
        { name: '扬州' },
        { name: '镇江' },
        { name: '泰州' },
        { name: '宿迁' }
      ];
    } catch (error) {
      console.error('初始化字典数据失败:', error);
    }
  };

  // 组件挂载时初始化
  onMounted(async () => {
    console.log('🚀 [操作日志] 页面初始化开始');
    console.log('🚀 [操作日志] 用户信息:', {
      areaCode: userStore.getAreaCode,
      areaName: userStore.getAreaName,
      userId: userStore.getUserInfo?.userId,
      userName: userStore.getUserInfo?.username
    });

    // 根据用户权限设置默认地市选择
    if (!canSwitchCity.value) {
      // 非省级用户，设置为用户所属地市，不允许切换
      selectedCityCode.value = userStore.getAreaCode;
      console.log('🚀 [操作日志] 地市用户，默认地市:', selectedCityCode.value);
    } else {
      // 省级用户，默认选择无锡
      selectedCityCode.value = 'wx';
      console.log('🚀 [操作日志] 省级用户，默认选择无锡');
    }

    console.log('🚀 [操作日志] 权限控制:', {
      canSwitchCity: canSwitchCity.value,
      selectedCityCode: selectedCityCode.value,
      currentCityName: getCurrentCityName(),
      filterAreaCode: filter.area_code
    });

    console.log('🚀 [操作日志] 默认筛选条件:', filter);
    console.log('🚀 [操作日志] 默认时间范围:', timeRange.value);
    console.log('🚀 [操作日志] 开始初始化字典数据');

    await initDictionary();

    console.log('🚀 [操作日志] 字典数据初始化完成，开始查询日志');
    await query();

    console.log('🚀 [操作日志] 页面初始化完成');
  });
</script>

<style scoped lang="less">
  /* 搜索容器样式 */
  .search-container {
    margin-bottom: 20px;
    background: url('@/assets/images/groupObstacle/search_bg.png') no-repeat;
    background-size: 100% 100%;
    border-radius: 12px;
    padding: 20px 24px;
    box-shadow: 0 4px 20px rgba(47, 108, 237, 0.08);
    border: 1px solid rgba(47, 108, 237, 0.1);
    position: relative;
    overflow: hidden;
    min-height: 140px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(240, 248, 255, 0.95) 50%,
        rgba(230, 244, 255, 0.9) 100%);
      z-index: 1;
    }

    > * {
      position: relative;
      z-index: 2;
    }
  }

  .filter-section {
    margin-bottom: 16px;
  }

  .filter-group {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .filter-label {
    font-weight: 600;
    color: #1e5bb8;
    margin-right: 12px;
    white-space: nowrap;
    font-size: 14px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  }

  .filter-select {
    border-radius: 8px;

    :deep(.ant-select-selector) {
      border-radius: 8px;
      border: 1px solid rgba(47, 108, 237, 0.2);
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(47, 108, 237, 0.05);

      &:hover {
        border-color: #2f6ced;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 4px 16px rgba(47, 108, 237, 0.1);
      }
    }

    :deep(.ant-select-focused .ant-select-selector) {
      border-color: #2f6ced;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 0 0 3px rgba(47, 108, 237, 0.1), 0 4px 16px rgba(47, 108, 237, 0.15);
    }
  }

  .filter-input {
    border-radius: 8px;
    border: 1px solid rgba(47, 108, 237, 0.2);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(47, 108, 237, 0.05);

    &:hover {
      border-color: #2f6ced;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 4px 16px rgba(47, 108, 237, 0.1);
    }

    &:focus {
      border-color: #2f6ced;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 0 0 3px rgba(47, 108, 237, 0.1), 0 4px 16px rgba(47, 108, 237, 0.15);
    }
  }

  .action-section {
    padding-top: 20px;
    border-top: 1px solid rgba(47, 108, 237, 0.1);
    display: flex;
    justify-content: flex-end;
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }

  .action-btn {
    height: 38px;
    border-radius: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);

    &.primary-btn {
      background: linear-gradient(135deg, #2f6ced 0%, #158ffe 100%);
      border: none;
      color: white;
      box-shadow: 0 4px 16px rgba(47, 108, 237, 0.2);

      &:hover {
        background: linear-gradient(135deg, #1e5bb8 0%, #0d7ce6 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(47, 108, 237, 0.3);
      }
    }

    &.secondary-btn {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(47, 108, 237, 0.2);
      color: #2f6ced;
      box-shadow: 0 2px 8px rgba(47, 108, 237, 0.05);

      &:hover {
        background: rgba(47, 108, 237, 0.05);
        border-color: #2f6ced;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 108, 237, 0.1);
      }
    }
  }

  /* 日志详情抽屉样式 */
  .log-detail-drawer {
    :deep(.ant-drawer-content) {
      background: #f8fafc;
    }

    .error-content {
      margin-top: 12px;
    }

    .json-content-wrapper {
      margin-top: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      .json-content {
        margin: 0;
        padding: 16px;
        background: transparent;
        border: none;
        font-size: 12px;
        line-height: 1.5;
        color: #495057;
        max-height: 300px;
        overflow-y: auto;
      }
    }
  }

  .log-detail-content {
    padding: 0;
  }

  .detail-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8e8e8;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e5bb8;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 8px;
  }

  .detail-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .detail-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;

    .label {
      font-weight: 500;
      color: #666;
      min-width: 80px;
      flex-shrink: 0;
    }

    .value {
      color: #333;
      word-break: break-all;
    }
  }

  .description-content,
  .remark-content {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    border-left: 4px solid #2f6ced;
    color: #333;
    line-height: 1.6;
  }

  .data-change-content {
    .data-section {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: #1e5bb8;
      }

      .json-content {
        background: #f8f9fa;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        padding: 12px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.4;
        color: #333;
        overflow-x: auto;
        white-space: pre-wrap;
        word-break: break-all;
      }

      .changed-fields {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
  }

  .city-display-text {
    color: #1890ff;
    font-weight: 500;
    padding: 4px 12px;
    background: #f0f8ff;
    border-radius: 6px;
    border: 1px solid #d6e4ff;
    font-size: 14px;
  }
</style>
