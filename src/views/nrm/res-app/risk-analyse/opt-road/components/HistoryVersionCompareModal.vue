<template>
  <a-modal
    :visible="visible"
    title="历史版本对比"
    width="100%"
    wrap-class-name="full-modal"
    :footer="null"
    :maskClosable="false"
    :destroyOnClose="true"
    class="history-compare-modal"
    @cancel="handleClose"
  >
    <div class="compare-container">
      <!-- 版本选择器 -->
      <div class="version-selector" style="margin-bottom: 16px;">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card size="small" title="当前版本">
              <div class="version-info">
                <div class="version-name">当前版本 {{ formatDate(new Date().toISOString()) }}</div>
                <!-- <div class="version-date"></div> -->
              </div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card size="small" title="历史版本">
              <div style="display: flex; align-items: center; gap: 8px;">
                <!-- 光盘图标 -->
                <div class="cd-icon-small">
                  <div class="cd-inner-small"></div>
                </div>
                <a-select
                  v-model:value="selectedHistoryVersion"
                  placeholder="选择历史版本"
                  style="flex: 1;"
                  @change="handleHistoryVersionChange"
                  :loading="historyVersionsLoading"
                >
                  <a-select-option
                    v-for="version in historyVersions"
                    :key="version.id"
                    :value="version.id"
                  >
                    {{ version.name }} ({{ formatDate(version.date) }}) {{ version.check_result }}
                  </a-select-option>
                </a-select>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 对比内容 -->
      <div class="compare-content">
        <a-row :gutter="16" style="height: calc(100vh - 200px);">
          <!-- 左侧：当前版本 -->
          <a-col :span="12">
            <div style="overflow-y: auto;height: 70vh;">
               <a-card title="当前版本" size="small" style="height: 100%;">
                <!-- 外层滚动容器 -->
                <div class="version-scroll-container">
                  <div class="version-content-wrapper">
                  <!-- 保护组详情 -->
                  <a-spin :spinning="currentVersionLoading" tip="正在加载当前版本数据...">
                    <a-descriptions bordered :column="2" size="small" style="margin-bottom: 16px;">
                      <a-descriptions-item label="保护组名称" :span="2">
                        <a-typography-text strong>
                          {{ getProtectionGroupName() }}
                        </a-typography-text>
                      </a-descriptions-item>
                      <a-descriptions-item label="保护组ID">
                        {{ props.groupInfo?.光路组id || props.protectionGroupId.split('_')[0] }}
                      </a-descriptions-item>
                      <a-descriptions-item label="地市">
                        {{ props.groupInfo?.地市名称 || props.protectionGroupId.split('_')[1] }}
                      </a-descriptions-item>
                      <a-descriptions-item label="检测状态">
                        <a-tag :color="getStatusColor(getCurrentCheckResult())">
                          {{ getCurrentCheckResult() }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="最新检测结果">
                        <a-tag :color="getStatusColor(props.groupInfo?.最新检测结果)">
                          {{ props.groupInfo?.最新检测结果 || '未检测' }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="异常信息" v-if="currentVersionData.exception_info" :span="2">
                        {{ currentVersionData.exception_info }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-spin>

                  <!-- 问题展示 -->
                  <a-collapse :bordered="false" expandIconPosition="right">
                    <!-- 同光缆问题 -->
                    <a-collapse-panel
                      key="1"
                      v-if="currentVersionData.sameCables && currentVersionData.sameCables.length > 0"
                    >
                      <template #header>
                        <a-row align="middle">
                          <a-col :span="8">
                            <a-space>
                              <exclamation-circle-outlined style="color: #ff4d4f;" />
                              <span style="font-weight: bold;">同光缆问题</span>
                            </a-space>
                          </a-col>
                          <a-col :span="16">
                            <a-tag color="error">
                              共 {{ currentVersionData.count_sameCables || 0 }} 个问题
                            </a-tag>
                          </a-col>
                        </a-row>
                      </template>
                      <div class="problem-details">
                        <a-row v-for="(item, index) in currentVersionData.sameCables_d" :key="index" style="margin-bottom: 12px;">
                          <a-col :span="24">
                            <a-card size="small" style="background: #fafafa;">
                              <a-row align="middle">
                                <a-col :span="8">
                                  <a-typography-text type="primary" strong>{{ item.result.a_opt_code }}</a-typography-text>
                                </a-col>
                                <a-col :span="2" style="text-align: center;">
                                  <arrow-right-outlined style="color: #666;" />
                                </a-col>
                                <a-col :span="8">
                                  <a-typography-text type="success" strong>{{ item.result.z_opt_code }}</a-typography-text>
                                </a-col>
                                <a-col :span="6">
                                  <a-space wrap>
                                    <a-tag v-for="cable in item.sameCables" :key="cable.id" color="orange">
                                      {{ cable.code }}
                                    </a-tag>
                                  </a-space>
                                </a-col>
                              </a-row>
                            </a-card>
                          </a-col>
                        </a-row>
                      </div>
                    </a-collapse-panel>

                    <!-- 同管道问题 -->
                    <a-collapse-panel key="2" v-if="currentVersionData.count_samePipeSegments >= 0">
                      <template #header>
                        <a-row align="middle">
                          <a-col :span="8">
                            <a-space>
                              <warning-outlined style="color: #faad14;" />
                              <span style="font-weight: bold;">同管道问题</span>
                            </a-space>
                          </a-col>
                          <a-col :span="16">
                            <a-tag color="warning">
                              共 {{ currentVersionData.count_samePipeSegments || 0 }} 个问题
                            </a-tag>
                          </a-col>
                        </a-row>
                      </template>
                      <div class="problem-details">
                        <a-row v-for="(item, index) in currentVersionData.samePipeSegments_d" :key="index" style="margin-bottom: 12px;">
                          <a-col :span="24">
                            <a-card size="small" style="background: #fafafa;">
                              <a-row align="middle" style="margin-bottom: 8px;">
                                <a-col :span="8">
                                  <a-typography-text type="primary" strong>{{ item.result.a_opt_code }}</a-typography-text>
                                </a-col>
                                <a-col :span="2" style="text-align: center;">
                                  <arrow-right-outlined style="color: #666;" />
                                </a-col>
                                <a-col :span="8">
                                  <a-typography-text type="success" strong>{{ item.result.z_opt_code }}</a-typography-text>
                                </a-col>
                                <a-col :span="6">
                                  <a-tag color="error">
                                    共计 {{ item.pipeSegments.filter((v) => !v.code.includes('忽略')).length }} 个同管道
                                  </a-tag>
                                </a-col>
                              </a-row>

                              <!-- 管道段详细列表 -->
                              <div v-if="item.pipeSegments && item.pipeSegments.length > 0">
                                <!-- 管道段数量较少时直接显示 -->
                                <div v-if="item.pipeSegments.length <= 10" class="pipe-segments-list">
                                  <a-space wrap>
                                    <a-tag
                                      v-for="pipeSegment in item.pipeSegments"
                                      :key="pipeSegment.id"
                                      :color="pipeSegment.code.includes('忽略') || pipeSegment.code.includes('局前井') ? 'default' : 'processing'"
                                      style="margin-bottom: 4px; cursor: pointer;"
                                      @click="handlePipeSegmentClick(pipeSegment)"
                                    >
                                      {{ pipeSegment.code }}
                                    </a-tag>
                                  </a-space>
                                </div>

                                <!-- 管道段数量较多时滚动显示 -->
                                <div v-else class="pipe-segments-scroll" style="max-height: 150px; overflow-y: auto;">
                                  <a-space wrap>
                                    <a-tag
                                      v-for="pipeSegment in item.pipeSegments"
                                      :key="pipeSegment.id"
                                      :color="pipeSegment.code.includes('忽略') || pipeSegment.code.includes('局前井') ? 'default' : 'processing'"
                                      style="margin-bottom: 4px; cursor: pointer;"
                                      @click="handlePipeSegmentClick(pipeSegment)"
                                    >
                                      {{ pipeSegment.code }}
                                    </a-tag>
                                  </a-space>
                                </div>
                              </div>
                            </a-card>
                          </a-col>
                        </a-row>
                      </div>
                    </a-collapse-panel>
                  </a-collapse>

                  <!-- 地图展示区域 -->
                  <div class="map-container" style="margin-top: 16px;">
                    <div class="map-title" style="margin-bottom: 8px; font-weight: bold; color: #1890ff; font-size: 14px;">地图可视化</div>
                    <a-spin :spinning="currentVersionLoading" tip="正在加载地图数据...">
                      <div style="width: 100%; height: 450px; border: 1px solid #d9d9d9; border-radius: 6px; position: relative;">
                        <CompareModalGisView
                          ref="currentVersionMapRef"
                          :key="`current-map-${props.visible}`"
                          v-if="props.visible"
                          style="width: 100%; height: 100%;"
                        />
                      </div>
                    </a-spin>
                  </div>
                  </div>
                </div>
              </a-card>
            </div>

          </a-col>

          <!-- 右侧：历史版本 -->
          <a-col :span="12">
            <div style="overflow-y: auto;height: 70vh;">
            <a-card title="历史版本" size="small" style="height: 100%;">
              <!-- 外层滚动容器 -->
              <div class="version-scroll-container">
                <div class="version-content-wrapper">
                  <div v-if="!selectedHistoryVersion" class="no-history-selected">
                    <a-empty description="请选择历史版本进行对比" />
                  </div>
                  <div v-else class="version-content">
                    <!-- 使用 risk-show-opt-road_his 组件 -->
                    <a-spin :spinning="historyVersionsLoading" tip="正在加载历史版本数据...">
                      <RiskShowOptRoadHis
                        :key="selectedHistoryVersion"
                        :protection-group-id="protectionGroupId"
                        :history-version-id="selectedHistoryVersion"
                        ref="historyComponentRef"
                      />
                    </a-spin>
                  </div>
                </div>
              </div>
            </a-card>
            </div>
          </a-col>
        </a-row>
      </div>

    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <a-space>
        <a-button @click="handleClose">关闭</a-button>
        <a-button type="primary" @click="handleExportCompare">
          <template #icon>
            <download-outlined />
          </template>
          导出对比报告
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import {
  DownloadOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  ArrowRightOutlined
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { useInfo } from '@/hooks/web/useRestAPI';
import RiskShowOptRoadHis from '../risk-show-opt-road_his.vue';
import CompareModalGisView from './CompareModalGisView.vue';
import { log } from 'vxe-table';

interface Props {
  visible: boolean;
  protectionGroupId: string;
  currentVersionData: any;
  historyDate?: string;
  groupInfo?: any; // 新增保护组详情信息
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const selectedHistoryVersion = ref('');
const historyVersionsLoading = ref(false);
const currentVersionLoading = ref(false);
const historyComponentRef = ref();
const currentVersionMapRef = ref();

// 版本数据
const historyVersions = ref<any[]>([]);
const historyVersionData = ref<any>({});

// 初始化API服务
const infoService = useInfo({
  rootPath: '/graph-rest-api',
});

// 当前版本数据
const currentVersionData = ref<any>({});

// 加载当前版本数据
const loadCurrentVersionData = async () => {
  try {
    console.log('开始加载当前版本数据...');
    currentVersionLoading.value = true;

    // 设置查询参数，参考 risk-show-opt-road.vue 的实现
    infoService.info.value.id = props.protectionGroupId.split('_')[0];
    infoService.info.value.ds = props.protectionGroupId.split('_')[1];
    infoService.info.value.cached = true; // 使用缓存数据

    console.log('查询参数:', {
      id: infoService.info.value.id,
      ds: infoService.info.value.ds,
      cached: infoService.info.value.cached
    });

    // 调用API获取当前版本数据，使用与 risk-show-opt-road 相同的API
    const result = await infoService.doCreateNew('/api/opt-group-api/risk-analyze-previous');

    console.log('当前版本数据加载结果:', result);

    if (result) {
      currentVersionData.value = result;

      // 延迟绘制地图，确保数据完全加载
      setTimeout(() => {
        drawCurrentVersionMap();
      }, 500);
    } else {
      console.warn('当前版本数据为空');
      currentVersionLoading.value = false;
    }
  } catch (error) {
    console.error('加载当前版本数据失败:', error);
    message.error('加载当前版本数据失败');
    currentVersionLoading.value = false;
  }
};

// 绘制当前版本地图
const drawCurrentVersionMap = () => {
  try {
    console.log('尝试绘制当前版本地图...');
    console.log('currentVersionMapRef.value:', currentVersionMapRef.value);
    console.log('currentVersionData.value:', currentVersionData.value);

    if (!currentVersionMapRef.value) {
      console.warn('地图组件引用不存在，延迟重试...');
      setTimeout(() => {
        drawCurrentVersionMap();
      }, 1000);
      return;
    }

    if (!currentVersionData.value || Object.keys(currentVersionData.value).length === 0) {
      console.warn('当前版本数据为空，无法绘制地图');
      currentVersionLoading.value = false;
      return;
    }

    // 验证数据结构
    const requiredFields = ['opt_code_list', 'pipeSegments'];
    const missingFields = requiredFields.filter(field => !currentVersionData.value[field]);

    if (missingFields.length > 0) {
      console.warn('数据结构不完整，缺少字段:', missingFields);
      console.log('完整数据结构:', Object.keys(currentVersionData.value));
    }

    console.log('开始绘制当前版本地图，数据结构:', {
      opt_code_list: currentVersionData.value.opt_code_list?.length || 0,
      pipeSegments: currentVersionData.value.pipeSegments?.length || 0,
      devices: currentVersionData.value.devices?.length || 0,
      title: currentVersionData.value.title
    });

    // 确保地图容器已经完全渲染
    nextTick(() => {
      setTimeout(() => {
        try {
          currentVersionMapRef.value.drawDatas(currentVersionData.value);
          console.log('当前版本地图绘制调用成功');

          // 延迟结束loading，确保地图完全绘制完成
          setTimeout(() => {
            currentVersionLoading.value = false;
          }, 2000);
        } catch (drawError) {
          console.error('调用drawDatas方法失败:', drawError);
          currentVersionLoading.value = false;
        }
      }, 200); // 额外的延迟确保DOM完全准备好
    });
  } catch (error) {
    console.error('绘制当前版本地图失败:', error);
    currentVersionLoading.value = false;
  }
};

// 获取历史版本列表
const loadHistoryVersions = async () => {
  try {
    historyVersionsLoading.value = true;

    // 设置查询参数
    infoService.info.value.object_id = props.protectionGroupId.split('_')[0];
    infoService.info.value.ds = props.protectionGroupId.split('_')[1];

    // 调用API获取历史时间列表
    const timeList = await infoService.doCreateNew('/api/opt-group-api/riskHisTimeList');


    console.log('历史版本列表:', timeList);

    if (timeList && Array.isArray(timeList)) {
      // 按时间排序，最新的在前面
      const sortedTimeList = timeList.sort((a, b) => {
        return new Date(b.his_date).getTime() - new Date(a.his_date).getTime();
      });

      // 转换为版本列表格式
      historyVersions.value = sortedTimeList.map((item, index) => ({
        id: item.his_date || `v${index + 1}`,
        name: `版本${index + 1}`,
        date: item.his_date,
        description: `检测时间: ${dayjs(item.his_date).format('YYYY-MM-DD HH:mm:ss')}`,
        creator: item.creator || '系统',
        check_result: item.check_result

      }));

      // 如果传入了历史日期，则选择对应的版本，否则选择最新的历史版本
      if (historyVersions.value.length > 0) {
        if (props.historyDate) {
          // 查找匹配的历史版本
          const matchedVersion = historyVersions.value.find(version =>
            version.date === props.historyDate ||
            dayjs(version.date).format('YYYY-MM-DD HH:mm:ss') === props.historyDate
          );

          if (matchedVersion) {
            selectedHistoryVersion.value = matchedVersion.id;
            console.log('选择指定历史版本:', matchedVersion);
          } else {
            selectedHistoryVersion.value = historyVersions.value[0].id;
            console.log('未找到匹配的历史版本，选择最新版本:', historyVersions.value[0]);
          }
        } else {
          selectedHistoryVersion.value = historyVersions.value[0].id;
          console.log('默认选择最新历史版本:', historyVersions.value[0]);
        }
      }
    }
  } catch (error) {
    console.error('加载历史版本列表失败:', error);
    message.error('加载历史版本列表失败');
  } finally {
    historyVersionsLoading.value = false;
  }
};

// 方法
const formatDate = (dateString: string) => {
  if (!dateString) return '未知';
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
};

const getStatusColor = (status: string) => {
  switch (status) {
    case '正常':
      return 'green';
    case '同光缆':
    case '同管道':
      return 'red';
    case '局内光路':
      return 'orange';
    default:
      return 'default';
  }
};

const handleHistoryVersionChange = async (versionId: string) => {
  console.log('选择历史版本:', versionId);
  // 历史版本数据将由 RiskShowOptRoadHis 组件自己处理
  // 这里只需要触发组件重新渲染
};

const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const handleExportCompare = () => {
  message.info('导出对比报告功能开发中...');
};

// 处理管道段点击事件
const handlePipeSegmentClick = (pipeSegment: any) => {
  console.log('点击管道段:', pipeSegment);
  // 在地图上高亮显示该管道段
  if (currentVersionMapRef.value && currentVersionMapRef.value.hightlights) {
    try {
      currentVersionMapRef.value.hightlights([pipeSegment]);
    } catch (error) {
      console.error('高亮管道段失败:', error);
    }
  }
};

// 获取告警类型
const getAlertType = (checkResult: string) => {
  switch (checkResult) {
    case '正常':
      return 'success';
    case '同光缆':
    case '同管道':
      return 'error';
    case '局内光路':
      return 'warning';
    default:
      return 'info';
  }
};

// 获取保护组名称
const getProtectionGroupName = () => {
  // 优先使用传入的保护组信息
  if (props.groupInfo?.保护组名称) {
    return props.groupInfo.保护组名称;
  }
  // 其次使用当前版本数据中的标题
  if (currentVersionData.value?.title) {
    return currentVersionData.value.title;
  }
  // 最后使用传入的当前版本数据
  if (props.currentVersionData?.title) {
    return props.currentVersionData.title;
  }
  return '未知保护组';
};

// 获取当前检测结果
const getCurrentCheckResult = () => {
  // 优先使用传入的保护组信息
  if (props.groupInfo?.最新检测结果) {
    return props.groupInfo.最新检测结果;
  }
  // 其次使用当前版本数据
  if (currentVersionData.value?.check_result) {
    return currentVersionData.value.check_result;
  }
  // 最后使用传入的当前版本数据
  if (props.currentVersionData?.check_result) {
    return props.currentVersionData.check_result;
  }
  return '未检测';
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    console.log('历史版本对比弹窗打开');
    // 弹窗打开时加载历史版本列表和当前版本数据
    nextTick(() => {
      loadHistoryVersions();
      // 加载当前版本数据
      setTimeout(() => {
        console.log('开始加载当前版本数据...');
        loadCurrentVersionData();
      }, 1000); // 增加延迟时间
    });
  } else {
    // 弹窗关闭时重置状态
    selectedHistoryVersion.value = '';
    historyVersionData.value = {};
    currentVersionData.value = {};
    console.log('历史版本对比弹窗关闭');
  }
});

// 监听当前版本数据变化，重新绘制地图
watch(() => currentVersionData.value, (newData) => {
  console.log('内部当前版本数据变化:', newData);
  if (newData && Object.keys(newData).length > 0 && props.visible) {
    // 延迟一点时间确保组件已经渲染
    setTimeout(() => {
      drawCurrentVersionMap();
    }, 500);
  }
});

// 监听地图组件引用变化
watch(() => currentVersionMapRef.value, (newRef) => {
  console.log('地图组件引用变化:', newRef);
  if (newRef && currentVersionData.value && Object.keys(currentVersionData.value).length > 0 && props.visible) {
    // 组件引用可用时立即绘制
    setTimeout(() => {
      drawCurrentVersionMap();
    }, 200);
  }
});

// 监听历史日期变化
watch(() => props.historyDate, (newHistoryDate) => {
  console.log('历史日期变化:', newHistoryDate);
  if (newHistoryDate && historyVersions.value.length > 0) {
    // 查找匹配的历史版本
    const matchedVersion = historyVersions.value.find(version =>
      version.date === newHistoryDate ||
      dayjs(version.date).format('YYYY-MM-DD HH:mm:ss') === newHistoryDate
    );

    if (matchedVersion && selectedHistoryVersion.value !== matchedVersion.id) {
      selectedHistoryVersion.value = matchedVersion.id;
      console.log('根据历史日期选择版本:', matchedVersion);
    }
  }
});
</script>

<style lang="less">
/* 全屏弹窗样式 */
.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }
  .ant-modal-body {
    flex: 1;
    padding: 16px;
  }
}

.history-compare-modal :deep(.ant-modal-body) {
  padding: 16px;
}

.compare-container {
  min-height: 600px;
}

.version-info {
  text-align: center;
}

.version-name {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 4px;
}

.version-date {
  font-size: 12px;
  color: #666;
}

.problem-summary {
  margin-bottom: 16px;
  text-align: center;
}

.data-compare-container {
  min-height: 400px;
}

.current-version-content {
  height: 100%;
  overflow-y: auto;
}

.map-container {
  border-radius: 6px;
  overflow: hidden;
  width: 100%;
}

.map-title {
  color: #1890ff;
  font-size: 14px;
}

/* 确保地图组件正确填充容器 */
.compare-modal-gis-wrapper {
  width: 100% !important;
  height: 100% !important;
}

.compare-modal-gis-wrapper > * {
  width: 100%;
}

/* 历史版本地图容器 */
.history-version-content .map-container {
  width: 100%;
  height: 400px;
}

.no-history-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.history-version-content {
  height: 100%;
  overflow-y: auto;
}

/* 统一的版本滚动容器样式 */
.version-scroll-container {
  height: calc(100% - 40px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 4px;
}

/* 滚动条样式优化 */
.version-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.version-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.version-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.version-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 版本内容包装器 */
.version-content-wrapper {
  width: 100%;
  min-height: 100%;
  padding-bottom: 16px; /* 底部留白，避免内容贴边 */
}

/* 版本内容容器 */
.version-content {
  width: 100%;
}

/* 确保问题展示区域有足够空间 */
.problem-details {
  margin-bottom: 16px;
}

.problem-details .ant-card {
  margin-bottom: 8px;
}

/* 优化弹窗布局 */
.compare-container {
  min-height: 600px;
}

.compare-content {
  min-height: 700px;
}

/* 光盘图标样式 */
.cd-icon-small {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(45deg, #1890ff, #40a9ff);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}

.cd-icon-small::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
  position: absolute;
}

.cd-inner-small {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
}
</style>
