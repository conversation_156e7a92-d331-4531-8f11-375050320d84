<template>
  <div class="dual-route-log-analytics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <div class="title-icon">
            <bar-chart-outlined />
          </div>
          <div class="title-text">
            <h1 class="page-title">双路由操作日志分析</h1>
            <p class="page-subtitle">操作行为分析、热度统计、用户活跃度分析</p>
          </div>
        </div>

        <div class="control-section">
          <div class="time-control">
            <label class="control-label">
              <calendar-outlined />
              时间范围
            </label>
            <a-range-picker
              v-model:value="dateRange"
              format="YYYY-MM-DD"
              placeholder="选择时间范围"
              @change="onDateRangeChange"
              class="range-picker"
            />
          </div>

          <div class="city-control" v-if="canSwitchCity">
            <label class="control-label">
              <environment-outlined />
              地市选择
            </label>
            <a-select
              v-model:value="selectedCityCode"
              placeholder="选择地市"
              style="width: 150px"
              @change="onCityChange"
            >
              <a-select-option value="">全省</a-select-option>
              <a-select-option value="wx">无锡市</a-select-option>
              <a-select-option value="nj">南京市</a-select-option>
              <a-select-option value="sz">苏州市</a-select-option>
              <a-select-option value="xz">徐州市</a-select-option>
              <a-select-option value="cz">常州市</a-select-option>
              <a-select-option value="nt">南通市</a-select-option>
              <a-select-option value="lyg">连云港市</a-select-option>
              <a-select-option value="ha">淮安市</a-select-option>
              <a-select-option value="yc">盐城市</a-select-option>
              <a-select-option value="yz">扬州市</a-select-option>
              <a-select-option value="zj">镇江市</a-select-option>
              <a-select-option value="tz">泰州市</a-select-option>
              <a-select-option value="sq">宿迁市</a-select-option>
            </a-select>
          </div>

          <div class="city-display" v-else>
            <label class="control-label">
              <environment-outlined />
              当前地市
            </label>
            <span class="city-name">{{ getCurrentCityName() }}</span>
          </div>

          <div class="action-buttons">
            <a-button type="primary" @click="refreshAnalytics" :loading="loading.summary">
              <template #icon><reload-outlined /></template>
              刷新数据
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 加载状态 -->
      <div class="loading-section" v-if="loading.summary">
        <a-spin size="large" tip="正在加载日志分析数据...">
          <div class="loading-placeholder"></div>
        </a-spin>
      </div>

      <!-- 分析结果展示 -->
      <div class="analytics-content" v-else>
        <!-- 概览统计卡片 -->
        <div class="overview-cards">
          <div class="stat-card">
            <div class="stat-icon">
              <bar-chart-outlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ analyticsData.totalOperations || 0 }}</div>
              <div class="stat-label">总操作次数</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <user-outlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ analyticsData.activeUsers || 0 }}</div>
              <div class="stat-label">活跃用户数</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <fire-outlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ analyticsData.peakHour || '--' }}</div>
              <div class="stat-label">操作高峰时段</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <thunderbolt-outlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ analyticsData.mostActiveUser || '--' }}</div>
              <div class="stat-label">最活跃用户</div>
            </div>
          </div>
        </div>

        <!-- 图表分析区域 -->
        <div class="charts-section">
          <!-- 时间分布分析 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3 class="chart-title">
                <clock-circle-outlined />
                操作时间分布分析
              </h3>
              <div class="chart-controls">
                <a-radio-group v-model:value="timeAnalysisType" @change="refreshTimeAnalysis">
                  <a-radio-button value="hour">按小时</a-radio-button>
                  <a-radio-button value="day">按天</a-radio-button>
                  <a-radio-button value="week">按周</a-radio-button>
                </a-radio-group>
              </div>
            </div>
            <div class="chart-content">
              <div ref="timeDistributionChart" class="chart-container"></div>
            </div>
          </div>

          <!-- 操作类型分析 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3 class="chart-title">
                <pie-chart-outlined />
                操作类型分布
              </h3>
            </div>
            <div class="chart-content">
              <div ref="operationTypeChart" class="chart-container"></div>
            </div>
          </div>
        </div>

        <!-- 用户活跃度分析 -->
        <div class="user-analysis-section">
          <div class="chart-card full-width">
            <div class="chart-header">
              <h3 class="chart-title">
                <team-outlined />
                用户活跃度分析
              </h3>
              <div class="chart-controls">
                <a-select v-model:value="userAnalysisType" @change="refreshUserAnalysis" style="width: 120px">
                  <a-select-option value="activity">活跃度</a-select-option>
                  <a-select-option value="frequency">操作频次</a-select-option>
                  <a-select-option value="types">操作类型</a-select-option>
                </a-select>
              </div>
            </div>
            <div class="chart-content">
              <div ref="userActivityChart" class="chart-container"></div>
            </div>
          </div>
        </div>

        <!-- 热度分析 -->
        <div class="heatmap-section">
          <div class="chart-card full-width">
            <div class="chart-header">
              <h3 class="chart-title">
                <fire-outlined />
                操作热度分析
              </h3>
              <div class="chart-subtitle">按时间段和操作类型的热度分布</div>
            </div>
            <div class="chart-content">
              <div ref="heatmapChart" class="chart-container"></div>
            </div>
          </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="data-table-section">
          <div class="table-card">
            <div class="table-header">
              <h3 class="table-title">
                <table-outlined />
                操作日志详细数据
              </h3>
              <div class="table-controls">
                <a-input-search
                  v-model:value="searchKeyword"
                  placeholder="搜索操作描述、用户名等"
                  style="width: 250px"
                  @search="onSearch"
                />
                <a-button @click="exportData" :loading="loading.export">
                  <template #icon><download-outlined /></template>
                  导出数据
                </a-button>
              </div>
            </div>
            <div class="table-content">
              <a-table
                :columns="tableColumns"
                :data-source="tableData"
                :pagination="tablePagination"
                :loading="loading.table"
                @change="onTableChange"
                size="small"
                :scroll="{ x: 1200 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'operation_time'">
                    {{ formatDateTime(record.operation_time) }}
                  </template>
                  <template v-else-if="column.key === 'operation_type'">
                    <a-tag :color="getOperationTypeColor(record.operation_type)">
                      {{ getOperationTypeName(record.operation_type) }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'object_type'">
                    <a-tag color="blue">{{ getObjectTypeName(record.object_type) }}</a-tag>
                  </template>
                  <template v-else-if="column.key === 'operation_result'">
                    <a-tag :color="record.operation_result === 'success' ? 'green' : 'red'">
                      {{ record.operation_result === 'success' ? '成功' : '失败' }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-button type="link" size="small" @click="viewLogDetail(record)">
                      查看详情
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志详情弹窗 -->
    <a-modal
      v-model:open="logDetailModalVisible"
      title="操作日志详情"
      width="800px"
      :footer="null"
      class="log-detail-modal"
    >
      <div class="log-detail-content" v-if="currentLogDetail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="操作时间">
            {{ formatDateTime(currentLogDetail.operation_time) }}
          </a-descriptions-item>
          <a-descriptions-item label="操作人员">
            {{ currentLogDetail.operator_name }} ({{ currentLogDetail.operator_id }})
          </a-descriptions-item>
          <a-descriptions-item label="操作类型">
            <a-tag :color="getOperationTypeColor(currentLogDetail.operation_type)">
              {{ getOperationTypeName(currentLogDetail.operation_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="对象类型">
            <a-tag color="blue">{{ getObjectTypeName(currentLogDetail.object_type) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="对象信息" :span="2">
            {{ currentLogDetail.object_name }} ({{ currentLogDetail.object_code }})
          </a-descriptions-item>
          <a-descriptions-item label="操作描述" :span="2">
            {{ currentLogDetail.operation_description }}
          </a-descriptions-item>
          <a-descriptions-item label="操作结果">
            <a-tag :color="currentLogDetail.operation_result === 'success' ? 'green' : 'red'">
              {{ currentLogDetail.operation_result === 'success' ? '成功' : '失败' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="地市">
            {{ currentLogDetail.area_name }}
          </a-descriptions-item>
          <a-descriptions-item label="操作前数据" :span="2" v-if="currentLogDetail.before_data">
            <pre class="json-data">{{ formatJsonData(currentLogDetail.before_data) }}</pre>
          </a-descriptions-item>
          <a-descriptions-item label="操作后数据" :span="2" v-if="currentLogDetail.after_data">
            <pre class="json-data">{{ formatJsonData(currentLogDetail.after_data) }}</pre>
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2" v-if="currentLogDetail.remark">
            {{ currentLogDetail.remark }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, defineOptions } from 'vue';

defineOptions({
  name: 'DualRouteLogAnalytics'
});

import { message } from 'ant-design-vue';
import * as echarts from 'echarts';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import 'dayjs/locale/zh-cn';

// 配置dayjs
dayjs.extend(relativeTime);
dayjs.extend(weekOfYear);
dayjs.locale('zh-cn');

import {
  BarChartOutlined,
  ReloadOutlined,
  PieChartOutlined,
  TableOutlined,
  EnvironmentOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  FireOutlined,
  UserOutlined,
  ThunderboltOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue';

import { useUserStore } from '@/store/modules/user';
import { getCityName, getShardingCode } from '@/utils/jiangsu-sharding';
import { useInfo } from '@/hooks/web/useRestAPI';

// 用户store
const userStore = useUserStore();

// 响应式数据
const loading = reactive({
  summary: false,
  table: false,
  chart: false,
  export: false,
  heatmap: false,
});

// 权限控制：判断用户是否可以切换地市
const canSwitchCity = computed(() => {
  return userStore.getAreaCode === 'js'; // 只有省级用户(js)可以切换地市
});

// 地市选择
const selectedCityCode = ref<string>('wx'); // 默认无锡

// 时间范围
const dateRange = ref<[dayjs.Dayjs, dayjs.Dayjs]>([
  dayjs().subtract(1, 'month'),
  dayjs()
]);

// 分析类型选择
const timeAnalysisType = ref<string>('hour'); // hour, day, week
const userAnalysisType = ref<string>('activity'); // activity, frequency, types

// 搜索关键词
const searchKeyword = ref<string>('');

// 分析数据
const analyticsData = reactive({
  totalOperations: 0,
  activeUsers: 0,
  peakHour: '',
  mostActiveUser: '',
  timeDistribution: [] as any[],
  operationTypes: [] as any[],
  userActivity: [] as any[],
  heatmapData: [] as any[]
});

// 表格数据
const tableData = ref<any[]>([]);

// 日志详情弹窗
const logDetailModalVisible = ref<boolean>(false);
const currentLogDetail = ref<any>(null);



// 图表引用
const timeDistributionChart = ref<HTMLElement>();
const operationTypeChart = ref<HTMLElement>();
const userActivityChart = ref<HTMLElement>();
const heatmapChart = ref<HTMLElement>();

// API服务初始化
const logQueryService = useInfo({
  rootPath: '/graph-rest-api',
});

const logStatisticsService = useInfo({
  rootPath: '/graph-rest-api',
});

// 表格配置
const tablePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
});

// 表格列配置
const tableColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }: { index: number }) => index + 1
  },
  {
    title: '操作时间',
    dataIndex: 'operation_time',
    key: 'operation_time',
    width: 160,
    sorter: true
  },
  {
    title: '操作人员',
    dataIndex: 'operator_name',
    key: 'operator_name',
    width: 120
  },
  {
    title: '操作类型',
    dataIndex: 'operation_type',
    key: 'operation_type',
    width: 120
  },
  {
    title: '对象类型',
    dataIndex: 'object_type',
    key: 'object_type',
    width: 120
  },
  {
    title: '对象名称',
    dataIndex: 'object_name',
    key: 'object_name',
    width: 200,
    ellipsis: true
  },
  {
    title: '操作描述',
    dataIndex: 'operation_description',
    key: 'operation_description',
    width: 250,
    ellipsis: true
  },
  {
    title: '操作结果',
    dataIndex: 'operation_result',
    key: 'operation_result',
    width: 100
  },
  {
    title: '地市',
    dataIndex: 'area_name',
    key: 'area_name',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right'
  }
];

// 工具方法
const formatDateTime = (dateTime: string | number): string => {
  if (!dateTime) return '--';
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

const formatJsonData = (jsonStr: string): string => {
  if (!jsonStr) return '';
  try {
    const obj = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;
    return JSON.stringify(obj, null, 2);
  } catch (e) {
    return jsonStr;
  }
};

const getOperationTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    'view': '查看',
    'update': '修改',
    'delete': '删除',
    'create': '创建',
    'risk_detection': '风险检测',
    'export': '导出',
    'import': '导入'
  };
  return typeMap[type] || type;
};

const getOperationTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    'view': 'blue',
    'update': 'orange',
    'delete': 'red',
    'create': 'green',
    'risk_detection': 'purple',
    'export': 'cyan',
    'import': 'magenta'
  };
  return colorMap[type] || 'default';
};

const getObjectTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    'opt_group': '光路组',
    'protection_group': '保护组',
    'optical_path': '光路',
    'system': '系统'
  };
  return typeMap[type] || type;
};

const getCurrentCityName = (): string => {
  if (canSwitchCity.value) {
    return getCityName(selectedCityCode.value) || '未知地市';
  } else {
    return getCityName(userStore.getAreaCode) || '未知地市';
  }
};

// 主要方法
const refreshAnalytics = async () => {
  console.log('🚀 [日志分析] 开始刷新分析数据');
  console.log('⏰ [日志分析] 当前时间范围:', {
    start: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
    end: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss')
  });
  console.log('🏙️ [日志分析] 当前选择地市:', selectedCityCode.value || '全省');

  loading.summary = true;
  try {
    console.log('📊 [日志分析] 开始并行加载统计数据和详细数据');
    await Promise.all([
      loadLogStatistics(),
      loadLogData()
    ]);

    console.log('🎨 [日志分析] 数据加载完成，开始初始化图表');
    await nextTick();
    initCharts();

    console.log('✅ [日志分析] 分析数据刷新完成');
    message.success('数据刷新成功');
  } catch (error) {
    console.error('❌ [日志分析] 刷新分析数据失败:', error);
    console.error('❌ [日志分析] 错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    message.error('刷新数据失败: ' + error.message);
  } finally {
    loading.summary = false;
    console.log('🏁 [日志分析] 刷新操作结束');
  }
};

// 加载日志统计数据
const loadLogStatistics = async () => {
  try {
    // 根据用户权限确定查询的地市编码
    let queryAreaCode = selectedCityCode.value;
    if (!queryAreaCode) {
      // 如果没有选择地市，使用用户默认地市
      queryAreaCode = userStore.getAreaCode;
    }

    const queryParams = {
      start_time: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
      end_time: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'),
      area_code: queryAreaCode,
      shardingCode: getShardingCode(queryAreaCode)
    };

    console.log('🔍 [日志分析] 开始加载统计数据');
    console.log('📋 [日志分析] 查询参数:', queryParams);
    console.log('👤 [日志分析] 用户信息:', {
      userAreaCode: userStore.getAreaCode,
      userAreaName: userStore.getAreaName,
      selectedCity: selectedCityCode.value
    });

    // 设置请求参数
    logStatisticsService.info.value = queryParams;

    // 调用API，所有参数都在请求体中
    const result = await logStatisticsService.doCreateNew('/api/dual-route-log/statistics');

    console.log('📊 [日志分析] 统计数据响应:', result);
    console.log('📊 [日志分析] 响应数据类型:', typeof result);
    console.log('📊 [日志分析] 是否为数组:', Array.isArray(result));

    if (Array.isArray(result)) {
      console.log('✅ [日志分析] 开始处理统计数据，数据条数:', result.length);
      processStatisticsData(result);
    } else if (result && Array.isArray(result.data)) {
      console.log('✅ [日志分析] 从响应中提取数据数组，数据条数:', result.data.length);
      processStatisticsData(result.data);
    } else {
      console.warn('⚠️ [日志分析] 统计数据格式不正确，期望数组，实际:', result);
    }
  } catch (error) {
    console.error('❌ [日志分析] 加载统计数据失败:', error);
    console.error('❌ [日志分析] 错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    message.error('加载统计数据失败: ' + error.message);
  }
};

// 加载日志详细数据
const loadLogData = async () => {
  loading.table = true;
  try {
    // 根据用户权限确定查询的地市编码
    let queryAreaCode = selectedCityCode.value;
    if (!queryAreaCode) {
      // 如果没有选择地市，使用用户默认地市
      queryAreaCode = userStore.getAreaCode;
    }

    const queryParams = {
      start_time: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
      end_time: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'),
      // 不限制area_code，因为地市库中只存自己的数据
      keyword: searchKeyword.value,
      pageSize: tablePagination.pageSize,
      currentPage: tablePagination.current,
      shardingCode: getShardingCode(queryAreaCode)
    };

    console.log('🔍 [日志分析] 开始加载日志详细数据');
    console.log('📋 [日志分析] 查询参数:', queryParams);
    console.log('📄 [日志分析] 分页信息:', {
      current: tablePagination.current,
      pageSize: tablePagination.pageSize,
      total: tablePagination.total,
      shardingCode: queryParams.shardingCode
    });

    // 设置请求参数
    logQueryService.info.value = queryParams;

    // 调用API，所有参数都在请求体中
    const result = await logQueryService.doCreateNew('/api/dual-route-log/query');

    console.log('📊 [日志分析] 日志数据响应:', result);
    console.log('📊 [日志分析] 响应结构:', {
      success: result?.success,
      dataLength: result?.data?.length,
      pageInfo: result?.pageInfo,
      message: result?.message
    });

    if (result && result.success) {
      tableData.value = result.data || [];
      tablePagination.total = result.pageInfo?.totalCount || 0;
      console.log('✅ [日志分析] 日志数据加载成功，数据条数:', tableData.value.length);
      console.log('📄 [日志分析] 更新分页信息:', {
        total: tablePagination.total,
        current: tablePagination.current,
        pageSize: tablePagination.pageSize
      });
    } else {
      console.warn('⚠️ [日志分析] 日志数据加载失败:', result?.message);
      message.error('加载日志数据失败: ' + (result?.message || '未知错误'));
    }
  } catch (error) {
    console.error('❌ [日志分析] 加载日志数据异常:', error);
    console.error('❌ [日志分析] 错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    message.error('加载日志数据失败: ' + error.message);
  } finally {
    loading.table = false;
  }
};

// 处理统计数据
const processStatisticsData = (data: any[]) => {
  if (!Array.isArray(data)) return;

  // 计算总操作次数
  analyticsData.totalOperations = data.reduce((sum, item) => sum + (item.count || 0), 0);

  // 计算活跃用户数
  const uniqueUsers = new Set(data.map(item => item.operator_id)).size;
  analyticsData.activeUsers = uniqueUsers;

  // 计算操作高峰时段
  const hourStats = data.reduce((acc, item) => {
    const hour = item.operation_hour || 0;
    acc[hour] = (acc[hour] || 0) + (item.count || 0);
    return acc;
  }, {} as Record<number, number>);

  const peakHour = Object.entries(hourStats).reduce((max, [hour, count]) =>
    (count as number) > max.count ? { hour: parseInt(hour), count: count as number } : max,
    { hour: 0, count: 0 }
  );
  analyticsData.peakHour = `${peakHour.hour}:00-${peakHour.hour + 1}:00`;

  // 计算最活跃用户
  const userStats = data.reduce((acc, item) => {
    const userId = item.operator_name || item.operator_id;
    acc[userId] = (acc[userId] || 0) + (item.count || 0);
    return acc;
  }, {} as Record<string, number>);

  const mostActiveUser = Object.entries(userStats).reduce((max, [user, count]) =>
    (count as number) > max.count ? { user, count: count as number } : max,
    { user: '', count: 0 }
  );
  analyticsData.mostActiveUser = mostActiveUser.user;

  // 处理时间分布数据
  analyticsData.timeDistribution = processTimeDistribution(data);

  // 处理操作类型数据
  analyticsData.operationTypes = processOperationTypes(data);

  // 处理用户活跃度数据
  analyticsData.userActivity = processUserActivity(data);

  // 处理热度图数据
  analyticsData.heatmapData = processHeatmapData(data);
};

// 处理时间分布数据
const processTimeDistribution = (data: any[]) => {
  const timeStats = data.reduce((acc, item) => {
    let key = '';
    if (timeAnalysisType.value === 'hour') {
      key = `${item.operation_hour || 0}时`;
    } else if (timeAnalysisType.value === 'day') {
      key = item.operation_date || '';
    } else {
      // 按周处理
      const date = dayjs(item.operation_date);
      key = `第${date.week()}周`;
    }

    acc[key] = (acc[key] || 0) + (item.count || 0);
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(timeStats).map(([time, count]) => ({ time, count }));
};

// 处理操作类型数据
const processOperationTypes = (data: any[]) => {
  const typeStats = data.reduce((acc, item) => {
    const type = item.operation_type || 'unknown';
    acc[type] = (acc[type] || 0) + (item.count || 0);
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(typeStats).map(([type, count]) => ({
    type: getOperationTypeName(type),
    count,
    originalType: type
  }));
};

// 处理用户活跃度数据
const processUserActivity = (data: any[]) => {
  const userStats = data.reduce((acc, item) => {
    const user = item.operator_name || item.operator_id || 'unknown';
    if (!acc[user]) {
      acc[user] = { name: user, count: 0, types: new Set() };
    }
    acc[user].count += (item.count || 0);
    acc[user].types.add(item.operation_type);
    return acc;
  }, {} as Record<string, any>);

  return Object.values(userStats).map((user: any) => ({
    name: user.name,
    count: user.count,
    typeCount: user.types.size
  })).sort((a, b) => b.count - a.count);
};

// 处理热度图数据
const processHeatmapData = (data: any[]): any[] => {
  const heatmapStats = data.reduce((acc, item) => {
    const hour = item.operation_hour || 0;
    const type = item.operation_type || 'unknown';
    const key = `${hour}-${type}`;
    acc[key] = (acc[key] || 0) + (item.count || 0);
    return acc;
  }, {} as Record<string, number>);

  const result: any[] = [];
  for (let hour = 0; hour < 24; hour++) {
    const operationTypes = ['view', 'update', 'delete', 'create', 'risk_detection'];
    operationTypes.forEach(type => {
      const key = `${hour}-${type}`;
      result.push([hour, type, heatmapStats[key] || 0]);
    });
  }
  return result;
};

// 初始化图表
const initCharts = async () => {
  await nextTick();
  initTimeDistributionChart();
  initOperationTypeChart();
  initUserActivityChart();
  initHeatmapChart();
};

// 初始化时间分布图表
const initTimeDistributionChart = () => {
  if (!timeDistributionChart.value) return;

  const chart = echarts.init(timeDistributionChart.value);
  const option = {
    title: {
      text: '操作时间分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: analyticsData.timeDistribution.map(item => item.time)
    },
    yAxis: {
      type: 'value',
      name: '操作次数'
    },
    series: [{
      data: analyticsData.timeDistribution.map(item => item.count),
      type: 'bar',
      itemStyle: {
        color: '#1890ff'
      }
    }]
  };
  chart.setOption(option);
};

// 初始化操作类型图表
const initOperationTypeChart = () => {
  if (!operationTypeChart.value) return;

  const chart = echarts.init(operationTypeChart.value);
  const option = {
    title: {
      text: '操作类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: '60%',
      data: analyticsData.operationTypes.map(item => ({
        name: item.type,
        value: item.count
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  chart.setOption(option);
};

// 初始化用户活跃度图表
const initUserActivityChart = () => {
  if (!userActivityChart.value) return;

  const chart = echarts.init(userActivityChart.value);
  const topUsers = analyticsData.userActivity.slice(0, 10);

  const option = {
    title: {
      text: '用户活跃度排行',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: topUsers.map(item => item.name),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '操作次数'
    },
    series: [{
      data: topUsers.map(item => item.count),
      type: 'bar',
      itemStyle: {
        color: '#52c41a'
      }
    }]
  };
  chart.setOption(option);
};

// 初始化热度图
const initHeatmapChart = () => {
  if (!heatmapChart.value) return;

  const chart = echarts.init(heatmapChart.value);
  const hours = Array.from({length: 24}, (_, i) => i + '时');
  const operationTypes = ['查看', '修改', '删除', '创建', '风险检测'];

  const option = {
    title: {
      text: '操作热度分析',
      left: 'center'
    },
    tooltip: {
      position: 'top'
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: hours,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: operationTypes,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: Math.max(...analyticsData.heatmapData.map(item => item[2])),
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%'
    },
    series: [{
      name: '操作次数',
      type: 'heatmap',
      data: analyticsData.heatmapData,
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  chart.setOption(option);
};

// 事件处理方法
const onDateRangeChange = () => {
  message.info('时间范围已更新，正在刷新数据...');
  refreshAnalytics();
};

const onCityChange = () => {
  refreshAnalytics();
};

const refreshTimeAnalysis = () => {
  loadLogStatistics().then(() => {
    initTimeDistributionChart();
  });
};

const refreshUserAnalysis = () => {
  loadLogStatistics().then(() => {
    initUserActivityChart();
  });
};

const onSearch = () => {
  loadLogData();
};

const onTableChange = (pagination: any) => {
  tablePagination.current = pagination.current;
  tablePagination.pageSize = pagination.pageSize;
  loadLogData();
};

const viewLogDetail = async (record: any) => {
  currentLogDetail.value = record;
  logDetailModalVisible.value = true;
};

const exportData = async () => {
  loading.export = true;
  try {
    // 导出逻辑
    message.success('数据导出成功');
  } catch (error) {
    message.error('数据导出失败');
  } finally {
    loading.export = false;
  }
};

// 初始化
onMounted(() => {
  console.log('🚀 [日志分析] 页面初始化开始');
  console.log('🚀 [日志分析] 用户信息:', {
    areaCode: userStore.getAreaCode,
    areaName: userStore.getAreaName,
    userId: userStore.getUserInfo?.userId,
    userName: userStore.getUserInfo?.username
  });

  // 根据用户权限设置默认地市
  if (!canSwitchCity.value) {
    // 非省级用户，设置为用户所属地市，不允许切换
    selectedCityCode.value = userStore.getAreaCode;
    console.log('🚀 [日志分析] 地市用户，默认地市:', selectedCityCode.value);
  } else {
    // 省级用户，默认查询无锡
    selectedCityCode.value = 'wx';
    console.log('🚀 [日志分析] 省级用户，默认查询无锡');
  }

  console.log('🚀 [日志分析] 权限控制:', {
    canSwitchCity: canSwitchCity.value,
    selectedCity: selectedCityCode.value,
    currentCityName: getCurrentCityName()
  });

  console.log('🚀 [日志分析] 默认时间范围:', {
    start: dateRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'),
    end: dateRange.value[1]?.format('YYYY-MM-DD HH:mm:ss')
  });
  console.log('🚀 [日志分析] 开始加载初始数据');

  refreshAnalytics();
});
</script>

<style scoped>
.dual-route-log-analytics {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.page-header {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.header-content {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.title-text {
  flex: 1;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #1a202c;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 4px 0 0 0;
  color: #718096;
  font-size: 14px;
}

.control-section {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.time-control,
.city-control,
.city-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.city-name {
  color: #1890ff;
  font-weight: 500;
  padding: 4px 12px;
  background: #f0f8ff;
  border-radius: 6px;
  border: 1px solid #d6e4ff;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #4a5568;
  font-weight: 500;
  white-space: nowrap;
}

.range-picker {
  min-width: 240px;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.loading-section {
  background: white;
  border-radius: 12px;
  padding: 60px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading-placeholder {
  height: 200px;
}

.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
}

.stat-label {
  color: #718096;
  font-size: 14px;
  margin-top: 4px;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.user-analysis-section,
.heatmap-section,
.data-table-section {
  width: 100%;
}

.chart-card,
.table-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header,
.table-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.chart-title,
.table-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-subtitle {
  color: #718096;
  font-size: 14px;
  margin-top: 4px;
}

.chart-content,
.table-content {
  padding: 20px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.full-width .chart-container {
  height: 400px;
}

.table-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.log-detail-modal .json-data {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

@media (max-width: 768px) {
  .dual-route-log-analytics {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .control-section {
    justify-content: center;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .charts-section {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
