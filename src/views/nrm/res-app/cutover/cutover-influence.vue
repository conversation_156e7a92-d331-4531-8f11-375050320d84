<script setup lang="ts">
  import { message, Modal } from 'ant-design-vue';
  import { defineProps, watch, ref, computed } from 'vue';
  import { useInfo, usePageQuery } from '@/hooks/web/useRestAPI';
  // import { getAreaName } from '@/views/nrm/res-app/cutover/cutover-util';
  // import { useMessage } from '@/hooks/web/useMessage';

  // import * as XLSX from 'xlsx';
  // import { saveAs } from 'file-saver';
  // import { formatToDateTime } from '@/utils/dateUtil';
  import { useProjectRunning } from '@/views/nrm/res-app/cutover/useProjectRunning';

  const props = defineProps<{ projectInfo: any; visible: boolean }>();
  // const projectInfo = ref<any>(props.projectInfo);
  // const emits = defineEmits(['doNext', 'doPrevious']);
  const columns = ref([
    {
      title: '资源编码',
      dataIndex: 'entity_code',
      key: 'entity_code',
    },
    {
      title: '资源名称',
      dataIndex: 'entity_name',
      key: 'entity_name',
    },
    {
      title: '接入号',
      dataIndex: 'access_code',
      key: 'access_code',
    },
    {
      title: '电路编号',
      dataIndex: 'circuit_code',
      key: 'circuit_code',
    },
    {
      title: '90天内割接次数',
      dataIndex: 'day30_times',
      key: 'day30_times',
    },
    {
      title: '影响业务名称',
      dataIndex: 'service_type',
      key: 'service_type',
    },
    {
      title: '客户类型',
      dataIndex: 'customer_type',
      key: 'customer_type',
    },
    {
      title: '客户等级',
      dataIndex: 'service_level',
      key: 'service_level',
    },
    {
      title: '客户名称',
      dataIndex: 'customer',
      key: 'customer',
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: '客户经理',
      dataIndex: 'customer_manager',
      key: 'customer_manager',
    },
    {
      title: '客户经理电话',
      dataIndex: 'customer_manager_phone',
      key: 'customer_manager_phone',
    },
    {
      title: '客户经理所属部门',
      dataIndex: 'customer_manager_org',
      key: 'customer_manager_org',
    },
    {
      title: '客户经理所属岗位',
      dataIndex: 'customer_manager_post',
      key: 'customer_manager_post',
    },
    {
      title: '是否生命线',
      dataIndex: 'is_lifecycle',
      key: 'is_lifecycle',
    },
    {
      title: '资源IP',
      dataIndex: 'dev_ip',
      key: 'dev_ip',
    },
    {
      title: '纤芯号',
      dataIndex: 'line_no',
      key: 'line_no',
    },
    {
      title: '光路名称',
      dataIndex: 'route_name',
      key: 'route_name',
    },
    {
      title: '光路编码',
      dataIndex: 'route_code',
      key: 'route_code',
    },
    {
      title: '光路A端设备',
      dataIndex: 'a_road_device_name',
      key: 'a_road_device_name',
    },
    {
      title: '光路A端端口',
      dataIndex: 'a_road_port_code',
      key: 'a_road_port_code',
    },
    {
      title: '光路Z端设备',
      dataIndex: 'z_road_device_name',
      key: 'z_road_device_name',
    },
    {
      title: '光路Z端端口',
      dataIndex: 'z_road_port_code',
      key: 'z_road_port_code',
    },
    {
      title: '光缆A端设备',
      dataIndex: 'a_cable_device_name',
      key: 'a_cable_device_name',
    },
    {
      title: '光缆A端端口',
      dataIndex: 'a_cable_port_code',
      key: 'a_cable_port_code',
    },
    {
      title: '光缆Z端设备',
      dataIndex: 'z_cable_device_name',
      key: 'z_cable_device_name',
    },
    {
      title: '光缆Z端端口',
      dataIndex: 'z_cable_port_code',
      key: 'z_cable_port_code',
    },
  ]);

  const roadColumns = [
    {
      title: '光缆编码',
      dataIndex: 'net_code',
      key: 'net_code',
    },
    {
      title: '缆段编码',
      dataIndex: 'cable_code',
      key: 'cable_code',
    },
    {
      title: '缆段名称',
      dataIndex: 'cable_name',
      key: 'cable_name',
    },
    {
      title: '纤芯号',
      dataIndex: 'line_code',
      key: 'line_code',
    },
    {
      title: '局向纤芯号',
      dataIndex: 'jx_fiber_code',
      key: 'jx_fiber_code',
    },
    {
      title: '光路编码',
      dataIndex: 'route_code',
      key: 'route_code',
    },
    {
      title: '光路名称',
      dataIndex: 'route_name',
      key: 'route_name',
    },
    {
      title: '光缆A设备编码',
      dataIndex: 'jx_a_device_code',
      key: 'jx_a_device_code',
    },
    {
      title: '光缆A设备名称',
      dataIndex: 'jx_a_device_name',
      key: 'jx_a_device_name',
    },
    {
      title: '光缆A端口编码',
      dataIndex: 'jx_a_port_code',
      key: 'jx_a_port_code',
    },
    {
      title: '光缆Z设备编码',
      dataIndex: 'jx_z_device_code',
      key: 'jx_z_device_code',
    },
    {
      title: '光缆Z设备名称',
      dataIndex: 'jx_z_device_name',
      key: 'jx_z_device_name',
    },
    {
      title: '光缆Z端口编码',
      dataIndex: 'jx_z_port_code',
      key: 'jx_z_port_code',
    },
  ];

  const roadGroupColumns = [
    {
      title: '保护组编码',
      dataIndex: 'opt_group_code',
      key: 'opt_group_code',
    },
    {
      title: '保护组名称',
      dataIndex: 'opt_group_name',
      key: 'opt_group_name',
    },
    {
      title: '割接影响',
      dataIndex: 'cutover_check_result',
      key: 'cutover_check_result',
    },
    {
      title: '包含光路',
      dataIndex: 'opt_codes',
      key: 'opt_codes',
    },
    {
      title: '割接影响光路',
      dataIndex: 'effect_opt_codes',
      key: 'effect_opt_codes',
    },
    {
      title: '包含光路数量',
      dataIndex: 'opt_code_num',
      key: 'opt_code_num',
    },
    {
      title: '影响光路数量',
      dataIndex: 'effect_opt_code_num',
      key: 'effect_opt_code_num',
    },
    {
      title: '光路组隐患',
      dataIndex: 'risk_check_result',
      key: 'risk_check_result',
    },
  ];

  watch(
    () => props.projectInfo,
    (newVal) => {
      console.log('======newVal=======', newVal);
      projectInfo.value = newVal;
      projectRunningStatusService.start();
      pageQuery();
    },
  );

  const influenceService = useInfo({
    rootPath: '/graph-rest-api',
  });
  const { info: influenceParam } = influenceService;
  /*
  const influenceRouteService = useInfo({
    rootPath: '/graph-rest-api',
  });
  const { info: influenceRouteParam, routeLoading } = influenceRouteService;

   */

  const queryService = usePageQuery({
    rootPath: 'graph-rest-api',
    queryUrl: '/api/cut-over/query-project-service',
    pagination: { pageSize: 10, hideOnSinglePage: true },
    filter: {
      action: '1',
    },
  });

  const queryRoadService = usePageQuery({
    rootPath: 'graph-rest-api',
    queryUrl: '/api/cut-over/query-project-road',
    pagination: { pageSize: 10, hideOnSinglePage: true },
    filter: {
      action: '1',
    },
  });

  const queryRoadGroupService = usePageQuery({
    rootPath: 'graph-rest-api',
    queryUrl: '/api/cut-over/query-project-opt-road-group',
    pagination: { pageSize: 10, hideOnSinglePage: true },
    filter: {
      action: '1',
    },
  });

  const summaryService = usePageQuery({
    rootPath: 'graph-rest-api',
    queryUrl: '/api/cut-over/summary-project-service',
    pagination: { pageSize: 100, hideOnSinglePage: true },

    filter: {
      action: '1',
    },
  });

  const projectInfoService = useInfo({
    rootPath: '/graph-rest-api',
  });

  const { dataSource, filter, loading: queryLoading } = queryService;
  const { dataSource: summary, filter: summaryFilter } = summaryService;
  const { dataSource: roadDataSource, filter: queryRoadFilter } = queryRoadService;
  const { dataSource: roadGroupDataSource, filter: queryRoadGroupFilter } = queryRoadGroupService;
  // eslint-disable-next-line vue/no-dupe-keys
  const { info: projectInfo } = projectInfoService;

  const pageQuery = async () => {
    filter.value.project_id = projectInfo.value.id;
    summaryFilter.value.project_id = projectInfo.value.id;
    queryRoadFilter.value.project_id = projectInfo.value.id;
    queryRoadGroupFilter.value.project_id = projectInfo.value.id;
    await queryService.pageQuery();
    await summaryService.pageQuery();
    await queryRoadService.pageQuery();
    await queryRoadGroupService.pageQuery();
    console.log('summary,', summary);
    // alert(JSON.stringify(projectInfo.value.resList));
  };

  const summaryInfo = computed(() => {
    let infoString = '';
    let orgNumber = 0;
    let publicNumber = 0;
    let orgString = '';
    let publicString = '';
    for (let i = 0; i < summary.value.length; i++) {
      let s = summary.value[i];
      if (s.customer_type == '政企') {
        orgNumber = orgNumber + s.row_count;
        orgString = orgString + s.service_type + s.row_count + '个 ';
      } else {
        publicNumber = publicNumber + s.row_count;
        publicString = publicString + s.service_type + s.row_count + '个 ';
      }
    }
    if (summary.value.length > 1) {
      infoString += '政企业务共' + orgNumber + '个,包含' + orgString + '。';
      infoString += '公众业务共' + publicNumber + '个,包含' + publicString + '。';
    }

    return infoString;
  });
  // const colorScale = d3.schemeCategory20b;
  // console.log('color:', colorScale);
  const downloadInfluenceService = useInfo({
    rootPath: '/graph-rest-api',
    info: {},
  });
  const { info: downloadInfluenceInfo, loading: downloadLoading } = downloadInfluenceService;
  const downloadInfluence = async () => {
    if (projectInfo.value.id == null || projectInfo.value.id == '') {
      alert('未找到割接单');
      return;
    }
    try {
      downloadInfluenceInfo.value.project_id = projectInfo.value.id;
      const result = await downloadInfluenceService.doCreateNew(`/api/cut-over/download-influence`);
      console.log('导出结果:', result);

      if (result.success) {
        // 显示成功消息
        message.success(`文件导出成功: ${result.fileName}`);

        // 显示跳转到文档安全平台的按钮
        Modal.confirm({
          title: '文件已上传到文档安全平台',
          content: `文件 "${result.fileName}" 已成功上传到文档安全平台，是否立即跳转查看？`,
          okText: '立即跳转',
          cancelText: '稍后查看',
          onOk() {
            // 新窗口打开文档安全平台
            window.open(result.redirectUrl, '_blank');
          },
        });
      } else {
        message.error(result.message || '导出失败');
      }
    } catch (error) {
      console.error('导出异常:', error);
      message.error('导出失败，请稍后重试');
    }
  };

  const downloadInfluenceRoute = async () => {
    if (projectInfo.value.id == null || projectInfo.value.id == '') {
      alert('未找到割接单');
      return;
    }
    try {
      downloadInfluenceInfo.value.project_id = projectInfo.value.id;
      downloadInfluenceInfo.value.city = projectInfo.value.area_code;
      downloadInfluenceInfo.value.resList = projectInfo.value.resList;
      const result = await downloadInfluenceService.doCreateNew(`/api/cut-over/downloadInfluenceRoute`);
      console.log('导出结果:', result);

      if (result.success) {
        // 显示成功消息
        message.success(`文件导出成功: ${result.fileName}`);

        // 显示跳转到文档安全平台的按钮
        Modal.confirm({
          title: '文件已上传到文档安全平台',
          content: `文件 "${result.fileName}" 已成功上传到文档安全平台，是否立即跳转查看？`,
          okText: '立即跳转',
          cancelText: '稍后查看',
          onOk() {
            // 新窗口打开文档安全平台
            window.open(result.redirectUrl, '_blank');
          },
        });
      } else {
        message.error(result.message || '导出失败');
      }
    } catch (error) {
      console.error('导出异常:', error);
      message.error('导出失败，请稍后重试');
    }
  };

  const downOptRouteGroup = async () => {
    if (projectInfo.value.id == null || projectInfo.value.id == '') {
      alert('未找到割接单');
      return;
    }
    try {
      downloadInfluenceInfo.value.project_id = projectInfo.value.id;
      downloadInfluenceInfo.value.city = projectInfo.value.area_code;
      downloadInfluenceInfo.value.resList = projectInfo.value.resList;
      const result = await downloadInfluenceService.doCreateNew(`/api/cut-over/downloadRouteGroup`);
      console.log('导出结果:', result);

      if (result.success) {
        // 显示成功消息
        message.success(`文件导出成功: ${result.fileName}`);

        // 显示跳转到文档安全平台的按钮
        Modal.confirm({
          title: '文件已上传到文档安全平台',
          content: `文件 "${result.fileName}" 已成功上传到文档安全平台，是否立即跳转查看？`,
          okText: '立即跳转',
          cancelText: '稍后查看',
          onOk() {
            // 新窗口打开文档安全平台
            window.open(result.redirectUrl, '_blank');
          },
        });
      } else {
        message.error(result.message || '导出失败');
      }
    } catch (error) {
      console.error('导出异常:', error);
      message.error('导出失败，请稍后重试');
    }
  };

  /*
  const resList = computed(() => {
    return projectInfo.value?.resList?.filter((item) => {
      return item.res_role == 'source';
    });
  });
   */

  const projectRunningStatusService = useProjectRunning({
    projectInfo: projectInfo,
    onComplete: () => {
      pageQuery();
    },
  });
  const { loading } = projectRunningStatusService;
  const tabActiveKey = ref<any>('service-list');

  /*
  function exportInfluence() {
    // 提取 title 形成 header 数组
    const headers = columns.value.map((column) => column.title);

    // 提取 key 形成 fields 数组
    const fields = columns.value.map((column) => column.key);

    const targetArray = dataSource.value.map((item) => {
      const targetObject = {};
      fields.forEach((field) => {
        targetObject[field] = item[field];
      });
      return targetObject;
    });

    const wb = XLSX.utils.book_new();
    // 将表格数据转换为工作表
    const ws = XLSX.utils.json_to_sheet(targetArray);

    //设置表头
    for (let col = 0; col < headers.length; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col }); // 编码单元格地址，例如'A1'
      ws[cellAddress] = { v: headers[col] }; // 设置单元格的值
    }

    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '影响分析');

    // 导出工作簿
    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    saveAs(
      new Blob([wbout], { type: 'application/octet-stream' }),
      projectInfo.value.name + '影响分析结果导出.xlsx',
    );
  }
   */
</script>

<template>
  <a-card v-show="props.visible">
    <template #title>
      <span class="card-title">影响分析</span>
    </template>
    <a-flex justify="flex-start" align="flex-start" wrap="wrap" gap="small">
      <a-button
        :size="'small'"
        type="primary"
        :loading="loading"
        @click="
          async () => {
            influenceParam.project_id = projectInfo.id;
            influenceParam.city = projectInfo.area_code;
            // influenceRouteParam.project_id = projectInfo.id;
            // influenceRouteParam.city = projectInfo.area_code;
            // influenceRouteParam.resList = projectInfo.resList;
            loading = true;
            await projectInfoService.doCreateNew('/api/cut-over');
            await influenceService.doCreateNew('/api/cut-over/influenceScope');
            projectRunningStatusService.start();
            // await influenceRouteService.doCreateNew('/api/cut-over/influenceRoute'); 在后端合并成一个方法
            await pageQuery();
          }
        "
        >影响分析</a-button
      >
      <a-button :size="'small'" :loading="queryLoading" @click="pageQuery">刷新</a-button>
      <a-button
        :size="'small'"
        type="primary"
        :loading="downloadLoading"
        @click="
          () => {
            downloadInfluence();
            // exportInfluence();
          }
        "
        >影响业务导出</a-button
      >
      <a-button
        :size="'small'"
        type="primary"
        :loading="downloadLoading"
        @click="
          () => {
            downloadInfluenceRoute();
          }
        "
        >影响光路导出</a-button
      >
      <a-button
        :size="'small'"
        type="primary"
        :loading="downloadLoading"
        @click="
          () => {
            downOptRouteGroup();
          }
        "
      >影响光路组导出</a-button
      >
    </a-flex>

    <a-alert :description="summaryInfo" type="warning" show-icon v-if="summaryInfo != ''" />

    <a-flex>
      <a-tabs v-model:activeKey="tabActiveKey" style="width: 100%">
        <a-tab-pane key="service-list" tab="影响业务">
          <a-flex>
            <a-table
              :columns="columns"
              :data-source="dataSource"
              rowKey="cfsAccessCode"
              :pagination="queryService.pagination"
              @change="queryService.pageChange"
              size="small"
              :scroll="{ x: 'max-content' }"
            >
              <template #bodyCell="{ column }">
                <template v-if="column.key === 'action'"> </template>
              </template>
            </a-table>
          </a-flex>
        </a-tab-pane>
        <a-tab-pane key="road-list" tab="影响光路" force-render>
          <a-table
            :columns="roadColumns"
            :data-source="roadDataSource"
            rowKey="cfsAccessCode"
            :pagination="queryRoadService.pagination"
            @change="queryRoadService.pageChange"
            size="small"
            :scroll="{ x: 'max-content' }"
          >
            <template #bodyCell="{ column }">
              <template v-if="column.key === 'action'"> </template>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="road-group-list" tab="光路保护组" force-render>
          <a-table
            :columns="roadGroupColumns"
            :data-source="roadGroupDataSource"
            rowKey="cfsAccessCode"
            :pagination="queryRoadGroupService.pagination"
            @change="queryRoadGroupService.pageChange"
            size="small"
            :scroll="{ x: 'max-content' }"
          >
            <template #bodyCell="{ column }">
              <template v-if="column.key === 'action'"> </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </a-flex>

    <!--
    <a-flex gap="middle" justify="center" align="start" vertical gutter="middle">
      <a-flex justify="center" align="flex-start" wrap="wrap" gap="small" style="width: 100%">
        <a-button @click="() => emits('doPrevious')">上一步</a-button>
        <a-button
          @click="
            () => {
              emits('doNext');
            }
          "
          >下一步</a-button
        >
      </a-flex>
    </a-flex>
    -->
  </a-card>
</template>

<style scoped lang="less"></style>
