interface GroupItem {
  title: string;
  icon: string;
  color: string;
  desc: string;
  date: string;
  group: string;
  path: string;
  tag?:string|null|undefined;
  roles?:string[] 
}

interface NavItem {
  title: string;
  icon: string;
  color: string;
}

interface DynamicInfoItem {
  avatar: string;
  name: string;
  date: string;
  desc: string;
}

export const navItems: NavItem[] = [
  {
    title: '首页',
    icon: 'ion:home-outline',
    color: '#1fdaca',
  },
  {
    title: '仪表盘',
    icon: 'ion:grid-outline',
    color: '#bf0c2c',
  },
  {
    title: '组件',
    icon: 'ion:layers-outline',
    color: '#e18525',
  },
  {
    title: '系统管理',
    icon: 'ion:settings-outline',
    color: '#3fb27f',
  },
  {
    title: '权限管理',
    icon: 'ion:key-outline',
    color: '#4daf1bc9',
  },
  {
    title: '图表',
    icon: 'ion:bar-chart-outline',
    color: '#00d8ff',
  },
];

export const dynamicInfoItems: DynamicInfoItem[] = [
  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年12月24日',
    desc: `障碍全息视图-新上线，可以直接地图查看全量主干断纤障碍单的位置以及全省申告汇聚情况；`
   
  }, 
  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年12月23日',
    desc: `障碍定位现在可以用接入号关联出来的OLT和分光器，直接去综调关联在途和历史的主干断纤障碍单和申告发现用户障碍单，在政企纤芯抢修右边的tab里呈现；`
   
  }, 
  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年12月19日',
    desc: `pon检测新增树状图绘制，并且点击节点可以查看光衰情况（再次点击节点关闭信息窗）`
   
  }, 
  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年12月11日',
    desc: `优化了双路由、障碍处理工作台、割接首页样式`
   
  }, 
  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年11月4日',
    desc: `（1）申告汇聚的结果，点击后会变红，不点击呈现蓝色；\n
    （2）资源影响分析导出功能上线，把障碍定位、障碍拦截中的导出功能全部集中在这里`,
  }, 
  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年10月31日',
    desc: `完成障碍处理工作台-障碍定位历史-表格信息优化`,
  }, 
  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年10月31日',
    desc: `完成权限控制开发`,
  }, 
  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年10月31日',
    desc: `障碍定位新增申告趋势图，可以分析随着时间产生的申告数据的变化`,
  }, 
  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年10月30日',
    desc: `新建了障碍工作台页面，将障碍处理的功能集成`,
  },  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年10月29日',
    desc: `群障定位现在可以用拦截单个光缆段了`,
  },  {
    avatar: 'moon|svg',
    name: '元凤',
    date: '2024年10月29日',
    desc: `优化了资源应用的功能列表，现在功能更加清晰`,
  },
];


export const groupItems: GroupItem[] = [
  {
    title: '障碍定位',
    icon: '障碍定位|svg',
    color: '#3fb27f',
    desc: '基于用户申告和接入号数据，进行障碍定位，呈现GIS地图和逻辑图',
    group: '',
    date: '',
    path: '/nrm/res-app/faultpositioning/groupfault',
    roles: ['障碍定位（菜单）'] 

  },

  {
    title: '障碍定位（南通）',
    icon: '障碍定位|svg',
    color: '#1890ff',
    desc: '南通地区专用障碍定位功能，针对南通地区地址格式和业务特点进行优化',
    group: '',
    date: '',
    path: '/nrm/res-app/faultpositioning/groupfault-nantong',
    tag: 'NEW',
    roles: ['障碍定位（菜单）']

  },
  // {
  //   title: '障碍定位-tab',
  //   icon: '障碍定位|svg',
  //   color: '#3fb27f',
  //   desc: '基于用户申告和接入号数据，进行障碍定位，呈现GIS地图和逻辑图',
  //   group: '',
  //   date: '',
  //   path: '/nrm/res-app/faultpositioning/groupfault-tab'
  // },
  {
    title: '主动拦截',
    icon: '障碍拦截|svg',
    color: '#3fb27f',
    desc: '发起预订时间的公众障碍，拦截公众用户投诉',
    group: '',
    date: '',
    path: '/nrm/res-app/faultpositioning/faultIntercept',
    roles: ['主动拦截（菜单）'] 
  },
  {
    title: '查询拦截',
    icon: '拦截查询|svg',
    color: '#e18525',
    desc: '查询曾经发起的拦截记录',
    group: '',
    date: '',
    path: '/nrm/res-app/faultpositioning/querycause',
    roles: ['拦截查询（菜单）'] 
  },
  // {
  //   title: '障碍视图',
  //   icon: '障碍视图|svg',
  //   color: '#bf0c2c',
  //   desc: '查看现在有多少障碍单，并关联告警，形成定位',
  //   group: '',
  //   date: '',
  //   path: '/nrm/res-app/faultpositioning/fault-hologram',
  //   roles: ['障碍视图（菜单）'] 
  // },
  {
    title: '障碍视图-新',
    icon: '障碍视图|svg',
    color: '#bf0c2c',
    desc: '查看现在有多少障碍单，并关联告警，形成定位',
    group: '',
    date: '',
    tag: "/resource/新增外力点标记.svg",
    path: '/nrm/res-app/faultpositioning/fault-hologram-new',
    roles: ['障碍视图（菜单）'] 

  },

  
  {
    title: '外力点管理',
    icon: '外力点管理|svg',
    color: '#00d8ff',
    desc: '管理和添加在障碍中发现的外部施工点，实现障碍发生时快速关联',
    group: '',
    date: '',
    path: '/nrm/res-app/faultpositioning/externalforcepoints',
    roles: ['外力点管理（菜单）'] 



  },
  {
    title: 'Pon口在线检测',
    icon: 'PON口检测|svg',
    color: '#EBD94E',
    desc: '通过IP和Pon口，一键检测在线情况',
    group: '',
    date: '',
    path: '/nrm/res-app/faultpositioning/check-pon',
    roles: ['PON口在线检测（菜单）'] 
  },
  {
    title: 'OTDR光缆断点定位',
    icon: 'OTDR检测|svg',
    color: '#EBD94E',
    desc: '现场OTDR打光定位光缆段断点后，显示断点地图位置',
    group: '',
    date: '',
    path: '/nrm/res-app/faultpositioning/otdrPositioning',
    roles: ['光缆断点定位（菜单）'] 

  },
  {
    title: '资源影响分析导出',
    icon: '清单导出|svg',
    color: '',
    desc: '导出资源影响清单，导出功能全部集成在这里',
    group: '',
    date: '',
    path: '/nrm/res-app/faultpositioning/ExportResInfluence',
    // tag: "/resource/新增外力点标记.svg",
    roles: ['资源影响分析导出（菜单）'] 

  },
  {
    title: '障碍功能使用数据分析',
    icon: '运营数据分析|svg',
    color: '',
    desc: '近期障碍运营数据分析，可以用于复盘障碍，优化管理',
    group: '',
    date: '',
    path: '/nrm/res-app/faultpositioning/dashboard-analysis',

  },


];
