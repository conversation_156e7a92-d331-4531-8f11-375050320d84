<script setup lang="ts">
  import IntroduceRow from './introduce-row.vue';
  import SalesCard from './sales-card.vue';
  import TopSearch from './components/top-search.vue';
  import ProportionSales from './proportion-sales.vue';
  import OfflineData from './offline-data.vue'
import CityScoreRanking from './city-score-ranking.vue';
  import dayjs from 'dayjs';

  defineOptions({
    name: 'dashboard-analysis',
  });

  const loading = ref(false);
  const visitData = ref([]);

  // 统一的时间范围控制
  const timeRange = ref([
    dayjs().subtract(3, 'month'),
    dayjs()
  ]);

  // 提供给子组件的时间范围
  provide('timeRange', timeRange);

  function handleTimeRangeChange(dates) {
    timeRange.value = dates;
    // 触发所有子组件重新加载数据
    nextTick(() => {
      window.dispatchEvent(new CustomEvent('timeRangeChanged', {
        detail: {
          startTime: dates[0]?.format('YYYY-MM-DD HH:mm:ss'),
          endTime: dates[1]?.format('YYYY-MM-DD HH:mm:ss')
        }
      }));
    });
  }

  // 快捷时间选择
  const timeRanges = {
    '最近7天': [dayjs().subtract(7, 'day'), dayjs()],
    '最近30天': [dayjs().subtract(30, 'day'), dayjs()],
    '最近3个月': [dayjs().subtract(3, 'month'), dayjs()],
    '最近6个月': [dayjs().subtract(6, 'month'), dayjs()],
    '最近1年': [dayjs().subtract(1, 'year'), dayjs()],
  };
</script>

<template>
  <page-container>
    <!-- 统一时间选择器 -->
    <a-card :bordered="false" style="margin-bottom: 24px;">
      <div style="display: flex; align-items: center; justify-content: space-between;">
        <div>
          <h2 style="margin: 0; color: #1890ff;">障碍定位功能使用分析</h2>
          <p style="margin: 8px 0 0 0; color: #666;">
            分析时间范围：{{ timeRange[0]?.format('YYYY-MM-DD') }} 至 {{ timeRange[1]?.format('YYYY-MM-DD') }}
          </p>
        </div>
        <div style="display: flex; align-items: center; gap: 16px;">
          <a-range-picker
            v-model:value="timeRange"
            :ranges="timeRanges"
            format="YYYY-MM-DD"
            @change="handleTimeRangeChange"
            style="width: 300px;"
          />
          <a-button type="primary" @click="handleTimeRangeChange(timeRange)">
            刷新分析
          </a-button>
        </div>
      </div>
    </a-card>

    <Suspense :fallback="null">
      <IntroduceRow :loading="loading" :visit-data="visitData" />
    </Suspense>

    <Suspense :fallback="null">
      <SalesCard :loading="loading" />
    </Suspense>

    <a-row
      :gutter="24"
      :style="{ marginTop: '24px' }"
    >
      <a-col :xl="12" :lg="24" :md="24" :sm="24" :xs="24">
        <Suspense :fallback="null">
          <TopSearch :loading="loading" />
        </Suspense>
      </a-col>
      <a-col :xl="12" :lg="24" :md="24" :sm="24" :xs="24">
        <Suspense :fallback="null">
          <ProportionSales :loading="loading" />
        </Suspense>
      </a-col>
    </a-row>

    <Suspense :fallback="null">
      <OfflineData :loading="loading" />
    </Suspense>

    <!-- 地市使用情况评分模块 -->
    <Suspense :fallback="null">
      <CityScoreRanking />
    </Suspense>
  </page-container>
</template>
