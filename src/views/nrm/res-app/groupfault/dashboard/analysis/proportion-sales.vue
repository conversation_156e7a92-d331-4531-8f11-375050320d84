<script setup lang="ts">
import { EllipsisOutlined } from '@ant-design/icons-vue'
import { Pie, Line } from '@antv/g2plot'
import { useInfo } from '@/hooks/web/useRestAPI'
import { useUserStoreWithOut } from '@/store/modules/user'
import dayjs from 'dayjs'

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})

const analysisType = ref('trend')
const pieContainer1 = ref()
const lineContainer1 = ref()
const lineContainer2 = ref()

function handleChangeAnalysisType(e: any) {
  analysisType.value = e.target.value
  nextTick(() => {
    if (analysisType.value === 'trend') {
      renderTrendCharts()
    } else if (analysisType.value === 'distribution') {
      renderDistributionChart()
    }
  })
}

const charts = shallowRef<any[]>([])
const userStore = useUserStoreWithOut()

// 障碍定位统计服务
const faultPositioningService = useInfo({
  rootPath: '/graph-rest-api',
})

// 统计数据
const timeAnalysisData = ref({
  dailyTrend: [],
  cityDistribution: [],
  successRateData: []
})

function renderDistributionChart() {
  // 清除之前的图表
  charts.value.forEach(chart => chart?.destroy?.())
  charts.value = []

  if (pieContainer1.value && timeAnalysisData.value.cityDistribution.length > 0) {
    const pie = new Pie(pieContainer1.value, {
      appendPadding: 10,
      data: timeAnalysisData.value.cityDistribution,
      angleField: 'value',
      colorField: 'city',
      radius: 1,
      innerRadius: 0.6,
      label: {
        type: 'spider',
        formatter: (item) => {
          return `${item.city}: ${item.value}`
        },
      },
      legend: {
        position: 'bottom',
      },
      interactions: [{ type: 'element-selected' }, { type: 'element-active' }],
      statistic: {
        title: {
          content: '总查询',
        },
        content: {
          content: timeAnalysisData.value.cityDistribution.reduce((sum, item) => sum + item.value, 0).toString(),
        },
      },
    })
    pie.render()
    charts.value.push(pie)
  }
}

function renderTrendCharts() {
  // 清除之前的图表
  charts.value.forEach(chart => chart?.destroy?.())
  charts.value = []

  // 渲染日查询趋势图
  if (lineContainer1.value && timeAnalysisData.value.dailyTrend.length > 0) {
    const line1 = new Line(lineContainer1.value, {
      data: timeAnalysisData.value.dailyTrend,
      xField: 'date',
      yField: 'count',
      smooth: true,
      color: '#1890ff',
      point: {
        size: 3,
      },
      xAxis: {
        label: {
          formatter: (text) => dayjs(text).format('MM-DD'),
        },
      },
      yAxis: {
        label: {
          formatter: (text) => `${text}次`,
        },
      },
      tooltip: {
        formatter: (datum) => {
          return { name: '障碍定位查询', value: `${datum.count}次` }
        },
      },
    })
    line1.render()
    charts.value.push(line1)
  }
}

// 城市代码到名称的映射
function cityCodeToName(code: string): string {
  const cityMap = {
    'js': '江苏省',
    'nj': '南京',
    'zj': '镇江',
    'wx': '无锡',
    'sz': '苏州',
    'nt': '南通',
    'yz': '扬州',
    'yc': '盐城',
    'xz': '徐州',
    'ha': '淮安',
    'lyg': '连云港',
    'cz': '常州',
    'tz': '泰州',
    'sq': '宿迁'
  }
  return cityMap[code] || '未知'
}

// 获取统一时间范围
const timeRange = inject('timeRange', ref([dayjs().subtract(3, 'month'), dayjs()]))

// 详情弹窗状态
const detailModalVisible = ref(false)
const detailModalTitle = ref('')
const detailModalData = ref([])
const detailModalLoading = ref(false)

// 查询时间分析数据
async function queryTimeAnalysisData() {
  try {
    const { info: statsInfo } = faultPositioningService

    // 使用统一的时间范围
    statsInfo.value = {
      start_time: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'),
      end_time: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')
    }

    const result = await faultPositioningService.doCreateNew(
      '/api/accs_nbr_no/query_tb_groupfault_log_postion'
    )

    if (result && result.value) {
      // 过滤掉测试用户数据
      const data = result.value.filter(item => item.operator !== '裴启程')

      // 按日期统计
      const dailyStats = {}
      const cityStats = {}
      const accessNumberStats = {}
      const userDailyStats = {}

      data.forEach(item => {
        const date = dayjs(item.query_time).format('YYYY-MM-DD')
        const city = item.city || 'unknown'
        const operator = item.operator
        const accessNumbers = item.accs_nbr_nos_list || ''

        // 日期统计
        if (!dailyStats[date]) {
          dailyStats[date] = { total: 0, users: new Set() }
        }
        dailyStats[date].total++
        dailyStats[date].users.add(operator)

        // 城市统计
        if (!cityStats[city]) {
          cityStats[city] = 0
        }
        cityStats[city]++

        // 接入号统计（分析频繁查询的接入号）
        if (accessNumbers) {
          const numbers = accessNumbers.split(',').map(n => n.trim()).filter(n => n)
          numbers.forEach(number => {
            if (!accessNumberStats[number]) {
              accessNumberStats[number] = { count: 0, dates: new Set() }
            }
            accessNumberStats[number].count++
            accessNumberStats[number].dates.add(date)
          })
        }

        // 用户每日查询统计
        if (!userDailyStats[date]) {
          userDailyStats[date] = {}
        }
        userDailyStats[date][operator] = (userDailyStats[date][operator] || 0) + 1
      })

      // 分析频繁查询的接入号（查询次数超过5次）
      const frequentAccessNumbers = Object.entries(accessNumberStats)
        .filter(([_, stats]: [string, any]) => stats.count > 5)
        .map(([number, stats]: [string, any]) => ({
          accessNumber: number,
          queryCount: stats.count,
          queryDays: stats.dates.size
        }))
        .sort((a, b) => b.queryCount - a.queryCount)
        .slice(0, 20) // 增加到20条

      // 分析单日多次查询的情况
      const dailyMultipleQueries = []
      Object.entries(userDailyStats).forEach(([date, users]: [string, any]) => {
        Object.entries(users).forEach(([user, count]: [string, any]) => {
          if (count > 3) {
            dailyMultipleQueries.push({ date, user, count })
          }
        })
      })

      // 按查询次数排序并增加数量
      dailyMultipleQueries.sort((a, b) => b.count - a.count)
      dailyMultipleQueries.splice(20) // 只保留前20条

      // 生成日趋势数据
      const dailyTrend = []

      Object.entries(dailyStats).forEach(([date, stats]: [string, any]) => {
        dailyTrend.push(
          { date, count: stats.total, type: '障碍定位查询' }
        )
      })

      // 生成城市分布数据
      const cityDistribution = Object.entries(cityStats)
        .map(([cityCode, count]: [string, any]) => ({
          city: cityCodeToName(cityCode),
          value: count
        }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 8) // 只显示前8个城市

      timeAnalysisData.value = {
        dailyTrend: dailyTrend.sort((a, b) => a.date.localeCompare(b.date)),
        cityDistribution,
        frequentAccessNumbers,
        dailyMultipleQueries
      }
    }
  } catch (error) {
    console.error('查询时间分析数据失败:', error)
  }
}

// 显示频繁查询接入号详情
async function showAccessNumberDetails(accessNumber: string) {
  try {
    detailModalLoading.value = true
    detailModalData.value = []
    detailModalTitle.value = `接入号 ${accessNumber} 查询详情`
    detailModalVisible.value = true

    console.log('查询接入号详情:', accessNumber)
    console.log('时间范围:', timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'), '到', timeRange.value[1].format('YYYY-MM-DD HH:mm:ss'))

    const { info: detailInfo } = faultPositioningService
    detailInfo.value = {
      start_time: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'),
      end_time: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss'),
      accs_nbr_nos_list: accessNumber
    }

    console.log('发送的查询参数:', detailInfo.value)

    const result = await faultPositioningService.doCreateNew(
      '/api/accs_nbr_no/query_tb_groupfault_log_postion'
    )

    console.log('接入号详情查询结果:', result)

    if (result && result.value) {
      const data = result.value.filter(item => item.operator !== '裴启程')

      console.log('过滤后的数据:', data)

      detailModalData.value = data.map(item => ({
        queryTime: dayjs(item.query_time).format('YYYY-MM-DD HH:mm:ss'),
        operator: item.operator,
        city: cityCodeToName(item.city || 'unknown'),
        accessNumbers: item.accs_nbr_nos_list,
        position: item.position || '-'
      }))

      console.log('最终弹窗数据:', detailModalData.value)
    } else {
      console.log('查询结果为空或无效')
    }
  } catch (error) {
    console.error('查询接入号详情失败:', error)
  } finally {
    detailModalLoading.value = false
  }
}

// 显示多次查询用户详情
async function showMultipleQueryDetails(user: string, date: string) {
  try {
    detailModalLoading.value = true
    detailModalData.value = []
    detailModalTitle.value = `${user} 在 ${date} 的查询详情`
    detailModalVisible.value = true

    const { info: detailInfo } = faultPositioningService
    detailInfo.value = {
      start_time: `${date} 00:00:00`,
      end_time: `${date} 23:59:59`,
      operator: user
    }

    const result = await faultPositioningService.doCreateNew(
      '/api/accs_nbr_no/query_tb_groupfault_log_postion'
    )

    if (result && result.value) {
      const data = result.value.filter(item => item.operator !== '裴启程')

      detailModalData.value = data.map(item => ({
        queryTime: dayjs(item.query_time).format('YYYY-MM-DD HH:mm:ss'),
        operator: item.operator,
        city: cityCodeToName(item.city || 'unknown'),
        accessNumbers: item.accs_nbr_nos_list,
        position: item.position || '-'
      }))
    }
  } catch (error) {
    console.error('查询用户详情失败:', error)
  } finally {
    detailModalLoading.value = false
  }
}

onMounted(async () => {
  // 监听全局时间范围变化事件
  window.addEventListener('timeRangeChanged', queryTimeAnalysisData)

  await queryTimeAnalysisData()
  renderTrendCharts()
})

onUnmounted(() => {
  window.removeEventListener('timeRangeChanged', queryTimeAnalysisData)
  charts.value.forEach((chart) => {
    chart?.destroy?.()
  })
  charts.value = []
})
</script>

<template>
  <a-card
    :loading="loading"
    class="analysisCard"
    :bordered="false"
    title="障碍定位功能时间分析"
    :style="{
      height: '100%',
    }"
  >
    <template #extra>
      <div class="analysisCardExtra">
        <a-dropdown placement="bottomRight">
          <template #overlay>
            <a-menu>
              <a-menu-item @click="queryTimeAnalysisData">刷新数据</a-menu-item>
              <a-menu-item>导出图表</a-menu-item>
            </a-menu>
          </template>
          <EllipsisOutlined />
        </a-dropdown>
        <div class="analysisTypeRadio">
          <a-radio-group :value="analysisType" @change="handleChangeAnalysisType">
            <a-radio-button value="trend">
              时间趋势
            </a-radio-button>
            <a-radio-button value="distribution">
              地市分布
            </a-radio-button>
            <a-radio-button value="analysis">
              使用分析
            </a-radio-button>
          </a-radio-group>
        </div>
      </div>
    </template>
    <div>
      <div v-show="analysisType === 'trend'">
        <a-typography-text style="margin-bottom: 16px; display: block;">障碍定位功能日查询趋势</a-typography-text>
        <div ref="lineContainer1" style="height: 400px;" />
      </div>
      <div v-show="analysisType === 'distribution'">
        <a-typography-text style="margin-bottom: 16px; display: block;">障碍定位功能地市查询分布</a-typography-text>
        <div ref="pieContainer1" style="height: 350px;" />
      </div>
      <div v-show="analysisType === 'analysis'">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-typography-text style="margin-bottom: 16px; display: block;">频繁查询接入号 (查询次数>5)</a-typography-text>
            <a-list
              size="small"
              :data-source="timeAnalysisData.frequentAccessNumbers || []"
              style="height: 400px; overflow-y: auto;"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a-tag
                        color="orange"
                        style="cursor: pointer;"
                        @click="showAccessNumberDetails(item.accessNumber)"
                      >
                        {{ item.accessNumber }}
                      </a-tag>
                    </template>
                    <template #description>
                      查询 {{ item.queryCount }} 次，涉及 {{ item.queryDays }} 天（点击查看详情）
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-col>
          <a-col :span="12">
            <a-typography-text style="margin-bottom: 16px; display: block;">单日多次查询情况 (>3次)</a-typography-text>
            <a-list
              size="small"
              :data-source="timeAnalysisData.dailyMultipleQueries || []"
              style="height: 400px; overflow-y: auto;"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a-tag
                        color="red"
                        style="cursor: pointer;"
                        @click="showMultipleQueryDetails(item.user, item.date)"
                      >
                        {{ item.user }}
                      </a-tag>
                    </template>
                    <template #description>
                      {{ item.date }} 查询 {{ item.count }} 次（点击查看详情）
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-col>
        </a-row>
      </div>
    </div>
  </a-card>

  <!-- 详情弹窗 -->
  <a-modal
    v-model:open="detailModalVisible"
    :title="detailModalTitle"
    width="1000px"
    :footer="null"
  >
    <a-table
      :columns="[
        { title: '查询时间', dataIndex: 'queryTime', key: 'queryTime', width: 150 },
        { title: '操作人', dataIndex: 'operator', key: 'operator', width: 100 },
        { title: '地市', dataIndex: 'city', key: 'city', width: 80 },
        { title: '接入号列表', dataIndex: 'accessNumbers', key: 'accessNumbers', ellipsis: true },
        { title: '标记位置', dataIndex: 'position', key: 'position', width: 120 }
      ]"
      :data-source="detailModalData"
      :loading="detailModalLoading"
      :pagination="{ pageSize: 10 }"
      size="small"
    />
  </a-modal>
</template>

<style scoped lang="less">
.analysisCardExtra {
  height: inherit;
}

.analysisTypeRadio {
  position: absolute;
  right: 54px;
  bottom: 12px;
}

.analysisCard {
  :deep(.ant-card-head) {
    position: relative;
  }
}
</style>
