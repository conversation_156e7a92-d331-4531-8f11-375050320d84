<template>
  <a-card
    title="地市使用情况评分排名（全部13个地市）"
    :bordered="false"
    :style="{ marginTop: '24px' }"
  >
    <template #extra>
      <a-tooltip title="综合障碍定位查询次数、拦截次数、活跃用户数计算评分">
        <InfoCircleOutlined style="color: #999;" />
      </a-tooltip>
    </template>

    <!-- 评分说明 -->
    <div style="margin-bottom: 16px; padding: 12px; background: #f6f8fa; border-radius: 6px;">
      <div style="font-weight: 500; margin-bottom: 8px;">评分规则：</div>
      <div style="color: #666; font-size: 13px;">
        <span style="margin-right: 24px;">• 障碍定位查询次数排名（权重30%）</span>
        <span style="margin-right: 24px;">• 拦截次数排名（权重40%）</span>
        <span>• 活跃用户数排名（权重30%）</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" style="text-align: center; padding: 40px;">
      <a-spin size="large" />
      <div style="margin-top: 16px; color: #666;">正在计算地市评分...</div>
    </div>

    <!-- 评分排名卡片 - 显示所有13个地市 -->
    <div v-else class="city-score-grid">
      <div
        v-for="(city, index) in cityScoreRanking"
        :key="city.cityName"
        class="city-score-card"
        :class="{ 'top-three': index < 3, 'top-ten': index < 10 }"
      >
        <!-- 排名徽章 -->
        <div class="rank-badge" :class="`rank-${index + 1}`">
          <span v-if="index < 3" class="rank-icon">
            {{ index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉' }}
          </span>
          <span v-else class="rank-number">{{ index + 1 }}</span>
        </div>

        <!-- 地市信息 -->
        <div class="city-info">
          <h3 class="city-name">{{ city.cityName }}</h3>
          <div class="city-score">
            <span class="score-value">{{ city.totalScore.toFixed(1) }}</span>
            <span class="score-label">分</span>
          </div>
        </div>

        <!-- 详细指标 -->
        <div class="city-metrics">
          <div class="metric-item">
            <span class="metric-label">查询次数</span>
            <span class="metric-value">{{ city.queryCount }}</span>
            <span class="metric-rank">(第{{ city.queryRank }}名)</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">拦截次数</span>
            <span class="metric-value">{{ city.interceptCount }}</span>
            <span class="metric-rank">(第{{ city.interceptRank }}名)</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">活跃用户</span>
            <span class="metric-value">{{ city.activeUsers }}</span>
            <span class="metric-rank">(第{{ city.userRank }}名)</span>
          </div>
        </div>

        <!-- 评分进度条 -->
        <div class="score-progress">
          <a-progress 
            :percent="(city.totalScore / 100) * 100" 
            :stroke-color="getScoreColor(city.totalScore)"
            :show-info="false"
            size="small"
          />
        </div>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-if="!loading && cityScoreRanking.length === 0" style="text-align: center; padding: 40px;">
      <a-empty description="暂无评分数据" />
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { ref, inject, onMounted, watch } from 'vue'
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { usePageQuery, useInfo } from '@/hooks/web/useRestAPI';

import dayjs from 'dayjs'

// 注入时间范围
const timeRange = inject('timeRange', ref([
  dayjs().subtract(3, 'month'), 
  dayjs()
]))

// 状态管理
const loading = ref(false)
const cityScoreRanking = ref<any[]>([])

// 查询服务 - 使用正确的障碍定位查询日志接口
const faultPositioningQueryService = useInfo({
  rootPath: '/graph-rest-api',
})

const interceptQueryService = usePageQuery({
  rootPath: '/graph-rest-api',
  queryUrl: '/api/accs_nbr_no/querycause',
  filter: {},
})

// 地市代码到名称的映射
function cityCodeToName(code: string): string {
  const cityMapping = {
    'nj': '南京',
    'sz': '苏州', 
    'wx': '无锡',
    'cz': '常州',
    'nt': '南通',
    'yz': '扬州',
    'tz': '泰州',
    'zj': '镇江',
    'yc': '盐城',
    'ha': '淮安',
    'sq': '宿迁',
    'lyg': '连云港',
    'xz': '徐州'
  }
  return cityMapping[code] || code
}

// 获取评分颜色
function getScoreColor(score: number): string {
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#faad14'
  if (score >= 40) return '#fa8c16'
  return '#ff4d4f'
}

// 计算排名分数（排名越高分数越高）
function calculateRankScore(rank: number, totalCities: number): number {
  return ((totalCities - rank + 1) / totalCities) * 100
}

// 计算并列排名（相同数值的地市获得相同排名）
function calculateTiedRanking(cityArray: any[], field: string): Record<string, number> {
  // 按指定字段降序排序
  const sortedCities = [...cityArray].sort((a, b) => b[field] - a[field])

  console.log(`=== ${field} 排名计算 ===`)
  console.log('排序后的地市数据:')
  sortedCities.forEach((city, index) => {
    console.log(`${index + 1}. ${cityCodeToName(city.cityCode)}: ${city[field]}`)
  })

  const rankMap: Record<string, number> = {}
  let currentRank = 1

  for (let i = 0; i < sortedCities.length; i++) {
    const city = sortedCities[i]

    // 如果不是第一个，且当前值与前一个值不同，则更新排名
    if (i > 0 && sortedCities[i][field] !== sortedCities[i - 1][field]) {
      currentRank = i + 1
    }

    rankMap[city.cityCode] = currentRank

    // 输出排名分配过程
    const prevValue = i > 0 ? sortedCities[i - 1][field] : null
    const currentValue = sortedCities[i][field]
    console.log(`${cityCodeToName(city.cityCode)}: 值=${currentValue}, 前一个值=${prevValue}, 分配排名=${currentRank}`)
  }

  console.log(`${field} 最终排名:`)
  Object.entries(rankMap).forEach(([cityCode, rank]) => {
    const city = cityArray.find(c => c.cityCode === cityCode)
    console.log(`${cityCodeToName(cityCode)}: 第${rank}名 (${field}=${city[field]})`)
  })

  return rankMap
}

// 计算地市综合评分
async function calculateCityScores() {
  try {
    loading.value = true
    console.log('=== 开始计算地市评分 ===')
    console.log('时间范围:', timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'), '到', timeRange.value[1].format('YYYY-MM-DD HH:mm:ss'))

    // 1. 查询障碍定位数据 - 使用正确的接口
    console.log('开始查询障碍定位数据...')
    const { info: faultPositioningInfo } = faultPositioningQueryService
    faultPositioningInfo.value = {
      start_time: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'),
      end_time: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')
    }

    const faultPositioningResult = await faultPositioningQueryService.doCreateNew(
      '/api/accs_nbr_no/query_tb_groupfault_log_postion'
    )
    console.log('障碍定位数据查询完成，原始数据量:', faultPositioningResult?.value?.length || 0)

    // 2. 查询拦截数据
    console.log('开始查询拦截数据...')
    interceptQueryService.filter.value = {
      startTime: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'),
      endTime: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')
    }
    interceptQueryService.pagination.value.pageSize = 10000
    interceptQueryService.pagination.value.current = 1

    await interceptQueryService.pageQuery()
    console.log('拦截数据查询完成，原始数据量:', interceptQueryService.dataSource.value?.length || 0)

    // 3. 初始化所有13个地市的统计数据
    const allCities = ['nj', 'sz', 'wx', 'cz', 'nt', 'yz', 'tz', 'zj', 'yc', 'ha', 'sq', 'lyg', 'xz']
    const cityStats = {}

    // 初始化所有地市数据
    allCities.forEach(cityCode => {
      cityStats[cityCode] = {
        queryCount: 0,
        interceptCount: 0,
        activeUsers: new Set()
      }
    })

    // 统计障碍定位查询数据
    if (faultPositioningResult && faultPositioningResult.value) {
      const queryData = faultPositioningResult.value.filter(item =>
        item.operator !== '裴启程'
      )

      console.log('过滤后障碍定位数据量:', queryData.length)
      console.log('障碍定位数据前3条:', queryData.slice(0, 3))

      queryData.forEach(item => {
        const city = item.city || 'unknown'
        if (cityStats[city]) {
          cityStats[city].queryCount++
          if (item.operator) {
            cityStats[city].activeUsers.add(item.operator)
          }
        } else if (city !== 'unknown') {
          console.log('发现未知地市代码:', city)
        }
      })

      console.log('障碍定位查询统计结果:')
      Object.entries(cityStats).forEach(([city, stats]: [string, any]) => {
        if (stats.queryCount > 0) {
          console.log(`${cityCodeToName(city)}: ${stats.queryCount}次查询, ${stats.activeUsers.size}个用户`)
        }
      })
    }

    // 统计拦截数据
    if (interceptQueryService.dataSource.value) {
      const interceptData = interceptQueryService.dataSource.value.filter(item =>
        item.operator !== '裴启程'
      )

      console.log('过滤后拦截数据量:', interceptData.length)
      console.log('拦截数据前3条:', interceptData.slice(0, 3))

      interceptData.forEach(item => {
        const city = item.city || 'unknown'
        if (cityStats[city]) {
          cityStats[city].interceptCount++
          if (item.operator) {
            cityStats[city].activeUsers.add(item.operator)
          }
        } else if (city !== 'unknown') {
          console.log('发现未知地市代码:', city)
        }
      })

      console.log('拦截数据统计结果:')
      Object.entries(cityStats).forEach(([city, stats]: [string, any]) => {
        if (stats.interceptCount > 0) {
          console.log(`${cityCodeToName(city)}: ${stats.interceptCount}次拦截, ${stats.activeUsers.size}个用户`)
        }
      })
    }

    // 4. 转换为数组并计算排名 - 包含所有13个地市
    const cityArray = allCities.map(cityCode => {
      const stats = cityStats[cityCode]
      return {
        cityCode,
        cityName: cityCodeToName(cityCode),
        queryCount: stats.queryCount,
        interceptCount: stats.interceptCount,
        activeUsers: stats.activeUsers.size
      }
    })

    console.log('所有地市统计结果:')
    cityArray.forEach(city => {
      console.log(`${city.cityName}: 查询${city.queryCount}次, 拦截${city.interceptCount}次, 用户${city.activeUsers}个`)
    })

    if (cityArray.length === 0) {
      cityScoreRanking.value = []
      return
    }

    // 5. 计算各项排名 - 修复并列排名问题

    // 计算查询次数排名（相同次数的地市排名相同）
    const queryRankMap = calculateTiedRanking(cityArray, 'queryCount')

    // 计算拦截次数排名（相同次数的地市排名相同）
    const interceptRankMap = calculateTiedRanking(cityArray, 'interceptCount')

    // 计算活跃用户数排名（相同用户数的地市排名相同）
    const userRankMap = calculateTiedRanking(cityArray, 'activeUsers')

    console.log('查询次数排名:', queryRankMap)
    console.log('拦截次数排名:', interceptRankMap)
    console.log('活跃用户排名:', userRankMap)

    // 6. 为每个地市分配排名和计算评分
    const cityScores = cityArray.map(city => {
      const queryRank = queryRankMap[city.cityCode]
      const interceptRank = interceptRankMap[city.cityCode]
      const userRank = userRankMap[city.cityCode]

      // 计算各项分数（排名越高分数越高）
      const queryScore = calculateRankScore(queryRank, cityArray.length)
      const interceptScore = calculateRankScore(interceptRank, cityArray.length)
      const userScore = calculateRankScore(userRank, cityArray.length)

      // 加权计算总分
      const totalScore = queryScore * 0.3 + interceptScore * 0.4 + userScore * 0.3

      console.log(`${city.cityName}: 查询排名${queryRank}(${queryScore.toFixed(1)}分), 拦截排名${interceptRank}(${interceptScore.toFixed(1)}分), 用户排名${userRank}(${userScore.toFixed(1)}分), 总分${totalScore.toFixed(1)}`)

      return {
        ...city,
        queryRank,
        interceptRank,
        userRank,
        queryScore,
        interceptScore,
        userScore,
        totalScore
      }
    })

    // 7. 按总分排序，显示所有13个地市
    cityScoreRanking.value = cityScores
      .sort((a, b) => b.totalScore - a.totalScore)

    console.log('地市评分计算完成，共', cityScoreRanking.value.length, '个地市:')
    cityScoreRanking.value.forEach((city, index) => {
      console.log(`第${index + 1}名: ${city.cityName} - ${city.totalScore.toFixed(1)}分`)
    })

  } catch (error) {
    console.error('计算地市评分失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听时间范围变化
watch(timeRange, () => {
  calculateCityScores()
}, { deep: true })

// 监听全局时间范围变化事件
onMounted(() => {
  window.addEventListener('timeRangeChanged', calculateCityScores)
  calculateCityScores()
})
</script>

<style scoped>
.city-score-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.city-score-card {
  position: relative;
  padding: 20px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.city-score-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.city-score-card.top-three {
  border: 2px solid #ffd700;
  background: linear-gradient(135deg, #fff9e6 0%, #fff 100%);
}

.city-score-card.top-ten {
  border: 1px solid #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #fff 100%);
}

.city-score-card:not(.top-ten) {
  border: 1px solid #ff7875;
  background: linear-gradient(135deg, #fff2f0 0%, #fff 100%);
  opacity: 0.8;
}

.rank-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  color: #fff;
  background: #1890ff;
}

.rank-badge.rank-1 { background: linear-gradient(135deg, #ffd700, #ffed4e); color: #333; }
.rank-badge.rank-2 { background: linear-gradient(135deg, #c0c0c0, #e8e8e8); color: #333; }
.rank-badge.rank-3 { background: linear-gradient(135deg, #cd7f32, #daa520); color: #fff; }

.rank-icon {
  font-size: 16px;
}

.rank-number {
  font-size: 14px;
}

.city-info {
  text-align: center;
  margin-bottom: 16px;
}

.city-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #262626;
}

.city-score {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.score-value {
  font-size: 24px;
  font-weight: 700;
  color: #1890ff;
}

.score-label {
  font-size: 14px;
  color: #666;
}

.city-metrics {
  margin-bottom: 16px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  font-size: 13px;
  color: #666;
}

.metric-value {
  font-weight: 600;
  color: #262626;
}

.metric-rank {
  font-size: 12px;
  color: #999;
}

.score-progress {
  margin-top: 12px;
}
</style>
