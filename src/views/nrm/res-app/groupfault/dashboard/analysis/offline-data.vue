<script setup lang="ts">
import { useInfo, usePageQuery } from '@/hooks/web/useRestAPI'
import { useUserStoreWithOut } from '@/store/modules/user'
import dayjs from 'dayjs'

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})

const activeKey = ref('recent')
const tableLoading = ref(false)
const userStore = useUserStoreWithOut()

// 障碍定位详细统计服务
const detailStatsService = useInfo({
  rootPath: '/graph-rest-api',
})

// 统计数据
const detailStatsData = ref([])
const summaryStats = ref({
  totalQueries: 0,
  totalPositioned: 0,
  totalUsers: 0,
  avgResponseTime: 0
})

function handleTabChange() {
  queryDetailStats()
}

// 表格列定义
const columns = [
  {
    title: '地市',
    dataIndex: 'cityName',
    key: 'cityName',
    width: 80,
  },
  {
    title: '障碍定位查询次数',
    dataIndex: 'totalQueries',
    key: 'totalQueries',
    width: 90,
    sorter: (a, b) => a.totalQueries - b.totalQueries,
  },
  {
    title: '拦截次数',
    dataIndex: 'interceptQueries',
    key: 'interceptQueries',
    width: 80,
    sorter: (a, b) => a.interceptQueries - b.interceptQueries,
  },
  {
    title: '拦截率',
    dataIndex: 'interceptRate',
    key: 'interceptRate',
    width: 80,
    sorter: (a, b) => parseFloat(a.interceptRate) - parseFloat(b.interceptRate),
  },
  {
    title: '活跃用户',
    dataIndex: 'activeUsers',
    key: 'activeUsers',
    width: 80,
    sorter: (a, b) => a.activeUsers - b.activeUsers,
  },
  {
    title: '人均查询',
    dataIndex: 'avgQueries',
    key: 'avgQueries',
    width: 80,
    sorter: (a, b) => a.avgQueries - b.avgQueries,
  },
  {
    title: '涉及接入号数',
    dataIndex: 'accessNumberCount',
    key: 'accessNumberCount',
    width: 90,
    sorter: (a, b) => a.accessNumberCount - b.accessNumberCount,
  },
  {
    title: '频繁查询用户',
    dataIndex: 'frequentUsers',
    key: 'frequentUsers',
    width: 90,
    sorter: (a, b) => a.frequentUsers - b.frequentUsers,
  },
  {
    title: '最近查询时间',
    dataIndex: 'lastQueryTime',
    key: 'lastQueryTime',
    width: 130,
    sorter: (a, b) => new Date(a.lastQueryTime).getTime() - new Date(b.lastQueryTime).getTime(),
  },
]
// 城市代码到名称的映射
function cityCodeToName(code: string): string {
  const cityMap = {
    'js': '江苏省',
    'nj': '南京',
    'zj': '镇江',
    'wx': '无锡',
    'sz': '苏州',
    'nt': '南通',
    'yz': '扬州',
    'yc': '盐城',
    'xz': '徐州',
    'ha': '淮安',
    'lyg': '连云港',
    'cz': '常州',
    'tz': '泰州',
    'sq': '宿迁'
  }
  return cityMap[code] || '未知'
}

// 获取统一时间范围
const timeRange = inject('timeRange', ref([dayjs().subtract(3, 'month'), dayjs()]))

// 查询详细统计数据
async function queryDetailStats() {
  try {
    tableLoading.value = true
    const { info: statsInfo } = detailStatsService

    // 使用统一的时间范围
    const startTime = timeRange.value[0].format('YYYY-MM-DD HH:mm:ss')
    const endTime = timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')

    statsInfo.value = {
      start_time: startTime,
      end_time: endTime
    }

    const result = await detailStatsService.doCreateNew(
      '/api/accs_nbr_no/query_tb_groupfault_log_postion'
    )

    if (result && result.value) {
      // 过滤掉测试用户数据
      const data = result.value.filter(item => item.operator !== '裴启程')

      // 按地市统计详细数据
      const cityDetailStats = {}
      const allUsers = new Set()
      const accessNumberAnalysis = {}

      data.forEach(item => {
        const city = item.city || 'unknown'
        const operator = item.operator
        const accessNumbers = item.accs_nbr_nos_list || ''

        allUsers.add(operator)

        if (!cityDetailStats[city]) {
          cityDetailStats[city] = {
            totalQueries: 0,
            interceptQueries: 0,
            users: new Set(),
            lastQueryTime: null,
            accessNumbers: new Set(),
            dailyQueries: {}
          }
        }

        cityDetailStats[city].totalQueries++
        cityDetailStats[city].users.add(operator)

        // 分析接入号
        if (accessNumbers) {
          const numbers = accessNumbers.split(',').map(n => n.trim()).filter(n => n)
          numbers.forEach(number => {
            cityDetailStats[city].accessNumbers.add(number)
            if (!accessNumberAnalysis[number]) {
              accessNumberAnalysis[number] = { count: 0, cities: new Set() }
            }
            accessNumberAnalysis[number].count++
            accessNumberAnalysis[number].cities.add(city)
          })
        }

        // 统计每日查询
        const date = dayjs(item.query_time).format('YYYY-MM-DD')
        if (!cityDetailStats[city].dailyQueries[date]) {
          cityDetailStats[city].dailyQueries[date] = {}
        }
        cityDetailStats[city].dailyQueries[date][operator] = (cityDetailStats[city].dailyQueries[date][operator] || 0) + 1

        const queryTime = new Date(item.query_time)
        if (!cityDetailStats[city].lastQueryTime || queryTime > cityDetailStats[city].lastQueryTime) {
          cityDetailStats[city].lastQueryTime = queryTime
        }
      })

      // 分析频繁查询的接入号
      const frequentAccessNumbers = Object.entries(accessNumberAnalysis)
        .filter(([_, stats]: [string, any]) => stats.count > 3)
        .length

      // 查询拦截数据并合并统计
      await queryInterceptDataForCities(cityDetailStats)

      // 转换为表格数据
      const tableData = Object.entries(cityDetailStats).map(([cityCode, stats]: [string, any]) => {
        const activeUsers = stats.users.size
        const interceptRate = stats.totalQueries > 0 ? (stats.interceptQueries / stats.totalQueries * 100).toFixed(1) : '0.0'
        const avgQueries = activeUsers > 0 ? Math.round(stats.totalQueries / activeUsers) : 0

        // 统计频繁查询用户
        let frequentUsers = 0
        Object.values(stats.dailyQueries).forEach((dayUsers: any) => {
          Object.values(dayUsers).forEach((count: number) => {
            if (count > 3) frequentUsers++
          })
        })

        return {
          cityCode,
          cityName: cityCodeToName(cityCode),
          totalQueries: stats.totalQueries,
          interceptQueries: stats.interceptQueries,
          interceptRate: interceptRate + '%',
          activeUsers,
          avgQueries,
          accessNumberCount: stats.accessNumbers.size,
          frequentUsers,
          lastQueryTime: stats.lastQueryTime ? dayjs(stats.lastQueryTime).format('YYYY-MM-DD HH:mm') : '-'
        }
      }).sort((a, b) => b.totalQueries - a.totalQueries)

      detailStatsData.value = tableData

      // 计算汇总统计
      const totalIntercepts = Object.values(cityDetailStats).reduce((sum: number, stats: any) => sum + stats.interceptQueries, 0)
      summaryStats.value = {
        totalQueries: data.length,
        totalIntercepts,
        totalUsers: allUsers.size,
        frequentAccessNumbers
      }
    }
  } catch (error) {
    console.error('查询详细统计数据失败:', error)
  } finally {
    tableLoading.value = false
  }
}

// 拦截数据查询服务
const interceptQueryService = usePageQuery({
  rootPath: '/graph-rest-api',
  queryUrl: '/api/accs_nbr_no/querycause',
  filter: {},
})

// 查询拦截数据
async function queryInterceptDataForCities(cityDetailStats: any) {
  try {
    // 设置查询参数，传递字符串格式的时间
    interceptQueryService.filter.value = {
      startTime: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'), // 格式化为字符串
      endTime: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')    // 格式化为字符串
    }

    // 设置分页参数以获取更多数据
    interceptQueryService.pagination.value.pageSize = 10000
    interceptQueryService.pagination.value.current = 1

    // 执行查询
    await interceptQueryService.pageQuery()

    if (interceptQueryService.dataSource.value) {
      const interceptData = interceptQueryService.dataSource.value.filter(item => item.operator !== '裴启程')

      // 按城市统计拦截数据
      interceptData.forEach(item => {
        const city = item.city || 'unknown'
        if (cityDetailStats[city]) {
          cityDetailStats[city].interceptQueries++
        }
      })
    }
  } catch (error) {
    console.error('查询拦截数据失败:', error)
  }
}

onMounted(() => {
  // 监听全局时间范围变化事件
  window.addEventListener('timeRangeChanged', queryDetailStats)

  queryDetailStats()
})

onBeforeUnmount(() => {
  window.removeEventListener('timeRangeChanged', queryDetailStats)
})
</script>

<template>
  <a-card :loading="loading" class="detailStatsCard" :bordered="false" :style="{ marginTop: '32px' }">
    <template #title>
      <span>障碍定位功能详细统计分析</span>
    </template>
    <template #extra>
      <a-button @click="queryDetailStats" type="primary" size="small">
        刷新数据
      </a-button>
    </template>

    <!-- 汇总统计卡片 -->
    <a-row :gutter="16" style="margin-bottom: 24px;">
      <a-col :span="6">
        <a-card size="small">
          <a-statistic
            title="障碍定位查询总数"
            :value="summaryStats.totalQueries"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card size="small">
          <a-statistic
            title="拦截总数"
            :value="summaryStats.totalIntercepts"
            :value-style="{ color: '#ff7875' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card size="small">
          <a-statistic
            title="活跃用户数"
            :value="summaryStats.totalUsers"
            :value-style="{ color: '#faad14' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card size="small">
          <a-statistic
            title="频繁查询接入号数"
            :value="summaryStats.frequentAccessNumbers"
            :value-style="{ color: '#722ed1' }"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 只保留一个详细统计分析标签页 -->
    <a-table
      :columns="columns"
      :data-source="detailStatsData"
      :loading="tableLoading"
      :pagination="{
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条记录`,
      }"
      size="middle"
      :scroll="{ x: 800 }"
    >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.key === 'cityName'">
              <a-tag :color="record.totalQueries > 50 ? 'blue' : record.totalQueries > 20 ? 'green' : 'orange'">
                {{ text }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'interceptRate'">
              <span :style="{ color: parseFloat(text) > 20 ? '#ff4d4f' : parseFloat(text) > 10 ? '#faad14' : '#52c41a' }">
                {{ text }}
              </span>
            </template>
            <template v-else-if="column.key === 'frequentUsers'">
              <a-badge
                :count="record.frequentUsers"
                :number-style="{ backgroundColor: record.frequentUsers > 5 ? '#f5222d' : record.frequentUsers > 2 ? '#faad14' : '#52c41a' }"
              />
            </template>
            <template v-else-if="column.key === 'accessNumberCount'">
              <a-tag :color="record.accessNumberCount > 100 ? 'red' : record.accessNumberCount > 50 ? 'orange' : 'green'">
                {{ text }}
              </a-tag>
            </template>
          </template>
    </a-table>
  </a-card>
</template>

<style scoped lang="less">
.detailStatsCard {
  :deep(.ant-tabs-ink-bar) {
    bottom: auto;
  }

  :deep(.ant-tabs-bar) {
    border-bottom: none;
  }

  :deep(.ant-tabs-nav-container-scrolling) {
    padding-right: 40px;
    padding-left: 40px;
  }

  :deep(.ant-tabs-tab-prev-icon::before) {
    position: relative;
    left: 6px;
  }

  :deep(.ant-tabs-tab-next-icon::before) {
    position: relative;
    right: 6px;
  }

  :deep(.ant-table-thead > tr > th) {
    background: linear-gradient(to right, #f0f7ff, #e6f7ff);
    color: #1890ff;
    font-weight: 500;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #e6f7ff !important;
  }
}
</style>
