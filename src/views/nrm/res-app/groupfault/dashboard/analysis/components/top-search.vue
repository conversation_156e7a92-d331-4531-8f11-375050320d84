<script setup lang="ts">
import { EllipsisOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { TinyArea } from '@antv/g2plot'
import { useInfo, usePageQuery } from '@/hooks/web/useRestAPI'
import { useUserStoreWithOut } from '@/store/modules/user'
import dayjs from 'dayjs'
import NumberInfo from '../number-info.vue'
import Trend from '../trend.vue'

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})

const columns: Record<string, any>[] = [
  {
    title: '排名',
    dataIndex: 'index',
    key: 'index',
    width: 50,
  },
  {
    title: '地市',
    dataIndex: 'cityName',
    key: 'cityName',
    width: 70,
  },
  {
    title: '障碍定位查询次数',
    dataIndex: 'count',
    key: 'count',
    sorter: (a: { count: number }, b: { count: number }) => b.count - a.count,
    width: 80,
  },
  {
    title: '拦截率',
    dataIndex: 'interceptRate',
    key: 'interceptRate',
    sorter: (a: { interceptRate: number }, b: { interceptRate: number }) => b.interceptRate - a.interceptRate,
    width: 80,
  },
  {
    title: '活跃用户',
    dataIndex: 'activeUsers',
    key: 'activeUsers',
    sorter: (a: { activeUsers: number }, b: { activeUsers: number }) => b.activeUsers - a.activeUsers,
    width: 70,
  },
  {
    title: '频繁查询',
    dataIndex: 'frequentUsers',
    key: 'frequentUsers',
    sorter: (a: { frequentUsers: number }, b: { frequentUsers: number }) => b.frequentUsers - a.frequentUsers,
    width: 70,
  },
]

// 地市使用情况统计数据
const cityStatsData = ref([])
const totalCityQueries = ref(0)
const avgSuccessRate = ref(0)

const userStore = useUserStoreWithOut()

// 障碍定位地市统计服务
const cityStatsService = useInfo({
  rootPath: '/graph-rest-api',
})

const tinyAreaContainer1 = ref()
const tinyAreaContainer2 = ref()

// 城市代码到名称的映射
function cityCodeToName(code: string): string {
  const cityMap = {
    'js': '江苏省',
    'nj': '南京',
    'zj': '镇江',
    'wx': '无锡',
    'sz': '苏州',
    'nt': '南通',
    'yz': '扬州',
    'yc': '盐城',
    'xz': '徐州',
    'ha': '淮安',
    'lyg': '连云港',
    'cz': '常州',
    'tz': '泰州',
    'sq': '宿迁'
  }
  return cityMap[code] || '未知'
}

// 获取统一时间范围
const timeRange = inject('timeRange', ref([dayjs().subtract(3, 'month'), dayjs()]))

// 查询地市使用情况统计
async function queryCityStats() {
  try {
    const { info: statsInfo } = cityStatsService

    // 使用统一的时间范围
    statsInfo.value = {
      start_time: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'),
      end_time: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')
    }

    const result = await cityStatsService.doCreateNew(
      '/api/accs_nbr_no/query_tb_groupfault_log_postion'
    )

    if (result && result.value) {
      // 过滤掉测试用户数据
      const data = result.value.filter(item => item.operator !== '裴启程')

      // 按地市统计
      const cityStats = {}
      const cityFrequentUsers = {}

      data.forEach(item => {
        const city = item.city || 'unknown'
        const date = dayjs(item.query_time).format('YYYY-MM-DD')
        const operator = item.operator

        if (!cityStats[city]) {
          cityStats[city] = {
            totalCount: 0,
            interceptCount: 0,
            users: new Set(),
            dailyQueries: {}
          }
        }

        cityStats[city].totalCount++
        cityStats[city].users.add(operator)

        // 统计每日查询
        if (!cityStats[city].dailyQueries[date]) {
          cityStats[city].dailyQueries[date] = {}
        }
        cityStats[city].dailyQueries[date][operator] = (cityStats[city].dailyQueries[date][operator] || 0) + 1
      })

      // 分析每个地市的频繁查询用户
      Object.entries(cityStats).forEach(([city, stats]: [string, any]) => {
        let frequentCount = 0
        Object.values(stats.dailyQueries).forEach((dayUsers: any) => {
          Object.values(dayUsers).forEach((count: number) => {
            if (count > 3) frequentCount++
          })
        })
        cityFrequentUsers[city] = frequentCount
      })



      // 查询拦截数据并合并统计
      await queryInterceptData(cityStats)

      // 转换为表格数据并排序
      const tableData = Object.entries(cityStats)
        .map(([cityCode, stats]: [string, any], index) => ({
          index: index + 1,
          cityCode,
          cityName: cityCodeToName(cityCode),
          count: stats.totalCount,
          interceptCount: stats.interceptCount,
          interceptRate: stats.totalCount > 0 ? (stats.interceptCount / stats.totalCount * 100).toFixed(1) : '0.0',
          activeUsers: stats.users.size,
          frequentUsers: cityFrequentUsers[cityCode] || 0
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10) // 只显示前10名

      cityStatsData.value = tableData
      totalCityQueries.value = data.length

      // 计算平均拦截率
      const totalIntercepts = Object.values(cityStats).reduce((sum: number, stats: any) => sum + stats.interceptCount, 0)
      avgSuccessRate.value = totalCityQueries.value > 0 ? (totalIntercepts / totalCityQueries.value * 100) : 0
    }
  } catch (error) {
    console.error('查询地市统计数据失败:', error)
  }
}

// 拦截数据查询服务
const interceptQueryService = usePageQuery({
  rootPath: '/graph-rest-api',
  queryUrl: '/api/accs_nbr_no/querycause',
  filter: {},
})

// 查询拦截数据
async function queryInterceptData(cityStats: any) {
  try {
    // 设置查询参数，传递字符串格式的时间
    interceptQueryService.filter.value = {
      startTime: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'), // 格式化为字符串
      endTime: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')    // 格式化为字符串
    }

    // 设置分页参数以获取更多数据
    interceptQueryService.pagination.value.pageSize = 10000
    interceptQueryService.pagination.value.current = 1

    // 执行查询
    await interceptQueryService.pageQuery()

    if (interceptQueryService.dataSource.value) {
      const interceptData = interceptQueryService.dataSource.value.filter(item => item.operator !== '裴启程')

      // 按城市统计拦截数据
      interceptData.forEach(item => {
        const city = item.city || 'unknown'
        if (cityStats[city]) {
          cityStats[city].interceptCount++
        }
      })
    }
  } catch (error) {
    console.error('查询拦截数据失败:', error)
  }
}

// 生成趋势数据（模拟最近7天的数据）
const generateTrendData = () => {
  const trendData1 = []
  const trendData2 = []

  for (let i = 6; i >= 0; i--) {
    // 模拟查询次数趋势
    trendData1.push(Math.floor(Math.random() * 50) + 20)
    // 模拟成功率趋势
    trendData2.push(Math.floor(Math.random() * 30) + 60)
  }

  return { trendData1, trendData2 }
}

onMounted(async () => {
  // 监听全局时间范围变化事件
  window.addEventListener('timeRangeChanged', queryCityStats)

  await queryCityStats()

  const { trendData1, trendData2 } = generateTrendData()

  new TinyArea(tinyAreaContainer1.value, {
    height: 45,
    data: trendData1,
    smooth: true,
    autoFit: true,
    areaStyle: {
      fill: 'l(270) 0:#ffffff 0.5:#91d5ff 1:#1890ff',
    },
    line: {
      color: '#1890ff',
    },
  }).render()

  new TinyArea(tinyAreaContainer2.value, {
    height: 45,
    data: trendData2,
    smooth: true,
    autoFit: true,
    areaStyle: {
      fill: 'l(270) 0:#ffffff 0.5:#b7eb8f 1:#52c41a',
    },
    line: {
      color: '#52c41a',
    },
  }).render()
})

onBeforeUnmount(() => {
  window.removeEventListener('timeRangeChanged', queryCityStats)
})
</script>

<template>
  <a-card
    :loading="loading"
    :bordered="false"
    title="障碍定位地市使用情况统计"
    :style="{ height: '100%' }"
  >
    <template #extra>
      <span class="iconGroup">
        <a-dropdown placement="bottomRight">
          <template #overlay>
            <a-menu>
              <a-menu-item @click="queryCityStats">刷新数据</a-menu-item>
              <a-menu-item>导出报表</a-menu-item>
            </a-menu>
          </template>
          <EllipsisOutlined />
        </a-dropdown>
      </span>
    </template>
    <a-row :gutter="68">
      <a-col :sm="12" :xs="24" :style="{ marginBottom: '24px' }">
        <NumberInfo
          :gap="8"
          :total="totalCityQueries"
          status="up"
          :sub-total="cityStatsData.length"
        >
          <template #subTitle>
            <span>
              障碍定位查询总数
              <a-tooltip title="最近30天全省障碍定位功能查询总次数（已排除测试数据）">
                <InfoCircleOutlined :style="{ marginLeft: '8px' }" />
              </a-tooltip>
            </span>
          </template>
        </NumberInfo>
        <div ref="tinyAreaContainer1" />
      </a-col>
      <a-col :sm="12" :xs="24" :style="{ marginBottom: '24px' }">
        <NumberInfo
          :gap="8"
          :total="avgSuccessRate.toFixed(1)"
          status="up"
          :sub-total="cityStatsData.filter(item => parseFloat(item.successRate) > avgSuccessRate).length"
        >
          <template #subTitle>
            <span>
              障碍定位平均拦截率(%)
              <a-tooltip title="全省障碍定位功能相关的平均拦截比率">
                <InfoCircleOutlined :style="{ marginLeft: '8px' }" />
              </a-tooltip>
            </span>
          </template>
        </NumberInfo>
        <div ref="tinyAreaContainer2" />
      </a-col>
    </a-row>
    <a-table
      :row-key="(record:any) => record.index"
      size="small"
      :columns="columns"
      :data-source="cityStatsData"
      :pagination="{
        style: { marginBottom: 0 },
        pageSize: 8,
        hideOnSinglePage: true,
      }"
    >
      <template #bodyCell="scope">
        <template v-if="scope?.column?.key === 'cityName'">
          <a-tag :color="scope?.record?.count > 50 ? 'blue' : scope?.record?.count > 20 ? 'green' : 'orange'">
            {{ scope?.record?.cityName }}
          </a-tag>
        </template>
        <template v-else-if="scope?.column?.key === 'interceptRate'">
          <Trend :flag="parseFloat(scope?.record?.interceptRate) > avgSuccessRate ? 'up' : 'down'">
            <span :style="{ marginRight: '4px' }">{{ scope?.record?.interceptRate }}%</span>
          </Trend>
        </template>
        <template v-else-if="scope?.column?.key === 'frequentUsers'">
          <a-badge
            :count="scope?.record?.frequentUsers"
            :number-style="{ backgroundColor: scope?.record?.frequentUsers > 5 ? '#f5222d' : '#52c41a' }"
          />
        </template>
      </template>
    </a-table>
  </a-card>
</template>

<style scoped lang="less">
.iconGroup {
  span.anticon {
    margin-left: 16px;
    color: inherit;
    cursor: pointer;
    transition: color 0.32s;
    &:hover {
      color: var(--text-color);
    }
  }
}
</style>
