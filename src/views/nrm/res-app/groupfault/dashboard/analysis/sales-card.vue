<script setup lang="ts">
import { Column } from '@antv/g2plot'
import type { Key } from 'ant-design-vue/es/_util/type'
import { useInfo, usePageQuery } from '@/hooks/web/useRestAPI'
import { useUserStoreWithOut } from '@/store/modules/user'
import dayjs from 'dayjs'

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})

// 内部loading状态
const internalLoading = ref(false)

// 障碍定位统计数据
const faultPositioningData = ref({
  dailyStats: [],
  cityDailyStats: [],
  cityRanking: [],
  userRanking: []
})

// 拦截分析数据
const interceptData = ref({
  interceptStats: [],
  cityInterceptRanking: [],
  typeInterceptStats: [],
  cityDailyInterceptStats: [] // 新增：地市每日拦截统计
})

// 拦截分析标签页状态
const interceptAnalysisType = ref('heatmap')

// 计划性拦截数据
const plannedInterceptData = ref({
  plannedStats: [],
  cityPlannedRanking: [],
  typePlannedStats: [],
  allCityData: [] // 所有地市数据
})

// 计划性拦截地市选择
const selectedCity = ref('all')

const userStore = useUserStoreWithOut()

// 障碍定位统计服务
const faultPositioningService = useInfo({
  rootPath: '/graph-rest-api',
})

// 移除独立的时间选择逻辑，使用统一时间范围

function convertNumber(number: number) {
  return number.toLocaleString()
}

// 城市代码到名称的映射
function cityCodeToName(code: string): string {
  const cityMap = {
    'js': '江苏省',
    'nj': '南京',
    'zj': '镇江',
    'wx': '无锡',
    'sz': '苏州',
    'nt': '南通',
    'yz': '扬州',
    'yc': '盐城',
    'xz': '徐州',
    'ha': '淮安',
    'lyg': '连云港',
    'cz': '常州',
    'tz': '泰州',
    'sq': '宿迁'
  }
  return cityMap[code] || '未知'
}

// 获取统一时间范围
const timeRange = inject('timeRange', ref([dayjs().subtract(3, 'month'), dayjs()]))

// 查询障碍定位数据
async function queryFaultPositioningData() {
  try {
    internalLoading.value = true
    const { info: statsInfo } = faultPositioningService

    // 使用统一的时间范围
    const startTime = timeRange.value[0].format('YYYY-MM-DD HH:mm:ss')
    const endTime = timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')

    statsInfo.value = {
      start_time: startTime,
      end_time: endTime
    }

    const result = await faultPositioningService.doCreateNew(
      '/api/accs_nbr_no/query_tb_groupfault_log_postion'
    )

    if (result && result.value) {
      // 过滤掉测试用户数据
      const data = result.value.filter(item => item.operator !== '裴启程')

      // 按日期和城市统计
      const dailyStats = {}
      const cityDailyStats = {}
      const cityStats = {}
      const userStats = {}

      data.forEach(item => {
        const date = dayjs(item.query_time).format('YYYY-MM-DD')
        const city = item.city || 'unknown'
        const operator = item.operator

        // 日度统计
        if (!dailyStats[date]) {
          dailyStats[date] = { total: 0, marked: 0 }
        }
        dailyStats[date].total++
        if (item.position && item.position.trim() !== '') {
          dailyStats[date].marked++
        }

        // 城市每日统计
        if (!cityDailyStats[city]) {
          cityDailyStats[city] = {}
        }
        if (!cityDailyStats[city][date]) {
          cityDailyStats[city][date] = 0
        }
        cityDailyStats[city][date]++

        // 城市总统计
        cityStats[city] = (cityStats[city] || 0) + 1

        // 用户统计
        userStats[operator] = (userStats[operator] || 0) + 1
      })

      // 生成日度图表数据
      const dailyChartData = Object.entries(dailyStats)
        .map(([date, stats]: [string, any]) => ({
          x: date,
          y: stats.total,
          marked: stats.marked
        }))
        .sort((a, b) => a.x.localeCompare(b.x))

      // 生成地市每日分布数据
      const cityDailyChartData = []
      Object.entries(cityDailyStats).forEach(([cityCode, dailyData]: [string, any]) => {
        const cityName = cityCodeToName(cityCode)
        Object.entries(dailyData).forEach(([date, count]: [string, any]) => {
          cityDailyChartData.push({
            date,
            city: cityName,
            count
          })
        })
      })

      // 生成城市排行数据
      const cityRanking = Object.entries(cityStats)
        .map(([cityCode, count]: [string, any]) => ({
          title: cityCodeToName(cityCode),
          total: count
        }))
        .sort((a, b) => b.total - a.total)
        .slice(0, 10)

      // 生成用户排行数据
      const userRanking = Object.entries(userStats)
        .map(([user, count]: [string, any]) => ({
          title: user,
          total: count
        }))
        .sort((a, b) => b.total - a.total)
        .slice(0, 10)

      faultPositioningData.value = {
        dailyStats: dailyChartData,
        cityDailyStats: cityDailyChartData,
        cityRanking,
        userRanking
      }

      // 重新渲染图表
      renderCharts()

      // 查询拦截数据
      await queryInterceptData()

      // 查询计划性拦截数据
      await queryPlannedInterceptData()

      // 重新渲染图表
      renderCharts()
    }
  } catch (error) {
    console.error('查询障碍定位数据失败:', error)
  } finally {
    internalLoading.value = false
  }
}

// 拦截数据查询服务
const interceptQueryService = usePageQuery({
  rootPath: '/graph-rest-api',
  queryUrl: '/api/accs_nbr_no/querycause',
  filter: {},
})

// 查询拦截数据
async function queryInterceptData() {
  try {
    // 添加详细日志分析时间参数传递
    console.log('=== 拦截数据查询开始 ===')
    console.log('timeRange.value:', timeRange.value)
    console.log('timeRange.value[0]:', timeRange.value[0])
    console.log('timeRange.value[1]:', timeRange.value[1])
    console.log('timeRange.value[0] 类型:', typeof timeRange.value[0])
    console.log('timeRange.value[0] 是否为dayjs:', dayjs.isDayjs(timeRange.value[0]))
    console.log('timeRange.value[0] 格式化:', timeRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'))
    console.log('timeRange.value[1] 格式化:', timeRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'))

    // 设置查询参数，使用统一时间范围
    // 注意：分页参数通过pagination设置，不在filter中
    const filterParams = {
      startTime: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'), // 格式化为字符串
      endTime: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss'),   // 格式化为字符串
      devType: 'cbl' // 只查询光缆段的障碍拦截
    }

    console.log('设置的filter参数:', filterParams)
    interceptQueryService.filter.value = filterParams

    // 设置分页参数以获取更多数据
    interceptQueryService.pagination.value.pageSize = 10000
    interceptQueryService.pagination.value.current = 1

    console.log('设置的分页参数:', interceptQueryService.pagination.value)

    // 添加网络请求前的参数检查
    console.log('执行查询前的filter.value:', interceptQueryService.filter.value)
    console.log('执行查询前的filter.value序列化:', JSON.stringify(interceptQueryService.filter.value, null, 2))

    // 执行查询
    console.log('开始执行拦截查询...')

    await interceptQueryService.pageQuery()

    console.log('拦截查询完成')

    if (interceptQueryService.dataSource.value) {
      console.log('原始拦截数据总数:', interceptQueryService.dataSource.value.length)
      console.log('原始拦截数据前3条:', interceptQueryService.dataSource.value.slice(0, 3))

      const data = interceptQueryService.dataSource.value.filter(item =>
        item.operator !== '裴启程' && item.devtype === 'cbl' // 只统计cbl类型的障碍拦截
      )

      console.log('过滤后拦截数据总数:', data.length)
      console.log('过滤后拦截数据前3条:', data.slice(0, 3))

      // 按城市、日期和拦截类型统计
      const cityInterceptStats = {}
      const typeInterceptStats = {}
      const cityDailyInterceptStats = {}

      data.forEach(item => {
        const city = item.city || 'unknown'
        const devType = item.devtype || 'unknown'
        const startDate = dayjs(item.starttime).format('YYYY-MM-DD')

        // 城市统计
        if (!cityInterceptStats[city]) {
          cityInterceptStats[city] = { total: 0, active: 0, recovered: 0 }
        }
        cityInterceptStats[city].total++
        if (item.isvalid) cityInterceptStats[city].active++
        if (item.isrecoveried) cityInterceptStats[city].recovered++

        // 城市每日统计
        if (!cityDailyInterceptStats[city]) {
          cityDailyInterceptStats[city] = {}
        }
        if (!cityDailyInterceptStats[city][startDate]) {
          cityDailyInterceptStats[city][startDate] = 0
        }
        cityDailyInterceptStats[city][startDate]++

        // 类型统计
        if (!typeInterceptStats[devType]) {
          typeInterceptStats[devType] = { total: 0, active: 0, recovered: 0 }
        }
        typeInterceptStats[devType].total++
        if (item.isvalid) typeInterceptStats[devType].active++
        if (item.isrecoveried) typeInterceptStats[devType].recovered++
      })

      // 生成城市拦截排行数据
      const cityInterceptRanking = Object.entries(cityInterceptStats)
        .map(([cityCode, stats]: [string, any]) => ({
          title: cityCodeToName(cityCode),
          total: stats.total,
          active: stats.active,
          recovered: stats.recovered
        }))
        .sort((a, b) => b.total - a.total)
        .slice(0, 10)

      // 生成拦截类型统计数据（主要是cbl光缆段）
      const typeMapping = {
        'cbl': '光缆段障碍拦截',
        'pon': 'PON口',
        'obd': '分光器'
      }

      const typeInterceptData = Object.entries(typeInterceptStats)
        .map(([type, stats]: [string, any]) => ({
          type: typeMapping[type] || type,
          total: stats.total,
          active: stats.active,
          recovered: stats.recovered
        }))

      // 生成地市每日拦截数据（用于热力图和曲线图）
      const cityDailyInterceptChartData = []
      Object.entries(cityDailyInterceptStats).forEach(([cityCode, dailyData]: [string, any]) => {
        const cityName = cityCodeToName(cityCode)
        Object.entries(dailyData).forEach(([date, count]: [string, any]) => {
          cityDailyInterceptChartData.push({
            date,
            city: cityName,
            count
          })
        })
      })

      interceptData.value = {
        interceptStats: data,
        cityInterceptRanking,
        typeInterceptStats: typeInterceptData,
        cityDailyInterceptStats: cityDailyInterceptChartData
      }
    }
  } catch (error) {
    console.error('查询拦截数据失败:', error)
  }
}

// 计划性拦截数据查询服务
const plannedInterceptQueryService = usePageQuery({
  rootPath: '/graph-rest-api',
  queryUrl: '/api/accs_nbr_no/querycause',
  filter: {},
})

// 查询计划性拦截数据（非cbl类型）
async function queryPlannedInterceptData(cityFilter = null) {
  try {
    console.log('=== 计划性拦截查询开始 ===')
    console.log('地市过滤条件:', cityFilter)

    // 设置查询参数，查询非cbl类型的计划性拦截
    // 注意：分页参数通过pagination设置，不在filter中
    const filterParams = {
      startTime: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'), // 格式化为字符串
      endTime: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')    // 格式化为字符串
    }

    // 如果有地市过滤条件，添加到查询参数中
    if (cityFilter && cityFilter !== 'all') {
      // 将地市名称转换为地市代码
      const cityCode = getCityCodeByName(cityFilter)
      if (cityCode) {
        filterParams.city = cityCode
      }
    }

    console.log('计划性拦截查询参数:', filterParams)
    plannedInterceptQueryService.filter.value = filterParams

    // 设置分页参数以获取更多数据
    plannedInterceptQueryService.pagination.value.pageSize = 10000
    plannedInterceptQueryService.pagination.value.current = 1

    // 执行查询
    console.log('开始执行计划性拦截查询...')
    await plannedInterceptQueryService.pageQuery()
    console.log('计划性拦截查询完成')

    if (plannedInterceptQueryService.dataSource.value) {
      console.log('原始计划性拦截数据总数:', plannedInterceptQueryService.dataSource.value.length)
      const data = plannedInterceptQueryService.dataSource.value.filter(item =>
        item.operator !== '裴启程' &&
        item.devtype !== 'cbl' && // 排除cbl类型，这些是计划性拦截
        item.devtype !== 'cbl_sect' // 也排除cbl_sect
      )

      console.log('过滤后计划性拦截数据总数:', data.length)
      console.log('过滤后计划性拦截数据前3条:', data.slice(0, 3))

      // 按城市和拦截类型统计
      const cityPlannedStats = {}
      const typePlannedStats = {}

      data.forEach(item => {
        const city = item.city || 'unknown'
        const devType = item.devtype || 'unknown'

        // 城市统计
        if (!cityPlannedStats[city]) {
          cityPlannedStats[city] = { total: 0, active: 0, recovered: 0 }
        }
        cityPlannedStats[city].total++
        if (item.isvalid) cityPlannedStats[city].active++
        if (item.isrecoveried) cityPlannedStats[city].recovered++

        // 类型统计
        if (!typePlannedStats[devType]) {
          typePlannedStats[devType] = { total: 0, active: 0, recovered: 0 }
        }
        typePlannedStats[devType].total++
        if (item.isvalid) typePlannedStats[devType].active++
        if (item.isrecoveried) typePlannedStats[devType].recovered++
      })

      // 生成城市计划性拦截排行数据
      const cityPlannedRanking = Object.entries(cityPlannedStats)
        .map(([cityCode, stats]: [string, any]) => ({
          title: cityCodeToName(cityCode),
          total: stats.total,
          active: stats.active,
          recovered: stats.recovered
        }))
        .sort((a, b) => b.total - a.total)
        .slice(0, 10)

      // 生成计划性拦截类型统计数据
      const typeMapping = {
        'pon': 'PON口割接',
        'obd': '分光器割接',
        'switch': '交换机割接',
        'router': '路由器割接',
        'other': '其他计划性拦截'
      }

      const typePlannedData = Object.entries(typePlannedStats)
        .map(([type, stats]: [string, any]) => ({
          type: typeMapping[type] || `${type}割接`,
          total: stats.total,
          active: stats.active,
          recovered: stats.recovered
        }))

      plannedInterceptData.value = {
        plannedStats: data,
        cityPlannedRanking,
        typePlannedStats: typePlannedData,
        allCityData: data // 保存所有地市的原始数据
      }

      console.log('计划性拦截数据更新完成:', plannedInterceptData.value)

      // 重新渲染图表
      setTimeout(() => {
        renderPlannedInterceptChart()
      }, 100)
    }
  } catch (error) {
    console.error('查询计划性拦截数据失败:', error)
  }
}

const columnPlotContainer1 = ref()
const columnPlotContainer2 = ref()
const columnPlotContainer3 = ref()

// 热力图容器
const heatmapContainer = ref()
const interceptHeatmapContainer = ref()
const interceptTrendContainer = ref()
let heatmapChart = null
let interceptHeatmapChart = null
let interceptTrendChart = null

const charts = shallowRef<Column[]>([])

function renderCharts() {
  // 清除之前的图表
  charts.value.forEach(chart => chart?.destroy?.())
  charts.value = []

  // 渲染热力图（专业-区县交叉分析样式）
  renderHeatmapChart()
}

// 渲染热力图
function renderHeatmapChart() {
  if (!heatmapContainer.value || !faultPositioningData.value.cityDailyStats.length) {
    return
  }

  // 清除之前的图表
  if (heatmapChart) {
    heatmapChart.dispose()
  }

  // 动态导入echarts
  import('echarts').then((echarts) => {
    heatmapChart = echarts.init(heatmapContainer.value)

    // 获取所有城市和日期
    const cities = [...new Set(faultPositioningData.value.cityDailyStats.map(item => item.city))]
    const dates = [...new Set(faultPositioningData.value.cityDailyStats.map(item => item.date))].sort()

    // 构建热力图数据
    const heatmapData = []
    dates.forEach((date, i) => {
      cities.forEach((city, j) => {
        const item = faultPositioningData.value.cityDailyStats.find(d =>
          d.city === city && d.date === date
        )
        heatmapData.push([i, j, item ? item.count : 0])
      })
    })

    const option = {
      title: {
        text: '地市-日期障碍定位查询热力图',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        position: 'top',
        formatter: function(params) {
          return `${dates[params.data[0]]} - ${cities[params.data[1]]}<br/>查询次数: ${params.data[2]}`
        }
      },
      grid: {
        height: '75%',
        top: '12%',
        left: '10%',
        right: '5%',
        bottom: '15%'
      },
      xAxis: {
        type: 'category',
        data: dates.map(date => dayjs(date).format('MM-DD')),
        splitArea: {
          show: true
        },
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'category',
        data: cities,
        splitArea: {
          show: true
        }
      },
      visualMap: {
        min: 0,
        max: Math.max(...heatmapData.map(item => item[2]), 1),
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
        inRange: {
          color: ['#ffffff', '#91d5ff', '#1890ff', '#0050b3']
        }
      },
      series: [
        {
          name: '查询次数',
          type: 'heatmap',
          data: heatmapData,
          label: {
            show: true,
            fontSize: 10
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }

    heatmapChart.setOption(option)
  })
}

function changTab(activeKey: Key) {
  if (activeKey === 'queries') {
    // 每次切换都重新渲染障碍定位查询统计图表
    setTimeout(() => {
      renderHeatmapChart()
    }, 100)
  } else if (activeKey === 'intercept') {
    // 每次切换都重新渲染拦截分析图表
    setTimeout(() => {
      renderInterceptChart()
      if (interceptAnalysisType.value === 'heatmap') {
        renderInterceptHeatmap()
      } else if (interceptAnalysisType.value === 'trend') {
        renderInterceptTrendChart()
      }
    }, 100)
  } else if (activeKey === 'planned') {
    // 每次切换都重新渲染计划性拦截图表
    setTimeout(() => {
      renderPlannedInterceptChart()
    }, 100)
  }
}

// 拦截分析类型切换
function handleInterceptAnalysisTypeChange(e) {
  interceptAnalysisType.value = e.target.value
  setTimeout(() => {
    if (interceptAnalysisType.value === 'heatmap') {
      renderInterceptHeatmap()
    } else if (interceptAnalysisType.value === 'trend') {
      renderInterceptTrendChart()
    }
  }, 100)
}

// 地市名称转换为代码
function getCityCodeByName(cityName) {
  const cityMapping = {
    '南京': 'nj',
    '苏州': 'sz',
    '无锡': 'wx',
    '常州': 'cz',
    '南通': 'nt',
    '扬州': 'yz',
    '泰州': 'tz',
    '镇江': 'zj',
    '盐城': 'yc',
    '淮安': 'ha',
    '宿迁': 'sq',
    '连云港': 'lyg',
    '徐州': 'xz'
  }
  return cityMapping[cityName] || null
}

// 计划性拦截地市切换
function handleCityChange(value) {
  selectedCity.value = value
  console.log('地市切换到:', value)
  // 重新查询数据而不是仅仅过滤
  queryPlannedInterceptData(value)
}

// 更新计划性拦截图表
function updatePlannedInterceptChart() {
  if (!plannedInterceptData.value.allCityData.length) {
    return
  }

  let filteredData = plannedInterceptData.value.allCityData
  if (selectedCity.value !== 'all') {
    filteredData = plannedInterceptData.value.allCityData.filter(item =>
      item.city === selectedCity.value
    )
  }

  // 按类型统计
  const typeStats = {}
  filteredData.forEach(item => {
    const devType = item.devtype || 'unknown'
    if (!typeStats[devType]) {
      typeStats[devType] = { total: 0, active: 0, recovered: 0 }
    }
    typeStats[devType].total++
    if (item.isvalid) typeStats[devType].active++
    if (item.isrecoveried) typeStats[devType].recovered++
  })

  const typePlannedData = Object.entries(typeStats).map(([type, stats]: [string, any]) => ({
    type: type === 'cbl' ? '光缆段' : type === 'pon' ? 'PON口' : type === 'obd' ? '分光器' : type,
    total: stats.total,
    active: stats.active,
    recovered: stats.recovered
  }))

  // 更新图表数据
  plannedInterceptData.value.typePlannedStats = typePlannedData

  // 重新渲染图表
  setTimeout(() => {
    renderPlannedInterceptChart()
  }, 100)
}

// 渲染拦截分析图表
function renderInterceptChart() {
  if (columnPlotContainer2.value && interceptData.value.typeInterceptStats.length > 0) {
    // 清除之前的图表
    const existingChart = charts.value.find(chart => chart.container === columnPlotContainer2.value)
    if (existingChart) {
      existingChart.destroy()
      charts.value = charts.value.filter(chart => chart !== existingChart)
    }

    import('@antv/g2plot').then(({ Column }) => {
      const chart2 = new Column(columnPlotContainer2.value, {
        data: interceptData.value.typeInterceptStats,
        xField: 'type',
        yField: 'total',
        height: 300,
        color: '#ff7875',
        meta: {
          total: {
            alias: '拦截次数',
          },
        },
        tooltip: {
          formatter: (datum) => {
            return {
              name: '拦截次数',
              value: `${datum.total}次`
            }
          },
        },
      })
      chart2.render()
      charts.value.push(chart2)
    })
  }
}

// 渲染计划性拦截图表
function renderPlannedInterceptChart() {
  if (columnPlotContainer3.value && plannedInterceptData.value.typePlannedStats.length > 0) {
    // 清除之前的图表
    const existingChart = charts.value.find(chart => chart.container === columnPlotContainer3.value)
    if (existingChart) {
      existingChart.destroy()
      charts.value = charts.value.filter(chart => chart !== existingChart)
    }

    import('@antv/g2plot').then(({ Column }) => {
      const chart3 = new Column(columnPlotContainer3.value, {
        data: plannedInterceptData.value.typePlannedStats,
        xField: 'type',
        yField: 'total',
        height: 300,
        color: '#52c41a',
        meta: {
          total: {
            alias: '计划性拦截次数',
          },
        },
        tooltip: {
          formatter: (datum) => {
            return {
              name: '计划性拦截次数',
              value: `${datum.total}次`
            }
          },
        },
      })
      chart3.render()
      charts.value.push(chart3)
    })
  }
}

// 渲染拦截分析热力图
function renderInterceptHeatmap() {
  if (!interceptHeatmapContainer.value || !interceptData.value.cityDailyInterceptStats.length) {
    return
  }

  // 清除之前的图表
  if (interceptHeatmapChart) {
    interceptHeatmapChart.dispose()
  }

  // 动态导入echarts
  import('echarts').then((echarts) => {
    interceptHeatmapChart = echarts.init(interceptHeatmapContainer.value)

    // 获取所有城市和日期
    const cities = [...new Set(interceptData.value.cityDailyInterceptStats.map(item => item.city))]
    const dates = [...new Set(interceptData.value.cityDailyInterceptStats.map(item => item.date))].sort()

    // 构建热力图数据
    const heatmapData = []
    dates.forEach((date, i) => {
      cities.forEach((city, j) => {
        const item = interceptData.value.cityDailyInterceptStats.find(d =>
          d.city === city && d.date === date
        )
        heatmapData.push([i, j, item ? item.count : 0])
      })
    })

    const option = {
      title: {
        text: '地市-日期拦截分析热力图',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        position: 'top',
        formatter: function(params) {
          return `${dates[params.data[0]]} - ${cities[params.data[1]]}<br/>拦截次数: ${params.data[2]}`
        }
      },
      grid: {
        height: '75%',
        top: '12%',
        left: '10%',
        right: '5%',
        bottom: '15%'
      },
      xAxis: {
        type: 'category',
        data: dates.map(date => dayjs(date).format('MM-DD')),
        splitArea: {
          show: true
        },
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'category',
        data: cities,
        splitArea: {
          show: true
        }
      },
      visualMap: {
        min: 0,
        max: Math.max(...heatmapData.map(item => item[2]), 1),
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
        inRange: {
          color: ['#ffffff', '#ffccc7', '#ff7875', '#ff4d4f']
        }
      },
      series: [
        {
          name: '拦截次数',
          type: 'heatmap',
          data: heatmapData,
          label: {
            show: true,
            fontSize: 10
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }

    interceptHeatmapChart.setOption(option)
  })
}

// 渲染拦截趋势图（地市时间曲线）
function renderInterceptTrendChart() {
  if (!interceptTrendContainer.value || !interceptData.value.cityDailyInterceptStats.length) {
    return
  }

  // 清除之前的图表
  if (interceptTrendChart) {
    interceptTrendChart.dispose()
  }

  // 动态导入echarts
  import('echarts').then((echarts) => {
    interceptTrendChart = echarts.init(interceptTrendContainer.value)

    // 获取所有城市和日期
    const cities = [...new Set(interceptData.value.cityDailyInterceptStats.map(item => item.city))]
    const dates = [...new Set(interceptData.value.cityDailyInterceptStats.map(item => item.date))].sort()

    // 为每个城市构建数据系列
    const series = cities.map(city => {
      const cityData = dates.map(date => {
        const item = interceptData.value.cityDailyInterceptStats.find(d =>
          d.city === city && d.date === date
        )
        return item ? item.count : 0
      })

      return {
        name: city,
        type: 'line',
        data: cityData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2
        }
      }
    })

    const option = {
      title: {
        text: '地市拦截趋势分析',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params) {
          let result = `${params[0].axisValue}<br/>`
          params.forEach(param => {
            result += `${param.seriesName}: ${param.value}次<br/>`
          })
          return result
        }
      },
      legend: {
        data: cities,
        top: '8%',
        type: 'scroll'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates.map(date => dayjs(date).format('MM-DD')),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: '拦截次数',
        axisLabel: {
          formatter: '{value}'
        }
      },
      series: series
    }

    interceptTrendChart.setOption(option)
  })
}

onMounted(async () => {
  // 添加时间范围注入检查日志
  console.log('=== SalesCard组件挂载 ===')
  console.log('注入的timeRange:', timeRange.value)
  console.log('timeRange[0]:', timeRange.value[0])
  console.log('timeRange[1]:', timeRange.value[1])
  console.log('timeRange[0] 格式化:', timeRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'))
  console.log('timeRange[1] 格式化:', timeRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'))

  // 监听全局时间范围变化事件
  window.addEventListener('timeRangeChanged', queryFaultPositioningData)

  await queryFaultPositioningData()
})

onBeforeUnmount(() => {
  window.removeEventListener('timeRangeChanged', queryFaultPositioningData)
  charts.value.forEach(chart => chart?.destroy?.())
  charts.value = []
})
</script>

<template>
  <a-card :loading="loading || internalLoading" :bordered="false" :body-style="{ padding: 0 }">
    <div class="salesCard">
      <a-tabs
        size="large"
        :tab-bar-style="{ marginBottom: '24px' }"
        @change="changTab"
      >
        <!-- 移除独立的时间选择，使用统一时间范围 -->
        <a-tab-pane key="queries" tab="障碍定位查询统计">
          <a-row>
            <a-col :xl="16" :lg="12" :md="12" :sm="24" :xs="24">
              <div class="salesBar">
                <div ref="heatmapContainer" style="height: 450px; width: 100%;" />
              </div>
            </a-col>
            <a-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <div class="salesRank">
                <h4 class="rankingTitle">
                  地市障碍定位查询排名
                </h4>
                <ul class="rankingList">
                  <li v-for="(item, index) in faultPositioningData.cityRanking" :key="index">
                    <span
                      :class="`rankingItemNumber ${index < 3 ? 'active' : ''}`"
                    >
                      {{ index + 1 }}
                    </span>
                    <span class="rankingItemTitle" :title="item.title">
                      {{ item.title }}
                    </span>
                    <span class="rankingItemValue">
                      {{ convertNumber(item.total) }}
                    </span>
                  </li>
                </ul>
              </div>
            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="intercept" tab="拦截分析统计">
          <!-- 分析类型切换 -->
          <div style="margin-bottom: 16px; text-align: center;">
            <a-radio-group :value="interceptAnalysisType" @change="handleInterceptAnalysisTypeChange">
              <a-radio-button value="heatmap">热力图分析</a-radio-button>
              <a-radio-button value="trend">趋势曲线分析</a-radio-button>
            </a-radio-group>
          </div>

          <a-row :gutter="16">
            <!-- 图表区域 -->
            <a-col :xl="18" :lg="16" :md="16" :sm="24" :xs="24">
              <!-- 热力图分析 -->
              <div v-show="interceptAnalysisType === 'heatmap'">
                <div class="salesBar">
                  <div ref="interceptHeatmapContainer" style="height: 400px; width: 100%;" />
                </div>
              </div>

              <!-- 趋势曲线分析 -->
              <div v-show="interceptAnalysisType === 'trend'">
                <div class="salesBar">
                  <div ref="interceptTrendContainer" style="height: 400px; width: 100%;" />
                </div>
              </div>
            </a-col>

            <!-- 排名区域 -->
            <a-col :xl="6" :lg="8" :md="8" :sm="24" :xs="24">
              <div class="salesRank">
                <h4 class="rankingTitle">
                  地市拦截情况排名
                </h4>
                <ul class="rankingList">
                  <li v-for="(item, index) in interceptData.cityInterceptRanking" :key="index">
                    <span
                      :class="`rankingItemNumber ${index < 3 ? 'active' : ''}`"
                    >
                      {{ index + 1 }}
                    </span>
                    <span class="rankingItemTitle" :title="item.title">
                      {{ item.title }}
                    </span>
                    <span class="rankingItemValue">
                      {{ convertNumber(item.total) }}
                      <small style="color: #999; margin-left: 4px;">
                        (活跃{{ item.active }}/恢复{{ item.recovered }})
                      </small>
                    </span>
                  </li>
                </ul>
              </div>
            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="planned" tab="计划性拦截分析">
          <!-- 地市选择 -->
          <div style="margin-bottom: 16px; text-align: center;">
            <a-select
              :value="selectedCity"
              @change="handleCityChange"
              style="width: 200px;"
              placeholder="选择地市"
            >
              <a-select-option value="all">全部地市</a-select-option>
              <a-select-option value="南京">南京</a-select-option>
              <a-select-option value="苏州">苏州</a-select-option>
              <a-select-option value="无锡">无锡</a-select-option>
              <a-select-option value="常州">常州</a-select-option>
              <a-select-option value="南通">南通</a-select-option>
              <a-select-option value="扬州">扬州</a-select-option>
              <a-select-option value="泰州">泰州</a-select-option>
              <a-select-option value="镇江">镇江</a-select-option>
              <a-select-option value="盐城">盐城</a-select-option>
              <a-select-option value="淮安">淮安</a-select-option>
              <a-select-option value="宿迁">宿迁</a-select-option>
              <a-select-option value="连云港">连云港</a-select-option>
              <a-select-option value="徐州">徐州</a-select-option>
            </a-select>
          </div>

          <a-row>
            <a-col :xl="16" :lg="12" :md="12" :sm="24" :xs="24">
              <div class="salesBar">
                <div ref="columnPlotContainer3" />
              </div>
            </a-col>
            <a-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <div class="salesRank">
                <h4 class="rankingTitle">
                  {{ selectedCity === 'all' ? '地市计划性拦截排名' : `${selectedCity}计划性拦截统计` }}
                </h4>
                <ul class="rankingList">
                  <li v-for="(item, index) in plannedInterceptData.cityPlannedRanking" :key="index">
                    <span
                      :class="`rankingItemNumber ${index < 3 ? 'active' : ''}`"
                    >
                      {{ index + 1 }}
                    </span>
                    <span class="rankingItemTitle" :title="item.title">
                      {{ item.title }}
                    </span>
                    <span class="rankingItemValue">
                      {{ convertNumber(item.total) }}
                      <small style="color: #999; margin-left: 4px;">
                        (活跃{{ item.active }}/恢复{{ item.recovered }})
                      </small>
                    </span>
                  </li>
                </ul>
              </div>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-card>
</template>

<style scoped lang="less">
.rankingList {
  margin: 25px 0 0;
  padding: 0;
  list-style: none;
  li {
    display: flex;
    align-items: center;
    margin-top: 16px;
    zoom: 1;
    &::before,
    &::after {
      display: table;
      content: " ";
    }
    &::after {
      clear: both;
      height: 0;
      font-size: 0;
      visibility: hidden;
    }
    span {
      color: var(--text-color);
      font-size: 14px;
      line-height: 22px;
    }
    .rankingItemNumber {
      display: inline-block;
      width: 20px;
      height: 20px;
      margin-top: 1.5px;
      margin-right: 16px;
      font-weight: 600;
      font-size: 12px;
      line-height: 20px;
      text-align: center;
      background-color: #fafafa;
      border-radius: 20px;
      &.active {
        color: #fff;
        background-color: #314659;
      }
    }
    .rankingItemTitle {
      flex: 1;
      margin-right: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.salesExtra {
  display: inline-block;
  margin-right: 24px;
  a {
    margin-left: 24px;
    color: var(--text-color);
    &:hover {
      color: #1890ff;
    }
    &.currentDate {
      color: #1890ff;
    }
  }
}

.salesCard {
  .salesBar {
    padding: 0 0 32px 32px;
  }
  .salesRank {
    padding: 0 32px 32px 72px;
  }
  :deep(.ant-tabs-nav-wrap) {
    padding-left: 16px;
    .ant-tabs-tab {
      padding-top: 16px;
      padding-bottom: 14px;
      line-height: 24px;
    }
  }
  :deep(.ant-tabs-bar) {
    padding-left: 16px;
    .ant-tabs-tab {
      padding-top: 16px;
      padding-bottom: 14px;
      line-height: 24px;
    }
  }
  :deep(.ant-tabs-extra-content) {
    padding-right: 24px;
    line-height: 55px;
  }
  :deep(.ant-card-head) {
    position: relative;
  }
  :deep(.ant-card-head-title) {
    align-items: normal;
  }
}

.salesCardExtra {
  height: inherit;
}

.salesTypeRadio {
  position: absolute;
  right: 54px;
  bottom: 12px;
}

.offlineCard {
  :deep(.ant-tabs-ink-bar) {
    bottom: auto;
  }
  :deep(.ant-tabs-bar) {
    border-bottom: none;
  }
  :deep(.ant-tabs-nav-container-scrolling) {
    padding-right: 40px;
    padding-left: 40px;
  }
  :deep(.ant-tabs-tab-prev-icon::before) {
    position: relative;
    left: 6px;
  }
  :deep(.ant-tabs-tab-next-icon::before) {
    position: relative;
    right: 6px;
  }
  :deep(.ant-tabs-tab-active h4) {
    color: #1890ff;
  }
}

@media screen and (max-width: 992px) {
  .salesExtra {
    display: none;
  }

  .rankingList {
    li {
      span:first-child {
        margin-right: 8px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .rankingTitle {
    margin-top: 16px;
  }

  .salesCard .salesBar {
    padding: 16px;
  }
}

@media screen and (max-width: 576px) {
  .salesExtraWrap {
    display: none;
  }

  .salesCard {
    :deep(.ant-tabs-content) {
      padding-top: 30px;
    }
  }
}
</style>
