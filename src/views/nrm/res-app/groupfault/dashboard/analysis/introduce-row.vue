<script setup lang="ts">
import { InfoCircleOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import { Progress, TinyArea, TinyColumn } from '@antv/g2plot'
import { useInfo, usePageQuery } from '@/hooks/web/useRestAPI'
import { useUserStoreWithOut } from '@/store/modules/user'
import dayjs from 'dayjs'

import ChartCard from './components/chart-card.vue'
import Field from './components/field.vue'
import Trend from './trend.vue'

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})

function convertNumber(number: number) {
  return number.toLocaleString()
}

const topColResponsiveProps = {
  xs: 24,
  sm: 12,
  md: 12,
  lg: 12,
  xl: 6,
  style: { marginBottom: '24px' },
}

const tinyAreaContainer = ref()
const tinyColumnContainer = ref()
const progressContainer = ref()
const tinyAreaContainer2 = ref()

// 移除不再使用的图表引用

// 障碍定位统计数据
const faultPositioningStats = ref({
  totalQueries: 0,
  totalIntercepts: 0,
  activeUsers: 0,
  interceptRate: 0,
  recentTrend: []
})

const userStore = useUserStoreWithOut()

// 障碍定位统计服务
const faultPositioningStatsService = useInfo({
  rootPath: '/graph-rest-api',
})

const tinyArea = shallowRef()
const tinyColumn = shallowRef()
const progress = shallowRef()
const tinyArea2 = shallowRef()

// 获取统一时间范围
const timeRange = inject('timeRange', ref([dayjs().subtract(3, 'month'), dayjs()]))

// 高频用户详情弹窗
const frequentUserModalVisible = ref(false)
const selectedUserName = ref('')
const currentUserIndex = ref(0)
const allUsersData = ref([])

// 用户选择下拉框
function handleUserSelectChange(value) {
  const userIndex = allUsersData.value.findIndex(user => user.user === value)
  if (userIndex !== -1) {
    currentUserIndex.value = userIndex
    renderCurrentUserChart()
  }
}

// 查询障碍定位统计数据
async function queryFaultPositioningStats() {
  try {
    const { info: statsInfo } = faultPositioningStatsService

    // 使用统一的时间范围
    statsInfo.value = {
      start_time: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'),
      end_time: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')
    }

    const result = await faultPositioningStatsService.doCreateNew(
      '/api/accs_nbr_no/query_tb_groupfault_log_postion'
    )

    if (result && result.value) {
      // 过滤掉测试用户数据
      const data = result.value.filter(item => item.operator !== '裴启程')

      // 统计分析
      const totalQueries = data.length
      const uniqueOperators = new Set(data.map(item => item.operator)).size

      // 查询拦截数据
      const interceptResult = await queryInterceptStats()
      const totalIntercepts = interceptResult.totalIntercepts || 0
      const interceptRate = totalQueries > 0 ? (totalIntercepts / totalQueries) : 0

      // 分析频繁查询用户
      const userQueryCount = {}
      const dailyQueryCount = {}

      data.forEach(item => {
        const operator = item.operator
        const date = dayjs(item.query_time).format('YYYY-MM-DD')

        // 用户查询统计
        userQueryCount[operator] = (userQueryCount[operator] || 0) + 1

        // 每日查询统计
        if (!dailyQueryCount[date]) {
          dailyQueryCount[date] = {}
        }
        dailyQueryCount[date][operator] = (dailyQueryCount[date][operator] || 0) + 1
      })

      // 找出频繁查询用户（一天内查询超过3次）
      const frequentUsers = []
      Object.entries(dailyQueryCount).forEach(([date, users]) => {
        Object.entries(users).forEach(([user, count]) => {
          if (count > 3) {
            frequentUsers.push({ date, user, count })
          }
        })
      })

      // 最近7天的趋势数据
      const recentTrend = []
      for (let i = 6; i >= 0; i--) {
        const date = dayjs().subtract(i, 'day')
        const dayData = data.filter(item =>
          dayjs(item.query_time).format('YYYY-MM-DD') === date.format('YYYY-MM-DD')
        )
        recentTrend.push(dayData.length)
      }

      faultPositioningStats.value = {
        totalQueries,
        totalIntercepts,
        activeUsers: uniqueOperators,
        interceptRate,
        recentTrend,
        frequentUsers: frequentUsers.length,
        userQueryCount,
        frequentUsersList: frequentUsers
      }
    }
  } catch (error) {
    console.error('查询障碍定位统计数据失败:', error)
  }
}

// 拦截数据查询服务
const interceptQueryService = usePageQuery({
  rootPath: '/graph-rest-api',
  queryUrl: '/api/accs_nbr_no/querycause',
  filter: {},
})

// 查询拦截统计数据
async function queryInterceptStats() {
  try {
    // 添加详细日志分析时间参数传递
    console.log('=== IntroduceRow 拦截统计查询开始 ===')
    console.log('timeRange.value:', timeRange.value)
    console.log('timeRange.value[0]:', timeRange.value[0])
    console.log('timeRange.value[1]:', timeRange.value[1])
    console.log('timeRange.value[0] 格式化:', timeRange.value[0]?.format('YYYY-MM-DD HH:mm:ss'))
    console.log('timeRange.value[1] 格式化:', timeRange.value[1]?.format('YYYY-MM-DD HH:mm:ss'))

    // 设置查询参数，传递字符串格式的时间
    const filterParams = {
      startTime: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'), // 格式化为字符串
      endTime: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')    // 格式化为字符串
    }

    console.log('IntroduceRow 设置的filter参数:', filterParams)
    interceptQueryService.filter.value = filterParams

    // 设置分页参数以获取更多数据
    interceptQueryService.pagination.value.pageSize = 10000
    interceptQueryService.pagination.value.current = 1

    // 执行查询
    console.log('IntroduceRow 开始执行拦截查询...')
    await interceptQueryService.pageQuery()
    console.log('IntroduceRow 拦截查询完成')

    if (interceptQueryService.dataSource.value) {
      console.log('IntroduceRow 原始拦截数据总数:', interceptQueryService.dataSource.value.length)
      console.log('IntroduceRow 原始拦截数据前3条:', interceptQueryService.dataSource.value.slice(0, 3))

      const data = interceptQueryService.dataSource.value.filter(item => item.operator !== '裴启程')
      console.log('IntroduceRow 过滤后拦截数据总数:', data.length)

      return {
        totalIntercepts: data.length,
        activeIntercepts: data.filter(item => item.isvalid).length,
        recoveredIntercepts: data.filter(item => item.isrecoveried).length
      }
    }

    return { totalIntercepts: 0, activeIntercepts: 0, recoveredIntercepts: 0 }
  } catch (error) {
    console.error('查询拦截统计数据失败:', error)
    return { totalIntercepts: 0, activeIntercepts: 0, recoveredIntercepts: 0 }
  }
}

// 显示高频用户详情
async function showFrequentUserDetails() {
  if (faultPositioningStats.value.frequentUsersList.length === 0) {
    return
  }

  try {
    const { info: detailInfo } = faultPositioningStatsService
    detailInfo.value = {
      start_time: timeRange.value[0].format('YYYY-MM-DD HH:mm:ss'),
      end_time: timeRange.value[1].format('YYYY-MM-DD HH:mm:ss')
    }

    const result = await faultPositioningStatsService.doCreateNew(
      '/api/accs_nbr_no/query_tb_groupfault_log_postion'
    )

    if (result && result.value) {
      const data = result.value.filter(item => item.operator !== '裴启程')

      // 生成高频用户的每日使用情况
      const userDailyStats = {}

      faultPositioningStats.value.frequentUsersList.forEach(({ user }) => {
        userDailyStats[user] = {}

        // 获取时间范围内的所有日期
        const startDate = timeRange.value[0]
        const endDate = timeRange.value[1]
        let currentDate = startDate.clone()

        while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
          const dateStr = currentDate.format('YYYY-MM-DD')
          userDailyStats[user][dateStr] = 0
          currentDate = currentDate.add(1, 'day')
        }
      })

      // 统计每个用户每日的查询次数
      data.forEach(item => {
        const user = item.operator
        const date = dayjs(item.query_time).format('YYYY-MM-DD')

        if (userDailyStats[user] && userDailyStats[user][date] !== undefined) {
          userDailyStats[user][date]++
        }
      })

      // 准备高频用户详情数据
      allUsersData.value = Object.entries(userDailyStats).map(([user, dailyData]) => ({
        user,
        dailyData: Object.entries(dailyData).map(([date, count]) => ({ date, count }))
      }))

      currentUserIndex.value = 0
      frequentUserModalVisible.value = true

      // 延迟渲染图表
      nextTick(() => {
        renderCurrentUserChart()
      })
    }
  } catch (error) {
    console.error('查询高频用户详情失败:', error)
  }
}

// 切换用户
function switchUser(direction: 'prev' | 'next') {
  if (direction === 'prev' && currentUserIndex.value > 0) {
    currentUserIndex.value--
  } else if (direction === 'next' && currentUserIndex.value < allUsersData.value.length - 1) {
    currentUserIndex.value++
  }
  renderCurrentUserChart()
}

// 渲染当前用户图表
function renderCurrentUserChart() {
  const container = document.getElementById('current-user-chart')
  if (container && allUsersData.value.length > 0) {
    const currentUser = allUsersData.value[currentUserIndex.value]
    if (currentUser && currentUser.dailyData.length > 0) {
      // 清除之前的图表
      container.innerHTML = ''

      import('@antv/g2plot').then(({ Line }) => {
        const chart = new Line(container, {
          data: currentUser.dailyData,
          xField: 'date',
          yField: 'count',
          height: 300,
          smooth: true,
          color: '#1890ff',
          point: {
            size: 4,
          },
          xAxis: {
            label: {
              formatter: (text) => dayjs(text).format('MM-DD'),
            },
          },
          yAxis: {
            label: {
              formatter: (text) => `${text}次`,
            },
          },
          tooltip: {
            formatter: (datum) => {
              return { name: '查询次数', value: `${datum.count}次` }
            },
          },
        })
        chart.render()
      })
    }
  }
}

// 监听时间范围变化
onMounted(() => {
  // 监听全局时间范围变化事件
  window.addEventListener('timeRangeChanged', queryFaultPositioningStats)

  // 初始加载
  queryFaultPositioningStats()
})

onMounted(async () => {
  await queryFaultPositioningStats()

  // 初始化图表
  if (faultPositioningStats.value.recentTrend.length > 0) {
    tinyArea.value = new TinyArea(tinyAreaContainer.value, {
      height: 46,
      data: faultPositioningStats.value.recentTrend,
      smooth: true,
      autoFit: true,
      areaStyle: {
        fill: 'l(270) 0:#ffffff 0.5:#91d5ff 1:#1890ff',
      },
      line: {
        color: '#1890ff',
      },
    })
    tinyArea.value?.render()

    tinyColumn.value = new TinyColumn(tinyColumnContainer.value, {
      height: 46,
      autoFit: true,
      data: faultPositioningStats.value.recentTrend,
      color: '#52c41a',
    })
    tinyColumn.value?.render()

    // 活跃用户趋势（模拟数据，实际可以根据需要查询）
    const userTrendData = faultPositioningStats.value.recentTrend.map(val => Math.max(1, Math.floor(val * 0.3)))
    tinyArea2.value = new TinyArea(tinyAreaContainer2.value, {
      height: 46,
      data: userTrendData,
      smooth: true,
      autoFit: true,
      areaStyle: {
        fill: 'l(270) 0:#ffffff 0.5:#ffd666 1:#faad14',
      },
      line: {
        color: '#faad14',
      },
    })
    tinyArea2.value?.render()
  }

  progress.value = new Progress(progressContainer.value, {
    height: 46,
    autoFit: true,
    percent: faultPositioningStats.value.frequentUsers / Math.max(faultPositioningStats.value.activeUsers, 1),
    barWidthRatio: 0.2,
    color: '#13C2C2',
  })
  progress.value?.render()
})

onBeforeUnmount(() => {
  window.removeEventListener('timeRangeChanged', queryFaultPositioningStats)
  tinyArea.value?.destroy()
  tinyArea.value = undefined
  tinyColumn.value?.destroy()
  tinyColumn.value = undefined
  progress.value?.destroy()
  progress.value = undefined
  tinyArea2.value?.destroy()
  tinyArea2.value = undefined
})
</script>

<template>
  <a-row :gutter="24">
    <a-col v-bind="{ ...topColResponsiveProps }">
      <ChartCard :bordered="false" title="障碍定位查询总数" :loading="loading" :content-height="46">
        <template #action>
          <a-tooltip title="最近30天障碍定位功能查询总次数（已排除测试数据）">
            <InfoCircleOutlined />
          </a-tooltip>
        </template>
        <template #total>
          <span>{{ convertNumber(faultPositioningStats.totalQueries) }}</span>
        </template>
        <template #footer>
          <Field label="今日查询" :value="faultPositioningStats.recentTrend[6] || 0" />
        </template>
        <Trend
          :flag="faultPositioningStats.recentTrend[6] > faultPositioningStats.recentTrend[5] ? 'up' : 'down'"
          :style="{ marginRight: '16px' }"
        >
          较昨日
          <span class="trendText">
            {{ faultPositioningStats.recentTrend[6] > faultPositioningStats.recentTrend[5] ? '+' : '' }}
            {{ ((faultPositioningStats.recentTrend[6] - faultPositioningStats.recentTrend[5]) || 0) }}
          </span>
        </Trend>
        <div ref="tinyAreaContainer" />
      </ChartCard>
    </a-col>

    <a-col v-bind="{ ...topColResponsiveProps }">
      <ChartCard :bordered="false" title="拦截总数" :loading="loading" :content-height="46">
        <template #action>
          <a-tooltip title="障碍定位功能相关的拦截事件总数">
            <InfoCircleOutlined />
          </a-tooltip>
        </template>
        <template #total>
          <span>{{ convertNumber(faultPositioningStats.totalIntercepts) }}</span>
        </template>
        <template #footer>
          <Field
            label="拦截率"
            :value="`${(faultPositioningStats.interceptRate * 100).toFixed(1)}%`"
          />
        </template>
        <div ref="tinyColumnContainer" />
      </ChartCard>
    </a-col>

    <a-col v-bind="{ ...topColResponsiveProps }">
      <ChartCard :bordered="false" title="障碍定位活跃用户" :loading="loading" :content-height="46">
        <template #action>
          <a-tooltip title="最近30天使用障碍定位功能的用户数量">
            <InfoCircleOutlined />
          </a-tooltip>
        </template>
        <template #total>
          <span>{{ convertNumber(faultPositioningStats.activeUsers) }}</span>
        </template>
        <template #footer>
          <Field
            label="人均查询"
            :value="faultPositioningStats.activeUsers > 0 ? Math.round(faultPositioningStats.totalQueries / faultPositioningStats.activeUsers) : 0"
          />
        </template>
        <div ref="tinyAreaContainer2" />
      </ChartCard>
    </a-col>

    <a-col v-bind="{ ...topColResponsiveProps }">
      <ChartCard :bordered="false" title="频繁查询用户数" :loading="loading" :content-height="46">
        <template #action>
          <a-tooltip title="单日内使用障碍定位功能超过3次的用户数量，点击查看详情">
            <InfoCircleOutlined />
          </a-tooltip>
        </template>
        <template #total>
          <span
            style="cursor: pointer; color: #1890ff;"
            @click="showFrequentUserDetails"
            :title="faultPositioningStats.frequentUsers > 0 ? '点击查看高频用户详情' : ''"
          >
            {{ convertNumber(faultPositioningStats.frequentUsers || 0) }}
          </span>
        </template>
        <template #footer>
          <div :style="{ whiteSpace: 'nowrap', overflow: 'hidden' }">
            <Trend
              :flag="faultPositioningStats.frequentUsers > 5 ? 'up' : 'down'"
              :style="{ marginRight: '16px' }"
            >
              使用频率
              <span class="trendText">
                {{ faultPositioningStats.frequentUsers > 10 ? '高频' : faultPositioningStats.frequentUsers > 5 ? '中频' : '正常' }}
              </span>
            </Trend>
          </div>
        </template>
        <div ref="progressContainer" />
      </ChartCard>
    </a-col>
  </a-row>

  <!-- 高频用户详情弹窗 -->
  <a-modal
    v-model:open="frequentUserModalVisible"
    title="高频用户每日使用情况"
    width="1200px"
    :footer="null"
  >
    <div v-if="allUsersData.length > 0">
      <!-- 用户切换控制 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <a-button
          :disabled="currentUserIndex === 0"
          @click="switchUser('prev')"
          type="primary"
          ghost
        >
          <LeftOutlined /> 上一个用户
        </a-button>

        <div style="text-align: center; flex: 1; margin: 0 16px;">
          <div style="margin-bottom: 8px;">
            <a-select
              :value="allUsersData[currentUserIndex]?.user"
              @change="handleUserSelectChange"
              style="width: 200px;"
              placeholder="选择用户"
            >
              <a-select-option
                v-for="(userData, index) in allUsersData"
                :key="index"
                :value="userData.user"
              >
                {{ userData.user }}
              </a-select-option>
            </a-select>
          </div>
          <span style="color: #666;">
            {{ currentUserIndex + 1 }} / {{ allUsersData.length }}
          </span>
        </div>

        <a-button
          :disabled="currentUserIndex === allUsersData.length - 1"
          @click="switchUser('next')"
          type="primary"
          ghost
        >
          下一个用户 <RightOutlined />
        </a-button>
      </div>

      <!-- 时间轴图表 -->
      <div style="height: 350px; border: 1px solid #f0f0f0; border-radius: 6px; padding: 16px;">
        <div id="current-user-chart" style="height: 100%; width: 100%;"></div>
      </div>

      <!-- 用户统计信息 -->
      <div style="margin-top: 16px; display: flex; justify-content: space-around; background: #fafafa; padding: 12px; border-radius: 6px;">
        <div style="text-align: center;">
          <div style="font-size: 18px; font-weight: bold; color: #1890ff;">
            {{ allUsersData[currentUserIndex]?.dailyData.reduce((sum, item) => sum + item.count, 0) || 0 }}
          </div>
          <div style="color: #666;">总查询次数</div>
        </div>
        <div style="text-align: center;">
          <div style="font-size: 18px; font-weight: bold; color: #52c41a;">
            {{ allUsersData[currentUserIndex]?.dailyData.length || 0 }}
          </div>
          <div style="color: #666;">活跃天数</div>
        </div>
        <div style="text-align: center;">
          <div style="font-size: 18px; font-weight: bold; color: #faad14;">
            {{ allUsersData[currentUserIndex]?.dailyData.length > 0 ?
                Math.round((allUsersData[currentUserIndex]?.dailyData.reduce((sum, item) => sum + item.count, 0) || 0) / allUsersData[currentUserIndex]?.dailyData.length)
                : 0 }}
          </div>
          <div style="color: #666;">日均查询</div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="less">

</style>
