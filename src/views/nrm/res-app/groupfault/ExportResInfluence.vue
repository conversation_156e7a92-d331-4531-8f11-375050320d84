<template>
    <PageWrapper>

        <a-card class="mb-2 mt-2">

            <!-- 城市 -->
            请选择城市：
            <a-select style="width: 10vw" :disabled="cityUnchangable" v-model:value="city" maxTagCount="13">
                <a-select-option value="yz">扬州</a-select-option>
                <a-select-option value="nj">南京</a-select-option>
                <a-select-option value="wx">无锡</a-select-option>
                <a-select-option value="sz">苏州</a-select-option>
                <a-select-option value="cz">常州</a-select-option>
                <a-select-option value="tz">泰州</a-select-option>
                <a-select-option value="ha">淮安</a-select-option>
                <a-select-option value="nt">南通</a-select-option>
                <a-select-option value="lyg">连云港</a-select-option>
                <a-select-option value="sq">宿迁</a-select-option>
                <a-select-option value="yc">盐城</a-select-option>
                <a-select-option value="xz">徐州</a-select-option>
                <a-select-option value="zj">镇江</a-select-option>
            </a-select>
            （非省级用户不可切换地市）



        </a-card>

        <!-- 纤芯级导出 暂时下线，有bug -->
        <!-- <a-card title="纤芯级导出" class="mb-2 mt-2">

            <a-flex >

              
            <a-radio-group v-model:value="QueryResType" button-style="solid"   style="width: 15vw">
              <a-radio-button value="1">光缆段  
               
            </a-radio-button>
              <a-radio-button value="2">管道段
               
              </a-radio-button>
            </a-radio-group>
            
      
                <a-input-group v-if="QueryResType=='1'" compact style="margin-left: 5px;">
            <a-input v-model:value="cbl_sect_nos" style="width: 40%" allow-clear  placeholder="编码" />


            <a-button style="margin-left: 1vw" @click="() => {
                const cbl_sect_no_list = splitItemsBySeparators(cbl_sect_nos);

                downloadOtherCblSectAccsNos(cbl_sect_no_list, city);
            }
                " :loading="downloading">下载</a-button>
        </a-input-group>

    

        
        <a-input-group v-if="QueryResType=='2'" compact style="margin-left: 5px">
            <a-input v-model:value="bse_sect_nos" style="width: 40%" allow-clear placeholder="编码" />

            <a-button style="margin-left: 1vw" @click="() => {
                if (!bse_sect_nos || bse_sect_nos.trim() === '') {
                    showalert('请输入支撑段清单，用逗号或者空格分隔！');
                    return;
                }

                const bse_sect_no_list = splitItemsBySeparators(bse_sect_nos);

                DownloadEnterpriseCustomerByBseSectNos(bse_sect_no_list, city);
            }
                " :loading="downloading">下载</a-button>
        </a-input-group>

    
    
                </a-flex>
        </a-card> -->

        <a-card title="资源影响业务导出" class="mb-2 mt-2">


            <a-row :gutter="[4, 8]" style="width: 100%" justify="left">
                <!-- 光交接箱   光分纤箱   光配线架    综合配线箱 -->

                <a-col :span="6">设备类型：
                    <a-select v-model:value="filter.devType" style="width: 70%; text-align: left"
                        @change="handleDevTypeChange" show-search>
                        <a-select-opt-group label="缆段">
                            <a-select-option value="cbl_sect">光缆段</a-select-option>
                            <a-select-option value="fiber_busi_node">光路</a-select-option>
                            <a-select-option value="net_code">光缆</a-select-option>
                            <a-select-option value="pipe_code">管道段</a-select-option>
                        </a-select-opt-group>
                        <a-select-opt-group label="设备">
                            <a-select-option value="olt">OLT设备</a-select-option>
                            <a-select-option value="card">板卡</a-select-option>
                            <a-select-option value="pon">pon口</a-select-option>
                            <a-select-option value="obd">分光器OBD</a-select-option>
                            <a-select-option value="onu">ONU</a-select-option>
                            <a-select-option value="odb">光交接箱</a-select-option>
                            <a-select-option value="odb2">光分纤箱</a-select-option>
                            <a-select-option value="odf">光配线架</a-select-option>
                            <a-select-option value="idc">综合配线箱</a-select-option>
                        </a-select-opt-group>
                    </a-select>
                </a-col>

                <a-col v-if="filter.devType == 'olt' || filter.devType == 'card' || filter.devType == 'pon'" :span="6">
                    <!-- <span  v-if="filter.devType == 'olt'" style="margin-right:1vw">或</span>   -->
                    <span>OLT设备IP:</span>
                    <a-input v-model:value="filter.devIP" style="width: 70%; text-align: left; margin-left: 1vw"
                        allow-clear />
                </a-col>

                <a-col v-if="filter.devType !== 'olt'" :span="6">
                    <span v-if="filter.devType == 'card'" style="margin-left: 1vw">&nbsp;板卡</span>
                    <span v-if="filter.devType == 'pon'" style="margin-left: 1vw">&nbsp;pon口</span>

                    <span style="margin-right: 1vw">编码:</span>

                    <a-input v-model:value="filter.devCode" style="width: 70%; text-align: left" allow-clear />
                </a-col>

                <a-col :span="6">
                    <a-button style="margin-left: 1vw; margin-left: 1vw" @click="() => {
                        tableDevType = filter.devType;
                        if (!filter.devCode && filter.devType !== 'olt') {
                            showalert('请输入需要查询的编码');
                            return;
                        } else if (!filter.devCode && !filter.devIP) {
                            showalert('请输入需要查询的编码或IP');
                            return;
                        }

                        ExecuteGetInfluence(filter, city);

                        // ExecuteGetPipeEffect(devCodes,city);
                    }
                        " :loading="ActionLoading" type="primary">查询</a-button>



                    <a-button type="primary" style="margin-left: 1vw; margin-left: 1vw" :loading="ActionLoading"
                        @click="exportTable">
                        导出
                    </a-button>

                
                </a-col>

                <a-col>
                    根据接入号合并：<a-switch v-model:checked="MergeSwitch" checked-children="开" un-checked-children="关" >
                        
                    </a-switch>
                </a-col>
            </a-row>

            <!-- <a-card  style="width:100%" :bordered="false" title="业务范围"> -->

            <!-- :columns="(tableDevType == 'cbl' || tableDevType == 'cblsect'  || tableDevType == 'bsesect')? cblColumns : devColumns" -->

            <a-card :bordered="false" title="影响用户清单">

                <a-tabs v-model:activeKey="TabActiveKey" type="card">

                    <a-tab-pane key="1" tab="政企">

                        <div>


                            <a-table :dataSource="relatedEnterpriseData" :columns="EnterpriseColumns"
                                :pagination="{ hideOnSinglePage: true }" rowKey="cfsAccessCode" size="small">

                            </a-table>
                        </div>

                    </a-tab-pane>
                    <a-tab-pane key="2" tab="公众">


                        <div>
                            <a-table :dataSource="relatednormalData" :columns="NormalColumns"
                                :pagination="{ hideOnSinglePage: true }" rowKey="cfsAccessCode" size="small">

                            </a-table>

                        </div>

                    </a-tab-pane>
                    <a-tab-pane key="3" tab="影响光路" v-if="filter.devType === 'cbl_sect'">

                        <div>
                            <a-table :dataSource="influenceRouteData" :columns="RouteColumns"
                                :pagination="{ hideOnSinglePage: true }" rowKey="route_code" size="small">

                            </a-table>

                        </div>

                    </a-tab-pane>
                </a-tabs>
            </a-card>


        </a-card>




    </PageWrapper>

</template>


<script lang="ts" setup>
import { useInput } from '@/hooks/input/useInput';
import { useInfo } from '@/hooks/web/useRestAPI';
import { useUserStoreWithOut } from '@/store/modules/user';
import { ref,h, defineOptions } from 'vue';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { notification } from 'ant-design-vue';
import {
  DownOutlined, RightOutlined, QuestionCircleTwoTone, SwapOutlined ,
  SearchOutlined,
  GlobalOutlined,
  PartitionOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined,
  SmileOutlined,
} from '@ant-design/icons-vue';



defineOptions({

    name: 'ExportResInfluence',

});

const QueryResType = ref('1');

const TabActiveKey = ref('1');


const city = ref('cz');
const cityUnchangable = ref(true);

// const line_no_res_input = ref();

const cbl_sect_nos = ref<string>('ASQ.CYJYA/ZGG27/09(GT001-ODF001）');
const bse_sect_nos = ref<string>('ASQ-GDD-NL00/#8201-NL00/#6001-92482');


const downloading = ref(false);


const DownloadSelectedCblSectAccsNosinfoService = useInfo({
    rootPath: '/graph-rest-api',
    responseType: 'blob',
});

const { info: DownloadSelectedCblSectAccsNosinfo } = DownloadSelectedCblSectAccsNosinfoService;


const showalert = (info) => {
    alert(info);
};
// 下载数据
const downloadOtherCblSectAccsNos = async (cbl_sect_no_list, city) => {
    downloading.value = true;

    DownloadSelectedCblSectAccsNosinfo.value = { codes: cbl_sect_no_list, city: city };

    const result = await DownloadSelectedCblSectAccsNosinfoService.doDownload(
        '/api/accs_nbr_no/download_cbl_sect_effect',
    );
    const blob = new Blob([result], { type: 'application/vnd.ms-excel' });
    const fileName = '其他光缆段影响的接入号清单' + '.xlsx';
    const a = document.createElement('a');
    a.download = fileName;
    a.href = window.URL.createObjectURL(blob);
    a.click();
    a.remove();

    downloading.value = false;
};
const DownloadEnterpriseCustomerByBseSectNosInfoService = useInfo({
    rootPath: '/graph-rest-api',
});
const { info: DownloadEnterpriseCustomerByBseSectNosInfo } =
    DownloadEnterpriseCustomerByBseSectNosInfoService;
// 下载支撑段关联的其他的政企客户的数据
const DownloadEnterpriseCustomerByBseSectNos = async (bse_sect_nos, city) => {
    downloading.value = true;
    DownloadEnterpriseCustomerByBseSectNosInfo.value = { codes: bse_sect_nos, city: city };
    const result = await DownloadEnterpriseCustomerByBseSectNosInfoService.doDownload(
        '/api/accs_nbr_no/download_pipe_effect',
    );
    const blob = new Blob([result], { type: 'application/vnd.ms-excel' });
    const fileName = '支撑段关联的其他的客户的数据' + '.xlsx';
    const a = document.createElement('a');
    a.download = fileName;
    a.href = window.URL.createObjectURL(blob);
    a.click();
    a.remove();

    downloading.value = false;
};


const userStore = useUserStoreWithOut();

console.log('userStore.getAreaCode', userStore.getAreaCode);
userStore.getAreaCode
if (userStore.getAreaCode == 'js') {
    city.value = 'cz'
    cityUnchangable.value = false  //js用户可以切换地市
}
else {

    city.value = userStore.getAreaCode;
    cityUnchangable.value = true //地市用户不可以切换地市

}


// --------------------------------------------导出影响业务清单-------------------------------------------

const filter = ref({ devType: '', devCode: '', devIP: '' });
filter.value.devType = '';
filter.value.devCode = '';
filter.value.devIP = '';

const MergeSwitch = ref(true);


//给测试用例数据赋值
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleDevTypeChange = (value, option) => {
    console.log('value', value);

    switch (value) {
        case 'cbl_sect':
            filter.value.devCode = 'LY.JDI/GJ006-LY.ANZJXU01/GB01/PXG01[GJ006-GT001]';

            break;
        case 'fiber_busi_node':
            filter.value.devCode = 'F2303057691';

            break;
        case 'net_code':
            filter.value.devCode = 'LY.JDI/GJ006-LY.ANZJXU01/GB01/PXG01';

            break;
        case 'pipe_code':
            filter.value.devCode = 'CZ-GDD-26473-69712-74083';

            break;
        case 'olt':
            filter.value.devIP = '***********';
            break;
        case 'card':
            filter.value.devCode = 'H906CGHF';
            filter.value.devIP = '***********';
            break;
        case 'pon':
            filter.value.devCode = '00/04/03';
            filter.value.devIP = '************';

            break;
        case 'obd':
            filter.value.devCode = 'CZ.CYJ/GJ015/OBD128';

            break;
        case 'onu':
            filter.value.devCode = 'WJ.CZEYBX29/HW-5818-ONU001';

            break;
        case 'odb':
            filter.value.devCode = 'CZ.CYJ/GJ015/OBD128';

            break;
        case 'odb2':
            filter.value.devCode = 'CZ.CYJ/GJ015/OBD128';

            break;
        case 'odf':
            filter.value.devCode = 'CZ.CYJ/GJ015/OBD128';

            break;
        case 'idc':
            filter.value.devCode = 'CZ.CYJ/GJ015/OBD128';

            break;

        default:
            break;
    }
};

const tableDevType = ref('');

const getInfluenceInfoService = useInfo({
    rootPath: '/graph-rest-api',
});
const { info: getInfluenceInfo } = getInfluenceInfoService;

const getInfluenceInfoResult = ref();

// 影响光路相关
const getInfluenceRouteService = useInfo({
    rootPath: '/graph-rest-api',
});
const { info: getInfluenceRouteInfo } = getInfluenceRouteService;

const getInfluenceRouteResult = ref();
const influenceRouteData: any = ref([]);


const ActionLoading = ref(false);
const relatedEnterpriseData: any = ref([]);
const relatednormalData: any = ref([]);
const ExecuteGetInfluence = async (filter, city) => {
    ActionLoading.value = true;
    relatednormalData.value = [];
    relatedEnterpriseData.value = [];
    influenceRouteData.value = [];

    getInfluenceInfo.value = {
        devCode: filter.devCode,
        devType: filter.devType,
        city: city,
        ip_addr: filter.devIP,
    };

    console.log('getresourceinfluence.value', getInfluenceInfo.value);

    getInfluenceInfoResult.value = await getInfluenceInfoService.doCreateNew(
        '/api/influenceanalysis/getresourceinfluence',
    );

    // 如果是光缆段类型，同时查询影响光路
    if (filter.devType === 'cbl_sect') {
        getInfluenceRouteInfo.value = {
            devCode: filter.devCode,
            devType: filter.devType,
            city: city,
        };

        console.log('getinfluenceroute.value', getInfluenceRouteInfo.value);

        getInfluenceRouteResult.value = await getInfluenceRouteService.doCreateNew(
            '/api/influenceanalysis/getinfluenceroute',
        );

        console.log('getInfluenceRouteResult', getInfluenceRouteResult.value);

        if (getInfluenceRouteResult.value && getInfluenceRouteResult.value.route_influence) {
            influenceRouteData.value = getInfluenceRouteResult.value.route_influence;
        }
    }



    console.log('getInfluenceInfoResult',getInfluenceInfoResult.value);
    
    const govBizServiceMap = new Map();
    const publicUserServiceMap = new Map();

    if(getInfluenceInfoResult.value.cfs_influence.length==0){


        notification.open({
          message: `未查到业务信息`,
          duration: 0,
          icon: () => h(CloseCircleOutlined, { style: 'color: red' }),
        });
        
    }

    extractCodes(getInfluenceInfoResult.value);


    function extractCodes(jsonData) {
        // let resultList: any[] = [];

        console.log('jsonData', jsonData);
        if (jsonData && jsonData.cfs_influence) {
            jsonData.cfs_influence.forEach((cfs) => {
                // const serviceName = cfs.service_spec_name;


                console.log('cfs',cfs);
                
                const extractedData: any = {
                    crm_prod_id: cfs.crm_prod_id,
                    cfsAccessCode: cfs.accs_nbr_no,
                    cust_name: cfs.cust_name,
                    customer_level_name: cfs.customer_level_name,
                    khjl_name: cfs.khjl_name,
                    khjl_phone: cfs.khjl_phone,
                    khjl_org_name: cfs.khjl_org_name,
                    khjl_post_type: cfs.khjl_post_type,
                    cfsName: cfs.service_name,
                    cfsSpec: cfs.service_spec_name,
                    scene_name: cfs.scene_name,
                    is_scene: !cfs.scene_name ? '否' : '是',

                };

                console.log('cfs.cust_tp_id',cfs.cust_tp_id);
                // 客户类型是3、6、9、的话表示公众的，别的都是政企
                if (
                    cfs.cust_tp_id == '7' ||
                    cfs.cust_tp_id == '6' ||
                    cfs.cust_tp_id == '9'
                ) 
                {
                    console.log('cfs.cust_tp_id',cfs.cust_tp_id);
                    console.log('分配到公众');

                    mergeDataBasedOnAccessCode(relatednormalData.value, extractedData);
                } else {
                    // 否则，认为是政企用户的服务类型

                    console.log('cfs.cust_tp_id',cfs.cust_tp_id);
                    console.log('分配到政企');
                    mergeDataBasedOnAccessCode(relatedEnterpriseData.value, extractedData);

                }
            });



            //汇总计算结果到map里面
            countServices(relatedEnterpriseData.value, govBizServiceMap);
            countServices(relatednormalData.value, publicUserServiceMap);

            function countServices(data, serviceMap) {
                // 使用一个对象来存储不同的cfsName及其出现的次数  

                // 遍历数组  
                data.forEach(item => {
                    const cfsName = item.cfsSpec;
                    // 如果该cfsName已经存在于serviceCounts中，则增加其计数  
                    if (serviceMap.has(cfsName)) {
                        serviceMap.set(cfsName, serviceMap.get(cfsName) + 1);
                    } else {
                        // 否则，初始化计数为1  
                        serviceMap.set(cfsName, 1);
                    }
                });

                // 返回统计结果  
                return serviceMap;
            }

        }

        const countDefinedSceneNames = (dataArray) => {
            return dataArray.value.filter((item) => item.scene_name !== undefined).length;
        };

        // 构建政企用户的描述字符串
        let govBizSummaryStr = `合计：${relatedEnterpriseData.value.length} 个业务  `;

        govBizSummaryStr += '详情如下：\n';
        govBizServiceMap.forEach((count, serviceName) => {
            govBizSummaryStr += `${serviceName}： ${count}个 \n`;
        });
        govBizSummaryStr += `其中，影响生命线业务：${countDefinedSceneNames(relatedEnterpriseData)} 个\n`;

        // 构建公众用户的描述字符串
        let publicUserSummaryStr = `合计：${relatednormalData.value.length} 个业务  `;
        publicUserSummaryStr += '详情如下：\n';
        publicUserServiceMap.forEach((count, serviceName) => {
            publicUserSummaryStr += `${serviceName}： ${count}个 \n`;
        });
        publicUserSummaryStr += `其中，影响生命线业务：${countDefinedSceneNames(relatednormalData)} 个\n`;

        // 赋值给响应式引用


    }

    function mergeDataBasedOnAccessCode(dataArray, newData) {
        // 查找是否存在相同的 cfsAccessCode
        const existingEntry = dataArray.find(
            (entry) => entry.cfsAccessCode === newData.cfsAccessCode,
        );

        if (MergeSwitch.value && existingEntry) {
            // 如果找到了相同的 cfsAccessCode，则合并字段
            for (let key in newData) {
                if (newData.hasOwnProperty(key) && key !== 'cfsAccessCode') {
                    let newValue = newData[key];
                    let existingValue = existingEntry[key];


                    
                    if (!existingValue) {
                        // 如果现有条目中没有这个字段，直接添加
                        existingEntry[key] = newValue;
                    } else if (!existingValue.includes(newValue)) {
                        // 如果现有字段值中不包含新值，则使用逗号合并
                        existingEntry[key] = `${existingValue},${newValue}`;
                    }
                    // 如果字段值已包含新值，则不做任何操作
                }
            }
        } else {

            const existingEntry1 = dataArray.find(
                (entry) => entry.cfsAccessCode === newData.cfsAccessCode&&entry.crm_prod_id === newData.crm_prod_id
            );
            if(!existingEntry1){
                dataArray.push(newData);
            }
            // 如果没有找到相同的 cfsAccessCode，则添加新的对象到数组中
            
        }
    }
    ActionLoading.value = false;
};



function exportTable() {
    // 创建工作簿  
    const wb = XLSX.utils.book_new();
    // 将表格数据转换为工作表  
    const ws = XLSX.utils.json_to_sheet(relatedEnterpriseData.value);
    const ws2 = XLSX.utils.json_to_sheet(relatednormalData.value);

    const relatedEnterpriseDataheaders = ['产品ID', '接入号', '客户名称', '客户服务等级', '客户经理名称', '客户经理电话', '客户经理支局','客户经理岗位','影响业务类型', '业务规格名称', '元凤客户视图名称', '是否生命线'];
    const relatednormalDataheaders = ['产品ID', '接入号', '客户名称', '客户服务等级', '客户经理名称', '客户经理电话','客户经理支局','客户经理岗位', '影响业务类型', '业务规格名称', '元凤客户视图名称', '是否生命线'];


    // 设置表头  
    for (let col = 0; col < relatedEnterpriseDataheaders.length; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col }); // 编码单元格地址，例如'A1'  
        ws[cellAddress] = { v: relatedEnterpriseDataheaders[col] }; // 设置单元格的值  
    }
    // 设置表头  
    for (let col = 0; col < relatednormalDataheaders.length; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col }); // 编码单元格地址，例如'A1'  
        ws2[cellAddress] = { v: relatednormalDataheaders[col] }; // 设置单元格的值  
    }





    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '政企');
    XLSX.utils.book_append_sheet(wb, ws2, '公众');

    // 如果有影响光路数据，也添加到工作簿
    if (influenceRouteData.value && influenceRouteData.value.length > 0) {
        const ws3 = XLSX.utils.json_to_sheet(influenceRouteData.value);
        const routeHeaders = ['光路编码', '光路名称', '光缆编码', '光缆名称', '纤芯号', '局向纤芯号', 'A端设备编码', 'A端设备名称', 'A端设备类型', 'A端端口编码', 'Z端设备编码', 'Z端设备名称', 'Z端设备类型', 'Z端端口编码'];

        // 设置影响光路表头
        for (let col = 0; col < routeHeaders.length; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
            ws3[cellAddress] = { v: routeHeaders[col] };
        }

        XLSX.utils.book_append_sheet(wb, ws3, '影响光路');
    }

    // 导出工作簿
    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    saveAs(new Blob([wbout], { type: 'application/octet-stream' }), '影响分析导出.xlsx');
}



const EnterpriseColumns = [
    {
        title: '接入号',
        dataIndex: 'cfsAccessCode',
        key: 'cfsAccessCode',
    },
    {
        title: 'crm产品ID',
        dataIndex: 'crm_prod_id',
        key: 'crm_prod_id',
    },

    {
        title: '客户名称',
        dataIndex: 'cust_name',
        key: 'cust_name',
        width: 200
    },
    {
        title: '客户等级',
        dataIndex: 'customer_level_name',
        key: 'customer_level_name',
    },
    {
        title: '影响业务类型',
        dataIndex: 'cfsSpec',
        key: 'cfsSpec',
    },
    {
        title: '客户经理',
        dataIndex: 'khjl_name',
        key: 'khjl_name',
    },
    {
        title: '客户经理电话',
        dataIndex: 'khjl_phone',
        key: 'khjl_phone',
    },
    {
        title: '客户经理支局',
        dataIndex: 'khjl_org_name',
        key: 'khjl_org_name',
    },
    {
        title: '客户经理岗位类型',
        dataIndex: 'khjl_post_type',
        key: 'khjl_post_type',
    },
    {
        title: '是否生命线',
        dataIndex: 'is_scene',
        key: 'is_scene',

        filters: [
            { text: '是', value: '是' },
            { text: '否', value: '否' },

        ],
        onFilter: (value: string, record) => record.is_scene.indexOf(value) === 0,
    },
];

const NormalColumns = [
    {
        title: '接入号',
        dataIndex: 'cfsAccessCode',
        key: 'cfsAccessCode',
    },
    {
        title: 'crm产品ID',
        dataIndex: 'crm_prod_id',
        key: 'crm_prod_id',
    },
    {
        title: '影响业务名称',
        dataIndex: 'cfsName',
        key: 'cfsName',
    },
    {
        title: '客户名称',
        dataIndex: 'cust_name',
        key: 'cust_name',
    },

    {
        title: '影响业务类型',
        dataIndex: 'cfsSpec',
        key: 'cfsSpec',
    },
    {
        title: '是否生命线',
        dataIndex: 'is_scene',
        key: 'is_scene',

        filters: [
            { text: '是', value: '是' },
            { text: '否', value: '否' },

        ],
        onFilter: (value: string, record) => record.is_scene.indexOf(value) === 0,
    },
];

const RouteColumns = [
    {
        title: '光路编码',
        dataIndex: 'route_code',
        key: 'route_code',
        width: 200
    },
    {
        title: '光路名称',
        dataIndex: 'route_name',
        key: 'route_name',
        width: 200
    },
    {
        title: '光缆编码',
        dataIndex: 'cable_code',
        key: 'cable_code',
        width: 200
    },
    {
        title: '光缆名称',
        dataIndex: 'cable_name',
        key: 'cable_name',
        width: 200
    },
    {
        title: '纤芯号',
        dataIndex: 'line_code',
        key: 'line_code',
        width: 100
    },
    {
        title: '局向纤芯号',
        dataIndex: 'jx_fiber_code',
        key: 'jx_fiber_code',
        width: 150
    },
    {
        title: 'A端设备编码',
        dataIndex: 'jx_a_device_code',
        key: 'jx_a_device_code',
        width: 150
    },
    {
        title: 'A端设备名称',
        dataIndex: 'jx_a_device_name',
        key: 'jx_a_device_name',
        width: 150
    },
    {
        title: 'A端设备类型',
        dataIndex: 'a_device_type',
        key: 'a_device_type',
        width: 150
    },
    {
        title: 'A端端口编码',
        dataIndex: 'jx_a_port_code',
        key: 'jx_a_port_code',
        width: 150
    },
    {
        title: 'Z端设备编码',
        dataIndex: 'jx_z_device_code',
        key: 'jx_z_device_code',
        width: 150
    },
    {
        title: 'Z端设备名称',
        dataIndex: 'jx_z_device_name',
        key: 'jx_z_device_name',
        width: 150
    },
    {
        title: 'Z端设备类型',
        dataIndex: 'z_device_type',
        key: 'z_device_type',
        width: 150
    },
    {
        title: 'Z端端口编码',
        dataIndex: 'jx_z_port_code',
        key: 'jx_z_port_code',
        width: 150
    },
];



</script>