<template>
  <page-wrapper>
    <a-card :bordered="false">
      <a-space>
        地市：<a-select
          v-model:value="filter.region_id"
          :filterOption="
            (input: string, option: any) => {
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }
          "
          :allowClear="true"
          show-search
          style="width: 120px"
        >
          <a-select-option
            v-for="item in dictionary.region2List"
            :key="item.id"
            :value="item.id"
            :label="item.name"
            >{{ item.name }}
          </a-select-option>
        </a-select>
        客户名称：<a-input v-model:value="filter.name" />
        电路编码：<a-input v-model:value="filter.circuit_code" style="width: 140px" />
        客户账号：<a-input v-model:value="filter.account" style="width: 140px" />
        是否生命线业务：<a-select
          v-model:value="filter.is_life"
          :filterOption="
            (input: string, option: any) => {
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }
          "
          :allowClear="true"
          show-search
          style="width: 140px"
        >
          <a-select-option
            v-for="item in is_life_options"
            :key="item.id"
            :value="item.id"
            :label="item.name"
            >{{ item.name }}
          </a-select-option>
        </a-select>
        <a-button type="primary" html-type="submit" @click="getList">查询</a-button>
        <a-button type="default" @click="showCreateNewDialog">新建</a-button>
      </a-space>
    </a-card>
    <a-card>
      <a-table
        :dataSource="dataSource"
        :columns="columns"
        :pagination="queryService.pagination"
        @change="queryService.pageChange"
        :rowKey="'id'"
        :loading="loading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'is_life'">
            <a-tag
              v-if="record.is_life === '0'"
              color="red"
              style="font-weight: bold; font-size: 14px;"
            >
              是
            </a-tag>
            <span v-else style="color: #666;">否</span>
          </template>
          <template v-if="column.key === 'action'">
            <a-button
              type="link"
              @click="
                () => {
                  showEditDialog(record);
                }
              "
              ><SettingOutlined />修改</a-button
            >
            <a-button
              type="link"
              @click="
                () => {
                  go('./lifeline-management/cust-view-show/' + record.id);
                }
              "
              ><SettingOutlined />电路纳管</a-button
            >
<!--            <a-button
              type="link"
              @click="
                () => {
                  go('./cust_view_circuit_analyze/' + record.id);
                }
              "
              ><SettingOutlined />风险检测</a-button
            >-->
            <a-button
              type="link"
              @click="
                () => {
                  createConfirm({
                    iconType: 'warning',
                    title: '请确认',
                    content: '删除后将无法恢复',
                    onOk: async () => {
                      await queryService.deleteById(record.id);
                    },
                  });
                }
              "
              ><SettingOutlined />删除</a-button
            >
          </template>
        </template>
      </a-table>
      <editDialog
        :info="current"
        v-model:show="editDialogVisible"
        @on-finish="getList"
      />
    </a-card>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { onActivated, ref, onMounted, watchEffect, defineOptions, watch } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import { SettingOutlined } from '@ant-design/icons-vue';
  // import moment from 'moment';
  // go 用于跳转页面
  import { useGo } from '@/hooks/web/usePage';
  // import { defHttp } from '@/utils/http/axios';
  import { useRouter } from 'vue-router';

  // 处理分页查询
  import { usePageQuery } from '@/hooks/web/useRestAPI';
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();
  // 处理确认删除
  import { useMessage } from '@/hooks/web/useMessage';
  // import { def } from '@vue/shared';
  // import { TableProps } from 'ant-design-vue/lib/table/Table';
  // import { Table } from 'ant-design-vue';
  import { default as editDialog } from './cust-view-new.vue';

  defineOptions({ name: 'cust-view-list' });

  const go = useGo();

  const router = useRouter();
  const id = router.currentRoute.value.params.id;
  console.log('id信息:', id);

  const queryService = usePageQuery({
    rootPath: '/graph-rest-api',
    queryUrl: '/api/custview',
    filter: {
      name: '生命线',
      region: '',
      id: id,
      circuit_code: '', //电路编码
      account: '',
      is_life: '0', // 默认查询生命线业务
    },
  });

  const { filter, loading, dictionary, dataSource } = queryService;
  const desensitization = () => {
    const userName = userStore.getUserInfo.realName;
    const nameList = userStore.getLookUsers;
    if (!nameList.includes(userName)) {
      dataSource.value.forEach((item: any) => {
        item.name = desensitizeString(item.name);
      });
    }
  };
  onMounted(async () => {
    await queryService.queryDictionary();
    await getList();
  });
  onActivated(async () => {
    await queryService.queryDictionary();
    await getList();
  });
  watch(
    dataSource,
    () => {
      desensitization();
    },
    { deep: true },
  );
  watchEffect(async () => {
    // console.log( props.custViewInfo );
  });
  const getList = async () => {
    await queryService.pageQuery();
    desensitization();
  };
  const desensitizeString = (str: string) => {
    if (!str) {
      return str;
    }
    const firstChar = str[0];
    const remainingLength = str.length - 1;
    const stars = '*'.repeat(remainingLength);
    return firstChar + stars;
  };
  interface DataType {
    id: string;
    key: string;
    area_name: string;
    service_res_type_name: string;
    cus_name: string;
    acc_code: string;
    name: string;
    is_life: string;
  }

  // 是否生命线业务选项
  const is_life_options = [
    { id: '0', name: '是' },
    { id: '1', name: '否' },
  ];

  const columns = ref([
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '地市',
      dataIndex: 'region',
      key: 'region',
    },
    {
      title: '客户名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '客户账号',
      dataIndex: 'account',
      key: 'account',
    },
    {
      title: '是否生命线业务',
      dataIndex: 'is_life',
      key: 'is_life',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
    },
  ]);

  const { createConfirm } = useMessage();

  /*
  const param = {};
  const infoService = useInfo({
    rootPath: '/graph-rest-api',
    info: param,
    responseType: 'blob',
  });

  const { info } = infoService;
  const download = async (selectedRowKeys: any) => {
    //selectedRowKeys.value
    const result = await infoService.doDownload(`/api/custlinkapi/download`);
    console.log('result:', result, result.data);
    const blob = new Blob([result], { type: 'application/vnd.ms-excel' });
    let fileName = 'list' + '.xlsx';
    let a = document.createElement('a');
    a.download = fileName;
    a.href = window.URL.createObjectURL(blob);
    a.click();
    a.remove();
  };

   */
  const editDialogVisible = ref(false);
  const current = ref<any>({});
  const showCreateNewDialog = () => {
    current.value = {
      id: '',
      name: '生命线',
      is_life: '0', // 默认为生命线业务
    };
    editDialogVisible.value = true;
  };

  const showEditDialog = (record: any) => {
    current.value = record;
    editDialogVisible.value = true;
  };
</script>
