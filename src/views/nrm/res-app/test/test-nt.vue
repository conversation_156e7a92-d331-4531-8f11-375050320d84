<script setup lang="ts">
  import { usePermission } from '@/hooks/web/usePermission';
  import { useUserStore } from '@/store/modules/user';
  import { computed } from 'vue';

  const { hasPermission } = usePermission();
  const store = useUserStore();

  // 常见角色检查
  const commonRoles = [
    '测试按钮',
    '障碍定位（菜单）',
    '南通开发角色',
    '系统管理员',
    '普通用户'
  ];

  const roleCheckResults = computed(() => {
    return commonRoles.map(role => ({
      role,
      hasRole: hasPermission(role)
    }));
  });
</script>

<template>
  <div style="padding: 20px;">
    <a-card title="南通权限测试页面" style="margin-bottom: 16px;">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="当前用户地市代码">
          {{ store.getAreaCode }}
        </a-descriptions-item>
        <a-descriptions-item label="当前用户地市名称">
          {{ store.getAreaName }}
        </a-descriptions-item>
        <a-descriptions-item label="当前用户角色列表" :span="2">
          <a-tag v-for="role in store.getRoleList" :key="role" color="blue" style="margin: 2px;">
            {{ role }}
          </a-tag>
          <span v-if="!store.getRoleList || store.getRoleList.length === 0" style="color: #999;">
            暂无角色
          </span>
        </a-descriptions-item>
        <a-descriptions-item label="用户信息" :span="2">
          <pre>{{ JSON.stringify(store.getUserInfo, null, 2) }}</pre>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>

    <a-card title="权限测试按钮">
      <a-space wrap>
        <a-button v-auth="'测试按钮'" type="primary">
          拥有 ['测试按钮']权限可见
        </a-button>
        <a-button :disabled="!hasPermission('测试按钮')">
          需要权限的按钮
        </a-button>
        <a-button v-auth="'障碍定位（菜单）'" type="primary">
          障碍定位权限可见
        </a-button>
        <a-button v-auth="'常州开发角色'" type="primary">
          常州开发角色可见
        </a-button>
        <a-button v-auth="'南通开发角色'" type="primary">
          南通开发角色可见
        </a-button>
      </a-space>
    </a-card>

    <a-card title="角色检查结果" style="margin-top: 16px;">
      <a-list :data-source="roleCheckResults" size="small">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #title>
                {{ item.role }}
              </template>
              <template #description>
                <a-tag :color="item.hasRole ? 'green' : 'red'">
                  {{ item.hasRole ? '✅ 拥有' : '❌ 没有' }}
                </a-tag>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-card>
  </div>
</template>

<style scoped lang="less"></style>
