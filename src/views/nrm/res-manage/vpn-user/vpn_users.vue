<template>
  <page-wrapper title="专线用户资料">
    <a-card :bordered="false">
      <a-form :model="filter" name="horizontal_login" layout="inline" autocomplete="off"
        @finish="queryService.pageQuery" :labelCol="{ span: 10 }">
        <a-form-item name="area_code" label="区域" :rules="[{ message: '请选择区域', required: false }]">
          <a-select style="width: 100px" v-model:value="filter.area_code" :disabled="areaCodeFlag" :allowClear="true"
            show-search>
            <a-select-option v-for="item in dictionary.region2List" :key="item.id" :value="item.id"
              :label="item.name">{{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="acc_num" label="接入号">
          <a-input v-model:value="filter.acc_num" />
        </a-form-item>
        <a-form-item name="net_type_id" label="新老城">
          <a-select style="width: 100px" v-model:value="filter.net_type_id" :allowClear="true" placeholder="请选择">
            <a-select-option v-for="item in dictionary.manNetTypes" :key="item.id" :value="item.id"
              :label="item.name">{{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="pe_ip" label="PE设备IP">
          <a-input v-model:value="filter.pe_ip" />
        </a-form-item>
        <a-form-item name="pe_port_code" label="设备端口编码">
          <a-input v-model:value="filter.pe_port_code" />
        </a-form-item>
        <a-form-item name="service_id" label="产品服务定单ID">
          <a-input v-model:value="filter.service_id" />
        </a-form-item>
        <a-form-item name="prod_id" label="产品实例ID">
          <a-input v-model:value="filter.prod_id" />
        </a-form-item>
        <a-form-item name="vpn_instance_id" label="VPN实例">
          <a-input v-model:value="filter.vpn_instance_id" />
        </a-form-item>
        <a-form-item name="user_gateway_ip" label="网关IP">
          <a-input v-model:value="filter.user_gateway_ip" />
        </a-form-item>
        <!-- <a-form-item name="accessDeviceIp" label="接入设备IP">
          <a-input v-model:value="filter.accessDeviceIp" />
        </a-form-item>
        <a-form-item name="convergeDeviceIp" label="汇聚设备IP">
          <a-input v-model:value="filter.convergeDeviceIp" />
        </a-form-item> -->
        <!-- <a-form-item name="userState" label="用户状态">
          <a-input v-model:value="filter.userState" />
        </a-form-item>
        <a-form-item name="brasPortType" label="bras端口类型">
          <a-input v-model:value="filter.brasPortType" />
        </a-form-item> -->
        <a-form-item name="svlan" label="SVLAN">
          <a-input v-model:value.trim="filter.svlan" />
        </a-form-item>
        <a-form-item name="cvlan" label="CVLAN">
          <a-input v-model:value.trim="filter.cvlan" />
        </a-form-item>
        <a-form-item name="olt_ip" label="olt_ip">
          <a-input v-model:value.trim="filter.olt_ip" />
        </a-form-item>
        <!-- <a-form-item name="prodType" label="用户类型">
          <a-input v-model:value="filter.prodType" />
        </a-form-item> -->
        <!-- <a-form-item name="isInternet" label="是否公网专线">
          <a-input v-model:value="filter.isInternet" />
        </a-form-item> -->
        <!-- <a-form-item name="loid" label="loid">
          <a-input v-model:value="filter.loid" />
        </a-form-item> -->
      </a-form>

      <a-space style="float: right">
        <a-button type="primary" html-type="submit" @click="queryService.pageQuery">查询</a-button>
        <a-button @click="showCreateNewDialog">新建</a-button>
        <a-button :loading="downloading" @click="
          () => {
            download(1);
          }
        ">查询结果导出</a-button>
        <!-- <a-button
          :loading="downloading"
          @click="
            () => {
              download(2);
            }
          "
          >批量导出</a-button
        > -->
        <a-button @click="openExport(1)">批量导入</a-button>
        <a-button @click="openExport(2)">批量修改</a-button>
        <a-button @click="openExport(3)">批量导出</a-button>
      </a-space>
    </a-card>

    <a-card>
      <a-table :dataSource="dataSource" :columns="columns" :scroll="{ x: 2000 }" :pagination="queryService.pagination"
        @change="queryService.pageChange" :rowKey="'id'" :loading="loading" @resizeColumn="handleResizeColumn">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="
              () => {
                showEditDialog(record);
              }
            ">
              <SettingOutlined />修改
            </a-button>
            <a-button type="link" @click="
              () => {
                createConfirm({
                  iconType: 'warning',
                  title: '请确认',
                  content: '删除后将无法恢复',
                  onOk: async () => {
                    await queryService.deleteById(record.id);
                  },
                });
              }
            ">
              <SettingOutlined />删除
            </a-button>
          </template>
        </template>
      </a-table>
      <editDialog :info="current" v-model:show="editDialogVisible" @on-finish="queryService.pageQuery" />
    </a-card>
    <a-modal v-model:visible="showExport" :width="500" :title="modalTitle">
      <template #footer>
        <!-- <a-button key="back" @click="onClose">关闭</a-button>
        <a-button key="onSubmit" type="primary" @click="onSave">保存</a-button> -->
      </template>
      <div>
        <a-button type="link" @click="down">模版下载</a-button>
        <a-upload v-if="modalTitle == '批量导出'" v-model:file-list="fileList" accept=".xlsx,.xls" :customRequest="customRequest"
          :showUploadList="false">
          <a-button type="primary" style="margin-top:20px">导入文件</a-button>
        </a-upload>
        <a-upload v-else v-model:file-list="fileList" accept=".xlsx,.xls" name="file" method="post" :action="actionUrl"
          :maxCount="1" @change="handleChange" :headers="headers">
          <a-button type="primary" style="margin-top:20px">导入文件</a-button>
        </a-upload>
      </div>
    </a-modal>
  </page-wrapper>
</template>
<script lang="ts"></script>
<script lang="ts" setup>
import { ref, onMounted, onActivated } from 'vue';
import { PageWrapper } from '@/components/Page';
// go 用于跳转页面
// import { useGo } from '@/hooks/web/usePage';

// 处理分页查询
import { usePageQuery, useInfo } from '@/hooks/web/useRestAPI';

// 处理确认删除
import { useMessage } from '@/hooks/web/useMessage';
import regionArr from '@/assets/regionArr.json';
import { default as editDialog } from './vpn_user_edit.vue';

import axios from 'axios';

// const go = useGo();
import { useUserStore } from '@/store/modules/user';
const areaCodeFlag = ref(false);
const areaCode = ref<any>('321122930000000000000006');
const queryService = usePageQuery({
  rootPath: '/gather-rest-api',
  queryUrl: '/api/vpn_user',
  filter: {
    area_code: areaCode
  },
});
import type { UploadChangeParam } from 'ant-design-vue';
import { message } from 'ant-design-vue';
const { createMessage } = useMessage();
const { dataSource, filter, loading, dictionary } = queryService;
onMounted(async () => {
  getLocalCode();
  await queryService.pageQuery();
  await queryService.queryDictionary();
});
onActivated(async () => {
  getLocalCode();
  await queryService.pageQuery();
  await queryService.queryDictionary();
});
const userStore = useUserStore();
const getLocalCode = () => {
  regionArr?.forEach((item) => {
    if (userStore.getAreaCode == item.key) {
      areaCodeFlag.value = true;
      areaCode.value = item.value;
    }
  });
};
const infoService = useInfo({
  rootPath: '/gather-rest-api',
  responseType: 'blob',
});
const token = userStore.getToken;
const headers = ref(
  {
    Authorization: token,
  }
);
const fileList = ref([]);
const downloading = ref(false)
const modalTitle = ref('批量导入')
const showExport = ref(false);
let actionUrl = ref('');    //导入url

const columns = ref([
  {
    title: '区域',
    dataIndex: 'region_name',
    key: 'region_name',
    width: 120,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '接入号',
    dataIndex: 'acc_num',
    key: 'acc_num',
    width: 120,
    resizable: true,
    ellipsis: true,
  },
  {
    title: 'VPN实例',
    dataIndex: 'vpn_instance_id',
    key: 'vpn_instance_id',
    width: 120,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '网关IP',
    dataIndex: 'user_gateway_ip',
    key: 'user_gateway_ip',
    width: 140,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '网关ip掩码',
    dataIndex: 'user_ip_mask',
    key: 'user_ip_mask',
    width: 140,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '设备IP',
    dataIndex: 'pe_ip',
    key: 'pe_ip',
    width: 140,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '设备端口编码',
    dataIndex: 'pe_port_code',
    key: 'pe_port_code',
    width: 140,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '子接口号',
    dataIndex: 'pe_sub_port_code',
    key: 'pe_sub_port_code',
    width: 120,
    resizable: true,
    ellipsis: true,
  },

  {
    title: 'VLAN ID',
    dataIndex: 'vlan_id',
    key: 'vlan_id',
    width: 120,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '产品实例ID',
    dataIndex: 'prod_id',
    key: 'prod_id',
    width: 140,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '新老城',
    dataIndex: 'net_type_name',
    key: 'net_type_name',
    width: 80,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '上行速率',
    dataIndex: 'up_rate',
    key: 'up_rate',
    width: 110,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '下行速率',
    dataIndex: 'down_rate',
    key: 'down_rate',
    width: 110,
    resizable: true,
    ellipsis: true,
  },
  {
    title: 'interface',
    dataIndex: 'sap',
    key: 'sap',
    width: 110,
    resizable: true,
    ellipsis: true,
  },
  {
    title: 'ies',
    dataIndex: 'ies',
    key: 'IES',
    width: 110,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '单双层VLAN标识',
    dataIndex: 'single_or_double_desc',
    key: 'single_or_double_desc',
    width: 140,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '多网关标识',
    dataIndex: 'multi_gateway_desc',
    key: 'multi_gateway_desc',
    width: 140,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '流出流量策略模板',
    dataIndex: 'policyoutbound',
    key: 'policyoutbound',
    width: 140,
    resizable: true,
    ellipsis: true,
  },
  {
    title: 'SVLAN',
    dataIndex: 'svlan',
    key: 'svlan',
    width: 110,
    resizable: true,
    ellipsis: true,
  },
  {
    title: 'CVLAN',
    dataIndex: 'cvlan',
    key: 'cvlan',
    width: 110,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 180,
  },
]);

const { createConfirm } = useMessage();

const editDialogVisible = ref(false);
const current = ref<any>({});
const showEditDialog = (record: any) => {
  current.value = record;
  editDialogVisible.value = true;
};
const showCreateNewDialog = () => {
  current.value = {
    isValid: 1001049,
  };
  editDialogVisible.value = true;
};

//下载模板
const down = async () => {
  let downUrl = '';
  let tempFileName = '';
  if (modalTitle.value == '批量导入') {
    downUrl = `/api/vpn_user/downloadAddExcel`;
    tempFileName = '批量导入模板';
  } else if (modalTitle.value == '批量修改') {
    downUrl = `/api/vpn_user/downloadUpdateExcel`;
    tempFileName = '批量修改模板';
  } else if (modalTitle.value == '批量导出') {
    downUrl = `/api/vpn_user/downloadQueryExcel`;
    tempFileName = '批量导出模板';
  }
  let result = await infoService.doDownload(downUrl);

  console.log('result1:', result, result.data);
  const blob = new Blob([result], { type: 'application/vnd.ms-excel' });
  let fileName = tempFileName + '.xlsx';
  let a = document.createElement('a');
  a.download = fileName;
  a.href = window.URL.createObjectURL(blob);
  a.click();
  a.remove();
}

const handleChange = (info: UploadChangeParam) => {
  if (info.file.status !== 'uploading') {
    console.log('导入-->', info.file, info.fileList);
  }
  if (info.file.status === 'done') {
    createMessage.success(`${info.file.name} 导入成功`);
    showExport.value = false;
    queryService.pageQuery();
  } else if (info.file.status === 'error') {
    createMessage.error(`${info.file.name} file upload failed.`);
  }
};

//自定义上传
const customRequest = async ({ file, onSuccess, onError }) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await axios.post(actionUrl.value, formData, {
      headers: headers.value,
      responseType: 'blob' // 关键：指定响应类型为blob
    });

    // 处理响应的 Blob 数据
    const blob = response.data;
    console.log('Received Blob:', blob);

    let tempFileName = '专线用户资料';

    let fileName = tempFileName + '.xlsx';
    let a = document.createElement('a');
    a.download = fileName;
    a.href = window.URL.createObjectURL(blob);
    a.click();
    a.remove();

    createMessage.success(`专线用户资料导出成功`);
    showExport.value = false;

  } catch (error) {
    console.error('Upload error:', error);
    createMessage.error('上传失败');
  }

};


const openExport = async (val) => {
  if (val == 1) {
    modalTitle.value = '批量导入';
    actionUrl.value = '/gather-rest-api/api/vpn_user/importAdd';
  } else if (val == 2) {
    modalTitle.value = '批量修改';
    actionUrl.value = '/gather-rest-api/api/vpn_user/importUpdate';
  } else if (val == 3) {
    modalTitle.value = '批量导出';
    actionUrl.value = '/gather-rest-api/api/vpn_user/importQuery';
  }
  fileList.value = []
  showExport.value = true;
};

function handleResizeColumn(w, col) {
  col.width = w;
}
const downloadinfoService = useInfo({
  rootPath: '/gather-rest-api',
  info: {},
  responseType: 'blob',
});
const download = async (val) => {
  //selectedRowKeys.value
  if (val == 1 && !filter.value.area_code) return createMessage.error(`请选择地市`);
  downloading.value = true
  downloadinfoService.info.value = val == 2 ? { area_code: filter.value.area_code } : { ...filter.value, page: queryService.pagination.value.current, size: queryService.pagination.value.pageSize };
  // console.log("fileter_value.ds", filter,ds, downloadinfoService.info)
  const result = await downloadinfoService.doDownload(val == 2 ? `/api/vpn_user/exportVpnUserInfoByRegion` : '/api/vpn_user/exportVpnUserInfo');
  // console.log('result:', result);
  downloading.value = false
  const blob = new Blob([result], { type: 'application/vnd.ms-excel' });
  let fileName = val == 2 ? '批量导出' : '查询结果' + '.xlsx';
  let a = document.createElement('a');
  a.download = fileName;
  a.href = window.URL.createObjectURL(blob);
  a.click();
  a.remove();
};
</script>
