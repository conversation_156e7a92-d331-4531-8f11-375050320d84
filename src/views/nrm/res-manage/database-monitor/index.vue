<template>
  <PageWrapper dense contentFullHeight fixedHeight contentClass="flex">
    <div class="database-monitor-container">
      <!-- 页面标题和操作按钮 -->
      <div class="header-section">
        <div class="title-section">
          <h2>数据库监控</h2>
          <p class="subtitle">监控所有数据库分片的连接状态和性能指标</p>
        </div>
        <div class="action-section">
          <a-button type="primary" @click="refreshData" :loading="loading">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新数据
          </a-button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="stats-section">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card class="stat-card">
              <a-statistic
                title="总分片数"
                :value="stats.totalShards"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="stat-card">
              <a-statistic
                title="总活动连接数"
                :value="stats.totalActiveConnections"
                :value-style="{ color: '#52c41a' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="stat-card">
              <a-statistic
                title="总空闲连接数"
                :value="stats.totalIdleConnections"
                :value-style="{ color: '#722ed1' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="stat-card">
              <a-statistic
                title="总最大连接数"
                :value="stats.totalMaxConnections"
                :value-style="{ color: '#fa8c16' }"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="false"
          :scroll="{ x: 1200, y: 'calc(100vh - 400px)' }"
          row-key="shardingCode"
          size="middle"
        >
          <!-- 分片代码列 -->
          <template #shardingCode="{ record }">
            <a-tag color="blue">{{ record.shardingCode }}</a-tag>
          </template>

          <!-- 使用率列 -->
          <template #usage="{ record }">
            <div class="usage-info">
              <a-progress
                :percent="getUsagePercent(record)"
                :status="getUsageStatus(record)"
                size="small"
              />
              <span class="usage-text">
                {{ record.activeConnections }}/{{ record.maxPoolSize }}
              </span>
            </div>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button
                type="link"
                size="small"
                @click="refreshConnectionPool(record.shardingCode)"
                :loading="refreshingPools.includes(record.shardingCode)"
              >
                刷新连接池
              </a-button>
              <a-button type="link" size="small" @click="viewDetails(record)">
                查看详情
              </a-button>
            </a-space>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="数据库分片详情"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedRecord" class="detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="分片代码">
            {{ selectedRecord.shardingCode }}
          </a-descriptions-item>
          <a-descriptions-item label="活动连接数">
            {{ selectedRecord.activeConnections }}
          </a-descriptions-item>
          <a-descriptions-item label="空闲连接数">
            {{ selectedRecord.idleConnections }}
          </a-descriptions-item>
          <a-descriptions-item label="最大连接数">
            {{ selectedRecord.maxPoolSize }}
          </a-descriptions-item>
          <a-descriptions-item label="等待连接线程数">
            {{ selectedRecord.threadsAwaitingConnection }}
          </a-descriptions-item>
          <a-descriptions-item label="使用率">
            <a-progress
              :percent="getUsagePercent(selectedRecord)"
              :status="getUsageStatus(selectedRecord)"
            />
            <span style="margin-left: 8px;">
              {{ getUsagePercent(selectedRecord) }}% ({{ selectedRecord.activeConnections }}/{{ selectedRecord.maxPoolSize }})
            </span>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </PageWrapper>
</template>

<script setup lang="ts">
// @ts-nocheck
import { ref, reactive, onMounted, computed } from 'vue';
import { PageWrapper } from '@/components/Page';
import { ReloadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { validateAllSharding, refreshConnectionPool as refreshPool, type DatabaseShardingInfo } from '@/api/database-monitor';

defineOptions({
  name: 'DatabaseMonitor',
});

// 消息提示功能已通过 import { message } from 'ant-design-vue' 导入

// 响应式数据
const loading = ref(false);
const dataSource = ref<DatabaseShardingInfo[]>([]);
const refreshingPools = ref<string[]>([]);
const detailModalVisible = ref(false);
const selectedRecord = ref<DatabaseShardingInfo | null>(null);

// 统计信息
const stats = computed(() => {
  const total = dataSource.value.length;
  const totalActiveConnections = dataSource.value.reduce((sum, item) => {
    const activeConnections = Number(item.activeConnections) || 0;
    return sum + activeConnections;
  }, 0);
  const totalIdleConnections = dataSource.value.reduce((sum, item) => {
    const idleConnections = Number(item.idleConnections) || 0;
    return sum + idleConnections;
  }, 0);
  const totalMaxConnections = dataSource.value.reduce((sum, item) => {
    const maxPoolSize = Number(item.maxPoolSize) || 0;
    return sum + maxPoolSize;
  }, 0);

  return {
    totalShards: total,
    totalActiveConnections,
    totalIdleConnections,
    totalMaxConnections
  };
});

// 移除分页配置，不再需要分页

// 表格列配置
const columns = [
  {
    title: '分片代码',
    dataIndex: 'shardingCode',
    key: 'shardingCode',
    width: 180,
    slots: { customRender: 'shardingCode' },
  },
  {
    title: '活动连接数',
    dataIndex: 'activeConnections',
    key: 'activeConnections',
    width: 120,
    align: 'center',
  },
  {
    title: '空闲连接数',
    dataIndex: 'idleConnections',
    key: 'idleConnections',
    width: 120,
    align: 'center',
  },
  {
    title: '最大连接数',
    dataIndex: 'maxPoolSize',
    key: 'maxPoolSize',
    width: 120,
    align: 'center',
  },
  {
    title: '等待连接线程数',
    dataIndex: 'threadsAwaitingConnection',
    key: 'threadsAwaitingConnection',
    width: 140,
    align: 'center',
  },
  {
    title: '使用率',
    key: 'usage',
    width: 120,
    align: 'center',
    slots: { customRender: 'usage' },
  },
  {
    title: '操作',
    key: 'action',
    width: 160,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];

// 获取使用率百分比 (活动连接数/最大连接数)
const getUsagePercent = (record: DatabaseShardingInfo) => {
  const maxPoolSize = Number(record.maxPoolSize) || 0;
  const activeConnections = Number(record.activeConnections) || 0;
  if (maxPoolSize === 0) return 0;
  return Math.round((activeConnections / maxPoolSize) * 100);
};

// 获取使用率状态
const getUsageStatus = (record: DatabaseShardingInfo) => {
  const percent = getUsagePercent(record);
  if (percent >= 90) return 'exception';
  if (percent >= 70) return 'active';
  return 'success';
};

// 刷新数据
const refreshData = async () => {
  loading.value = true;
  try {
    const response = await validateAllSharding();
    if (response.resultCode === '0000') {
      dataSource.value = response.response;
      message.success('数据刷新成功');
    } else {
      message.error('获取数据失败');
    }
  } catch (error) {
    console.error('获取数据库监控数据失败:', error);
    message.error('获取数据失败，请检查网络连接');
  } finally {
    loading.value = false;
  }
};

// 删除了测试连接功能

// 刷新连接池
const refreshConnectionPool = async (shardingCode: string) => {
  refreshingPools.value.push(shardingCode);
  try {
    await refreshPool(shardingCode);
    message.success(`连接池 ${shardingCode} 刷新成功`);
    // 刷新数据
    await refreshData();
  } catch (error) {
    console.error('刷新连接池失败:', error);
    message.error(`连接池 ${shardingCode} 刷新失败`);
  } finally {
    refreshingPools.value = refreshingPools.value.filter(code => code !== shardingCode);
  }
};

// 查看详情
const viewDetails = (record: DatabaseShardingInfo) => {
  selectedRecord.value = record;
  detailModalVisible.value = true;
};

// 组件挂载时获取数据
onMounted(() => {
  refreshData();
});
</script>

<style scoped lang="less">
.database-monitor-container {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  
  .title-section {
    h2 {
      margin: 0;
      color: #262626;
      font-size: 24px;
      font-weight: 600;
    }
    
    .subtitle {
      margin: 4px 0 0 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

.stats-section {
  margin-bottom: 24px;
  
  .stat-card {
    text-align: center;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.table-section {
  flex: 1;
  overflow: hidden;

  .ant-table-wrapper {
    height: 100%;
  }

  .ant-table-container {
    height: 100%;
  }

  .usage-info {
    .usage-text {
      display: block;
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 4px;
    }
  }
}

.detail-content {
  .ant-descriptions-item-label {
    font-weight: 500;
  }
}
</style>
