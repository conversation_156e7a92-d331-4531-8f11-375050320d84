# 数据库监控功能

## 功能概述

数据库监控功能用于监控所有数据库分片的连接状态和性能指标，提供实时的数据库连接池信息。

## 功能特性

### 1. 实时监控
- 显示所有数据库分片的连接状态
- 监控活动连接数、空闲连接数、总连接数
- 显示连接池使用率和最大连接池大小
- 监控等待连接的线程数

### 2. 统计信息
- 总分片数统计
- 正常/异常分片数统计
- 总连接数统计
- 实时状态更新

### 3. 操作功能
- 刷新数据：获取最新的数据库状态
- 刷新连接池：重置指定分片的连接池
- 查看详情：显示分片的详细信息
- 测试连接：使用模拟数据测试功能

### 4. 界面特性
- 响应式设计，支持不同屏幕尺寸
- 表格分页显示
- 状态颜色标识（正常/异常）
- 连接池使用率进度条显示

## API接口

### 获取所有数据库分片验证信息
```
GET /daas-dev-api/database/validate-all-sharding
```

**响应格式：**
```json
{
  "response": [
    {
      "idleConnections": 10,
      "targetDataSourceType": "HikariDataSource",
      "activeConnections": 0,
      "shardingCode": "ds_network_cz",
      "totalConnections": 10,
      "threadsAwaitingConnection": 0,
      "isValid": true,
      "validateMessage": "连接正常",
      "maxPoolSize": 10,
      "dataSourceType": "DataSourceProxy"
    }
  ],
  "resultCode": "0000"
}
```

### 刷新数据库连接池
```
POST /daas-dev-api/database/refresh-connection-pool
```

**请求参数：**
```json
{
  "shardingCode": "ds_network_cz"
}
```

## 使用说明

### 1. 访问页面
在资源管理菜单下找到"数据库监控"选项，点击进入监控页面。

### 2. 查看监控数据
- 页面顶部显示统计卡片，包含总分片数、正常分片数、异常分片数和总连接数
- 下方表格显示每个分片的详细信息

### 3. 操作说明
- **刷新数据**：点击右上角的"刷新数据"按钮获取最新状态
- **测试连接**：点击"测试连接"按钮使用模拟数据测试功能
- **刷新连接池**：在表格操作列中点击"刷新连接池"重置指定分片的连接池
- **查看详情**：在表格操作列中点击"查看详情"查看分片的完整信息

### 4. 状态说明
- **绿色徽章**：连接正常
- **红色徽章**：连接异常
- **进度条颜色**：
  - 绿色：使用率正常（< 70%）
  - 橙色：使用率较高（70% - 90%）
  - 红色：使用率过高（≥ 90%）

## 技术实现

### 前端技术栈
- Vue 3 + TypeScript
- Ant Design Vue
- Vite

### 文件结构
```
src/views/nrm/res-manage/database-monitor/
├── index.vue          # 主页面组件
└── README.md          # 说明文档

src/api/
└── database-monitor.ts # API调用函数
```

### 主要组件
- `PageWrapper`: 页面包装器
- `a-table`: 数据表格
- `a-statistic`: 统计卡片
- `a-modal`: 详情弹窗
- `a-progress`: 使用率进度条

## 注意事项

1. 确保后端API服务正常运行
2. 检查网络连接和API权限
3. 定期刷新数据以获取最新状态
4. 关注连接池使用率，及时处理异常情况

## 故障排除

### 常见问题
1. **数据加载失败**
   - 检查网络连接
   - 确认API服务状态
   - 查看浏览器控制台错误信息

2. **刷新连接池失败**
   - 确认分片代码正确
   - 检查API权限
   - 联系系统管理员

3. **页面显示异常**
   - 刷新浏览器页面
   - 清除浏览器缓存
   - 检查浏览器兼容性
