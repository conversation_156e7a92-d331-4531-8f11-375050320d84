<template>
  <div class="dashboard-container">
    <!-- 背景装饰元素 -->
    <div class="tech-background"></div>
    <div class="tech-overlay"></div>
    <div class="tech-particles"></div>
    <div class="tech-circuit"></div>
    <div class="tech-grid"></div>
    <!-- 主内容区 -->
    <main class="dashboard-content">

      <!-- 分光器端口信息 -->
      <div class="dashboard-card optical-splitter">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M3 2h6v20H3z" />
              <path d="M9 5h6v17H9z" />
              <path d="M15 8h6v14h-6z" />
              <path d="M21 11v2" />
              <path d="M17 8v2" />
              <path d="M13 5v2" />
            </svg>
            <span>分光器端口数量</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <optical-splitter-chart />
          </div>
        </div>
      </div>

      <!-- 电杆 + 人手井数量 -->
      <div class="dashboard-card electric-pole">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M3 2h6v20H3z" />
              <path d="M9 5h6v17H9z" />
              <path d="M15 8h6v14h-6z" />
              <path d="M21 11v2" />
              <path d="M17 8v2" />
              <path d="M13 5v2" />
            </svg>
            <span>电杆 + 人手井数量</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <electric-pole-and-hand-well-chart />
          </div>
        </div>
      </div>

      <!-- 光交数量 -->
      <div class="dashboard-card optical-crossing">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M3 2h6v20H3z" />
              <path d="M9 5h6v17H9z" />
              <path d="M15 8h6v14h-6z" />
              <path d="M21 11v2" />
              <path d="M17 8v2" />
              <path d="M13 5v2" />
            </svg>
            <span>光交数量</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <optical-crossing-chart />
          </div>
        </div>
      </div>

      <!-- 线路覆盖 -->
      <div class="dashboard-card line-coverage">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
            <span>线路覆盖</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="relative h-full w-full rounded-lg overflow-hidden">
            <line-coverage-map />
          </div>
        </div>
      </div>

      <!-- 干线信息 -->
      <div class="dashboard-card trunk-line">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M2 12h5" />
              <path d="M17 12h5" />
              <path d="M7 12a5 5 0 0 1 5-5" />
              <path d="M12 7a5 5 0 0 1 5 5" />
              <path d="M12 17a5 5 0 0 1-5-5" />
              <path d="M12 17a5 5 0 0 0 5-5" />
            </svg>
            <span>干线信息</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <trunk-line-chart />
          </div>
        </div>
      </div>

      <!-- 主干光缆信息 -->
      <div class="dashboard-card backbone-cable">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M2 12h5" />
              <path d="M17 12h5" />
              <path d="M7 12a5 5 0 0 1 5-5" />
              <path d="M12 7a5 5 0 0 1 5 5" />
              <path d="M12 17a5 5 0 0 1-5-5" />
              <path d="M12 17a5 5 0 0 0 5-5" />
            </svg>
            <span>主干光缆皮长公里数</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <backbone-cable-chart />
          </div>
        </div>
      </div>

      <!-- 中继光缆信息 -->
      <div class="dashboard-card relay-cable">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M2 12h5" />
              <path d="M17 12h5" />
              <path d="M7 12a5 5 0 0 1 5-5" />
              <path d="M12 7a5 5 0 0 1 5 5" />
              <path d="M12 17a5 5 0 0 1-5-5" />
              <path d="M12 17a5 5 0 0 0 5-5" />
            </svg>
            <span>中继光缆条数、皮长公里数</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <relay-cable-chart />
          </div>
        </div>
      </div>

    </main>
  </div>
</template>

<script setup>
import LineCoverageMap from './line-charts/LineCoverageMap.vue';
import OpticalSplitterChart from './line-charts/OpticalSplitterChart.vue';
import RelayCableChart from './line-charts/RelayCableChart.vue';
import ElectricPoleAndHandWellChart from './line-charts/ElectricPoleAndHandWellChart.vue';
import OpticalCrossingChart from './line-charts/OpticalCrossingChart.vue';
import TrunkLineChart from './line-charts/TrunkLineChart.vue';
import BackboneCableChart from './line-charts/BackboneCableChart.vue';

defineOptions({
  name: 'line-dashboard'
});

</script>
<style scoped>
/* 主内容区 */
.dashboard-content {
  display: grid;
  grid-template-columns: repeat(15, 1fr);
  grid-template-rows: repeat(9, 1fr);
  height: calc(100vh - 85px);
  width: 100%;
  padding: 4px 14px 8px 4px;
  gap: 6px;
  position: relative;
  z-index: 10;
}

/* 图表位置和大小 */
/* 分光器端口利用率 */
.optical-splitter {
  grid-column: 1 / 4;
  grid-row: 1 / 4;
}

/* 电杆 + 人手井数量 */
.electric-pole {
  grid-column: 1 / 4;
  grid-row: 4 / 7;
}

/* 光交数量 */
.optical-crossing {
  grid-column: 1 / 4;
  grid-row: 7 / 10;
}

/* 线路覆盖 */
.line-coverage {
  grid-column: 4 / 10;
  grid-row: 1 / 10;
}

/* 干线信息 */
.trunk-line {
  grid-column: 10 / 13;
  grid-row: 1 / 4;
}

/* 中继光缆信息 */
.relay-cable {
  grid-column: 10 / 13;
  grid-row: 4/ 7;
}

/* 主干光缆信息 */
.backbone-cable {
  grid-column: 10 / 13;
  grid-row: 7 / 10;
}
</style>
