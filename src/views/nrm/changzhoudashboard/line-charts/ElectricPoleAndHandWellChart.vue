<template>
  <div ref="chartDom" class="w-full h-full"></div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { callChangzhouAPI } from '@/store/changzhou/util.ts';

const chartDom = ref(null);
let chart = null;
const electricPoleData = ref({
  市区: 0,
  金坛: 0,
  溧阳: 0
});

const handWellData = ref({
  市区: 0,
  金坛: 0,
  溧阳: 0
});

// 计算合计数据
const totalElectricPole = computed(() => {
  return electricPoleData.value.市区 + electricPoleData.value.金坛 + electricPoleData.value.溧阳;
});

const totalHandWell = computed(() => {
  return handWellData.value.市区 + handWellData.value.金坛 + handWellData.value.溧阳;
});

// 获取电杆数据
const fetchElectricPoleData = async () => {
  try {
    const result = await callChangzhouAPI('diangancount', 'V20250425191714951');
    if (result && result.count) {
      const data = {};
      result.count.forEach(item => {
        data[item.区域 === '市区' ? '市区' : item.区域] = item.数量;
      });
      electricPoleData.value = data;
    }
  } catch (error) {
    console.error('获取电杆数据失败:', error);
  }
};

// 获取人手井数据
const fetchHandWellData = async () => {
  try {
    const result = await callChangzhouAPI('renshoujingcount', 'V20250425192015013');
    if (result && result.count) {
      const data = {};
      result.count.forEach(item => {
        data[item.区域 === '市区' ? '市区' : item.区域] = item.数量;
      });
      handWellData.value = data;
    }
  } catch (error) {
    console.error('获取人手井数据失败:', error);
  }
};

// 初始化图表
const initChart = () => {
  if (!chartDom.value) return;

  chart = echarts.init(chartDom.value, null, { renderer: 'svg' });
  const option = {
    grid: {
      top: 25,
      bottom: 20,
      left: 55,
      right: 5,
      // containLabel: true
    },
    legend: {
      // top: 10,
      textStyle: {
        color: '#64748b',
        fontSize: 10
      }
    },
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(255,255,255,0.95)',
      borderColor: '#3b82f6',
      borderWidth: 1,
      textStyle: {
        color: '#1e293b'
      }
    },
    xAxis: {
      type: 'category',
      data: ['市区', '金坛', '溧阳', '合计'],
      axisLine: {
        lineStyle: {
          color: '#cbd5e1'
        }
      },
      axisLabel: {
        color: '#64748b'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisLabel: {
        color: '#64748b'
      }
    },
    series: [
      {
        name: '电杆数量',
        type: 'bar',
        data: [electricPoleData.value.市区,
        electricPoleData.value.金坛,
        electricPoleData.value.溧阳,
        totalElectricPole.value],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#3b82f6' },
            { offset: 1, color: 'rgba(59, 130, 246, 0.3)' }
          ])
        },
        barWidth: '30%',
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#3b82f6' },
              { offset: 1, color: '#3b82f6' }
            ])
          }
        }
      },
      {
        name: '人手井数量',
        type: 'bar',
        data: [handWellData.value.市区,
        handWellData.value.金坛,
        handWellData.value.溧阳,
        totalHandWell.value],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#f59e42' },
            { offset: 1, color: 'rgba(245, 158, 66, 0.3)' }
          ])
        },
        barWidth: '30%',
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#f59e42' },
              { offset: 1, color: '#f59e42' }
            ])
          }
        }
      },
    ]
  };

  chart.setOption(option);

  window.addEventListener('resize', () => {
    chart.resize();
  });
};

// 获取数据并初始化图表
const fetchDataAndInitChart = async () => {
  await Promise.all([fetchElectricPoleData(), fetchHandWellData()]);
  initChart();
};

onMounted(() => {
  fetchDataAndInitChart();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
  window.removeEventListener('resize', () => {
    chart.resize();
  });
});
</script>
