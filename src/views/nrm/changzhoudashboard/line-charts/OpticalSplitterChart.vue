<template>
  <div ref="chartDom" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive } from 'vue';
import * as echarts from 'echarts';
import { callChangzhouAPI } from '@/store/changzhou/util.ts';
import { createLocalStorage } from '@/utils/cache';
import { OPTICAL_SPLITTER_DATA_KEY } from '@/enums/cacheEnum';

const chartDom = ref(null);
let chart = null;

// 创建缓存实例，设置1天过期时间
const cacheStorage = createLocalStorage({ timeout: 60 * 60 * 24 });

// 预设数据常量
const MOCK_DATA = {
  '天宁区': {
    out端口总数: 354663,
    在用率: '59.47%'
  },
  '新北区': {
    out端口总数: 367735,
    在用率: '59.70%'
  },
  '武进区': {
    out端口总数: 601572,
    在用率: '59.62%'
  },
  '溧阳市': {
    out端口总数: 376590,
    在用率: '60.88%'
  },
  '经开区': {
    out端口总数: 182483,
    在用率: '61.46%'
  },
  '金坛区': {
    out端口总数: 314986,
    在用率: '60.88%'
  },
  '钟楼区': {
    out端口总数: 351300,
    在用率: '59.49%'
  }
};

// 定义响应式数据对象
const splitterState = reactive({
  isLoaded: false,
  isUsingMockData: false,
  isUsingCacheData: false,
  dataSource: '', // 'cache', 'mock', 'api'
  data: {}
});

const opticalSplitterData = ref({});

// 检查缓存数据
const checkCacheData = () => {
  try {
    const cachedData = cacheStorage.get(OPTICAL_SPLITTER_DATA_KEY);
    if (cachedData && Object.keys(cachedData).length > 0) {
      console.log('使用缓存数据:', cachedData);

      // 使用缓存数据更新状态
      opticalSplitterData.value = { ...cachedData };
      splitterState.data = { ...cachedData };
      splitterState.isUsingCacheData = true;
      splitterState.dataSource = 'cache';

      return true;
    }
  } catch (error) {
    console.error('读取缓存数据失败:', error);
  }
  return false;
};

// 设置缓存数据
const setCacheData = (cacheData) => {
  try {
    cacheStorage.set(OPTICAL_SPLITTER_DATA_KEY, cacheData);
    console.log('缓存数据已更新:', cacheData);
  } catch (error) {
    console.error('设置缓存数据失败:', error);
  }
};

// 使用预设数据
const useMockData = () => {
  console.log('使用预设数据:', MOCK_DATA);
  opticalSplitterData.value = { ...MOCK_DATA };
  splitterState.data = { ...MOCK_DATA };
  splitterState.isUsingMockData = true;
  splitterState.dataSource = 'mock';
};

// 获取分光器使用情况数据
const fetchOpticalSplitterData = async () => {
  try {
    const result = await callChangzhouAPI('nrm.graph_cz_obd_portused', 'V20250409093434784');
    if (result && result.data) {
      // 将API返回的数据转换为图表所需的格式
      const formattedData = {};
      result.data.forEach(item => {
        // 排除"常州市区"数据
        if (item.区局 !== '常州市区') {
          formattedData[item.区局] = {
            out端口总数: parseInt(item.out端口总数),
            在用率: (parseFloat(item.在用率) * 100).toFixed(2) + '%'
          };
        }
      });

      // 更新响应式数据
      opticalSplitterData.value = formattedData;
      splitterState.data = formattedData;
      splitterState.isLoaded = true;
      splitterState.isUsingMockData = false;
      splitterState.isUsingCacheData = false;
      splitterState.dataSource = 'api';

      // 更新缓存
      setCacheData(formattedData);

      // 获取真实数据后更新图表
      initChart();

      console.log('API数据已更新:', formattedData);
    }
  } catch (error) {
    console.error('获取分光器数据失败:', error);
  }
};

// 初始化图表
const initChart = () => {
  if (!chartDom.value || !opticalSplitterData.value || Object.keys(opticalSplitterData.value).length === 0) return;

  chart = echarts.init(chartDom.value, null, { renderer: 'svg' });
  const districts = Object.keys(opticalSplitterData.value);
  const xAxisData = districts.map(name => name.replace('区', '').replace('市', ''));
  const seriesData = districts.map(name => opticalSplitterData.value[name].out端口总数);

  const option = {
    grid: {
      top: 25,
      bottom: 20,
      left: 55,
      right: 5,
      // containLabel: true
    },
    legend: {
      // top: 10,
      textStyle: {
        color: '#64748b',
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(255,255,255,0.95)',
      borderColor: '#3b82f6',
      borderWidth: 1,
      textStyle: {
        color: '#1e293b'
      },
      formatter: function (params) {
        const district = params.name;
        // 将简称转换回完整区名
        const fullDistrictName = districts.find(name => name.includes(district));
        if (fullDistrictName && opticalSplitterData.value[fullDistrictName]) {
          const totalPorts = opticalSplitterData.value[fullDistrictName].out端口总数.toLocaleString('zh-CN');
          const utilizationRate = opticalSplitterData.value[fullDistrictName].在用率;
          return `<strong>${fullDistrictName}</strong><br/>
                  out端口总数: ${totalPorts}<br/>
                  在用率: ${utilizationRate}`;
        }
        return district;
      }
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#cbd5e1'
        }
      },
      axisLabel: {
        color: '#64748b'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisLabel: {
        color: '#64748b'
      }
    },
    series: [
      {
        name: '分光器out端口总数',
        type: 'bar',
        data: seriesData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#3b82f6' },
            { offset: 1, color: 'rgba(59, 130, 246, 0.3)' }
          ])
        },
        barWidth: '50%',
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#3b82f6' },
              { offset: 1, color: '#3b82f6' }
            ])
          }
        }
      },
    ]
  };

  chart.setOption(option);

  window.addEventListener('resize', () => {
    chart.resize();
  });
};

onMounted(() => {
  // 数据加载策略：缓存数据 > 预设数据 > 空白状态
  const hasCacheData = checkCacheData();

  if (!hasCacheData) {
    // 无缓存时使用预设数据
    useMockData();
  }

  // 立即初始化图表
  initChart();

  // 后台异步获取最新数据（无论是否有缓存都要更新）
  fetchOpticalSplitterData();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
  window.removeEventListener('resize', () => {
    chart.resize();
  });
});
</script>
