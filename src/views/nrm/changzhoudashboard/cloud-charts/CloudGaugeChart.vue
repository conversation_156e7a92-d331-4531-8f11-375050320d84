<template>
    <div ref="chartDom" class="gauge-chart"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
    value: {
        type: Number,
        default: 0
    },
    name: {
        type: String,
        default: ''
    }
});

const chartDom = ref(null);
let chart = null;

const initChart = () => {
    if (!chartDom.value) return;

    chart = echarts.init(chartDom.value, null, { renderer: 'svg' });
    const option = {
        series: [
            {
                type: 'gauge',
                startAngle: 180,
                endAngle: 0,
                center: ['50%', '75%'],
                radius: '95%',
                min: 0,
                max: 30,
                splitNumber: 6,
                axisLine: {
                    lineStyle: {
                        width: 8,
                        color: [
                            [0.25, '#10b981'],
                            [0.5, '#3b82f6'],
                            [0.75, '#f59e42'],
                            [1, '#ef4444']
                        ]
                    }
                },
                pointer: {
                    icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                    length: '15%',
                    width: 10,
                    offsetCenter: [0, '-60%'],
                    itemStyle: {
                        color: '#1e293b',
                        shadowColor: '#64748b',
                        shadowBlur: 10
                    }
                },
                axisTick: {
                    length: 10,
                    lineStyle: {
                        color: '#cbd5e1',
                        width: 1
                    }
                },
                splitLine: {
                    length: 20,
                    lineStyle: {
                        color: '#cbd5e1',
                        width: 2
                    }
                },
                axisLabel: {
                    color: '#64748b',
                    fontSize: 12,
                    distance: -40,
                    formatter: function (value) {
                        if (value === 0 || value === 30) {
                            return '';
                        }
                        return value + '%';
                    }
                },
                title: {
                    offsetCenter: [0, '-10%'],
                    fontSize: 14,
                    color: '#1e293b'
                },
                detail: {
                    offsetCenter: [0, '40%'],
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: '#1e293b',
                    formatter: function (value) {
                        return value.toFixed(2) + '%';
                    },
                    valueAnimation: true,
                    rich: {
                        value: {
                            fontSize: 28,
                            fontWeight: 'bold',
                            color: '#1e293b'
                        },
                        unit: {
                            fontSize: 14,
                            color: '#64748b',
                            padding: [0, 0, 0, 5]
                        }
                    }
                },
                data: [
                    {
                        value: props.value,
                        name: '',
                    }
                ],
                itemStyle: {
                    shadowColor: '#e2e8f0',
                    shadowBlur: 10
                },
                animation: false
            }
        ]
    };

    chart.setOption(option);
};

const updateChart = () => {
    if (!chart) return;

    chart.setOption({
        series: [
            {
                data: [
                    {
                        value: props.value,
                        name: ''
                    }
                ]
            }
        ]
    });
};

onMounted(() => {
    initChart();

    window.addEventListener('resize', () => {
        chart && chart.resize();
    });
});

watch(
    () => [props.value, props.name],
    () => {
        updateChart();
    }
);

onUnmounted(() => {
    if (chart) {
        chart.dispose();
        chart = null;
    }
});
</script>

<style scoped>
.gauge-chart {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.1));
}
</style>