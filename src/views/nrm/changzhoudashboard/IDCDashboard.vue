<template>
  <div class="dashboard-container">
    <!-- 背景装饰元素 -->
    <div class="tech-background"></div>
    <div class="tech-overlay"></div>
    <div class="tech-particles"></div>
    <div class="tech-circuit"></div>
    <div class="tech-grid"></div>
    <!-- 主内容区 -->
    <main class="dashboard-content">
      <!-- IDC局站数量 -->
      <div class="dashboard-card drone-network">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <!-- 多机房结构 -->
              <path d="M3 2h6v20H3z" />
              <path d="M9 5h6v17H9z" />
              <path d="M15 8h6v14h-6z" />
              <!-- 统一状态指示 -->
              <path d="M21 11v2" />
              <path d="M17 8v2" />
              <path d="M13 5v2" />
            </svg>
            <span>局站数量</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <i-d-c-number-chart />
          </div>
        </div>
      </div>
      <!-- IDC覆盖 -->
      <div class="dashboard-card wireless-coverage">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
            <span>局站覆盖</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="relative h-full w-full rounded-lg overflow-hidden">
            <i-d-c-coverage-map />
          </div>
        </div>
      </div>
      <!-- IDC数据中心卡片 - 动态渲染 -->
      <div v-for="(key, index) in idcCenterKeys" :key="key" :class="['dashboard-card', `idc-center${index + 1}`]">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <!-- 服务器机架 -->
              <path d="M5 2h14v20H5z" />
              <!-- 服务器节点 -->
              <path d="M8 6h8" />
              <path d="M8 10h8" />
              <path d="M8 14h8" />
              <!-- 右侧状态指示 -->
              <path d="M19 6v4" />
              <path d="M19 14v4" />
            </svg>
            <span>{{ idcCenterData[key].name }}</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <i-d-c-center-chart :idc-center-data="idcCenterData[key]" />
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import IDCNumberChart from './idc-charts/IDCNumberChart.vue';
import IDCCoverageMap from './idc-charts/IDCCoverageMap.vue';
import IDCCenterChart from './idc-charts/IDCCenterChart.vue';
import { idcCenterData, idcCenterKeys, fetchIDCCenterData } from '@/store/changzhou/idcCenterStore';

defineOptions({
  name: 'idc-dashboard'
});

// 组件挂载时确保数据已加载
onMounted(async () => {
  await fetchIDCCenterData();
});

</script>
<style scoped>
/* 主内容区 */
.dashboard-content {
  display: grid;
  grid-template-columns: repeat(15, 1fr);
  grid-template-rows: repeat(9, 1fr);
  height: calc(100vh - 85px);
  width: 100%;
  padding: 4px 14px 8px 4px;
  gap: 6px;
  position: relative;
  z-index: 10;
}

/* 图表位置和大小 */
/* IDC局站数量 */
.drone-network {
  grid-column: 1 / 4;
  grid-row: 1 / 4;
}

/* IDC覆盖 */
.wireless-coverage {
  grid-column: 4 / 10;
  grid-row: 1 / 10;
}

/* IDC数据中心卡片位置 */
.idc-center1 {
  grid-column: 10 / 13;
  grid-row: 1 / 4;
}

.idc-center2 {
  grid-column: 1 / 4;
  grid-row: 4 / 7;
}

.idc-center3 {
  grid-column: 10 / 13;
  grid-row: 4 / 7;
}

.idc-center4 {
  grid-column: 1 / 4;
  grid-row: 7 / 10;
}

.idc-center5 {
  grid-column: 10 / 13;
  grid-row: 7 / 10;
}
</style>
