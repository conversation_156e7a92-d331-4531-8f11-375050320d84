<template>
    <div class="dashboard-container">
        <!-- 主内容区 -->
        <main class="dashboard-content">
            <!-- 三朵云CPU利用率 -->
            <div class="dashboard-card cloud-cpu-usage">
                <div class="dashboard-card-header">
                    <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="h-4 w-4">
                            <path d="M6.5 17h11"></path>
                            <path d="M6.5 13h11"></path>
                            <path d="m10 9-2 2 2 2"></path>
                            <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                            <path d="M22 9H7"></path>
                        </svg>
                        <span>三朵云CPU利用率</span>
                    </h2>
                    <div class="header-decoration"></div>
                </div>
                <div class="dashboard-card-content">
                    <div class="grid grid-cols-3 gap-4 h-full items-center justify-items-center">
                        <div class="flex flex-col items-center">
                            <div class="gauge-chart-container">
                                <cloud-gauge-chart :value="cloudCpuUsageData.data.industry.value"
                                    :name="cloudCpuUsageData.data.industry.name" />
                            </div>
                            <div class="text-gray-800 text-center mt-2">行业云</div>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="gauge-chart-container">
                                <cloud-gauge-chart :value="cloudCpuUsageData.data.government.value"
                                    :name="cloudCpuUsageData.data.government.name" />
                            </div>
                            <div class="text-gray-800 text-center mt-2">政务云</div>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="gauge-chart-container">
                                <cloud-gauge-chart :value="cloudCpuUsageData.data.city.value"
                                    :name="cloudCpuUsageData.data.city.name" />
                            </div>
                            <div class="text-gray-800 text-center mt-2">一城一池</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 存储与内存使用 -->
            <div class="dashboard-card storage-memory-usage">
                <div class="dashboard-card-header">
                    <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="h-4 w-4">
                            <path
                                d="M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16">
                            </path>
                        </svg>
                        <span>存储/内存使用</span>
                    </h2>
                    <div class="header-decoration"></div>
                </div>
                <div class="dashboard-card-content">
                    <div class="grid grid-cols-2 gap-4 h-full items-center">
                        <div>
                            <h4 class="text-blue-600 text-center mb-2">存储使用 (T)</h4>
                            <storage-usage-chart />
                        </div>
                        <div>
                            <h4 class="text-blue-600 text-center mb-2">内存使用 (T)</h4>
                            <memory-usage-chart />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 云TOP5客户清单 -->
            <div class="dashboard-card cloud-customers">
                <div class="dashboard-card-header">
                    <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="h-4 w-4">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span>云TOP5客户清单</span>
                    </h2>
                    <div class="flex items-center gap-2">
                        <button class="menu-button" :class="{ active: activeCloudType === 'industry' }"
                            @click="setActiveCloudType('industry')">行业云</button>
                        <button class="menu-button" :class="{ active: activeCloudType === 'government' }"
                            @click="setActiveCloudType('government')">政务云</button>
                        <button class="menu-button" :class="{ active: activeCloudType === 'city' }"
                            @click="setActiveCloudType('city')">一城一池</button>
                    </div>
                </div>
                <div class="dashboard-card-content">
                    <div class="chart-container">
                        <cloud-customer-chart :cloudType="activeCloudType" />
                    </div>
                </div>
            </div>

            <!-- 资源数量展示区域 -->
            <div class="dashboard-card resource-count">
                <div class="dashboard-card-header">
                    <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="h-4 w-4">
                            <path d="M6 20h4"></path>
                            <path d="M14 20h4"></path>
                            <path d="M6 14h4"></path>
                            <path d="M14 14h4"></path>
                            <path d="M6 8h4"></path>
                            <path d="M14 8h4"></path>
                        </svg>
                        <span>资源数量</span>
                    </h2>
                    <div class="header-decoration"></div>
                </div>
                <div class="dashboard-card-content">
                    <div class="grid grid-cols-3 gap-4">
                        <resource-count-panel v-for="(resource, index) in resourceData" :key="index"
                            :title="resource.title" :data="resource.data" />
                    </div>
                </div>
            </div>
        </main>
    </div>


</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

// 导入所需的图表组件
import CloudGaugeChart from './cloud-charts/CloudGaugeChart.vue';
import StorageUsageChart from './cloud-charts/StorageUsageChart.vue';
import MemoryUsageChart from './cloud-charts/MemoryUsageChart.vue';
import CloudCustomerChart from './cloud-charts/CloudCustomerChart.vue';
import ResourceCountPanel from './cloud-charts/ResourceCountPanel.vue';
import {
    fetchCloudResourceData,
    getResourcePanelData,
    getDefaultResourcePanelData,
    cloudResourceData,
    fetchCloudCpuUsageData,
    cloudCpuUsageData
} from '@/store/changzhou/cloudStore';

defineOptions({
  name: 'cloud-dashboard'
});

// 客户图表类型切换
const activeCloudType = ref('industry');

// 资源数量数据（从cloudStore获取）
const resourceData = computed(() => {
    const data = getResourcePanelData();
    return data.length > 0 ? data : getDefaultResourcePanelData();
});

// 切换云类型
const setActiveCloudType = (type) => {
    activeCloudType.value = type;
};

onMounted(async () => {
    await fetchCloudCpuUsageData(); // 获取云CPU利用率数据
    await fetchCloudResourceData(); // 获取云主机信息数据
});
</script>
<style scoped>
/* 主内容区 */
.dashboard-content {
    display: grid;
    grid-template-columns: repeat(15, 1fr);
    grid-template-rows: repeat(7, 1fr);
    height: calc(100vh - 85px);
    width: 100%;
    padding: 4px 14px 8px 4px;
    gap: 6px;
    position: relative;
    z-index: 10;
}

/* 图表位置和大小 */
/* 三朵云CPU利用率 */
.cloud-cpu-usage {
    grid-column: 1 / 5;
    grid-row: 1 / 5;
}

/* 存储与内存使用 */
.storage-memory-usage {
    grid-column: 5 / 10;
    grid-row: 1 / 5;
}

/* 云TOP5客户清单 */
.cloud-customers {
    grid-column: 10 / 13;
    grid-row: 1 / 8;
}

/* 资源数量展示区域 */
.resource-count {
    grid-column: 1 / 10;
    grid-row: 5 / 8;
}

/* 图表容器样式 */
.gauge-chart-container {
    width: 130px;
    height: 130px;
}
</style>