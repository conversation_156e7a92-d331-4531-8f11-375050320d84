<template>
  <div class="dashboard-container">
    <!-- 背景装饰元素 -->
    <div class="tech-background"></div>
    <div class="tech-overlay"></div>
    <div class="tech-particles"></div>
    <div class="tech-circuit"></div>
    <div class="tech-grid"></div>
    <!-- 主内容区 -->
    <main class="dashboard-content">
      <!-- 城域网核心带宽资源 -->
      <div class="dashboard-card metropolitan-core-bandwidth-resource">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <!-- 服务器机架 -->
              <path d="M5 2h14v20H5z" />
              <!-- 服务器节点 -->
              <path d="M8 6h8" />
              <path d="M8 10h8" />
              <path d="M8 14h8" />
              <!-- 右侧状态指示 -->
              <path d="M19 6v4" />
              <path d="M19 14v4" />
            </svg>
            <span>城域网核心带宽资源</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <div class="grid grid-cols-2 gap-4 h-full items-center">
              <metropolitan-core-bandwidth-163-chart />
              <metropolitan-core-bandwidth-c-n2-chart />
            </div>
          </div>
        </div>
      </div>
      <!-- 有线覆盖 -->
      <div class="dashboard-card wired-coverage">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
            <span>城域网及传输资源</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="relative h-full w-full rounded-lg overflow-hidden">
            <wired-coverage-map />
          </div>
        </div>
      </div>
      <!-- 传输核心端口资源 -->
      <div class="dashboard-card transmission-core-port-resource">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <!-- 多机房结构 -->
              <path d="M3 2h6v20H3z" />
              <path d="M9 5h6v17H9z" />
              <path d="M15 8h6v14h-6z" />
              <!-- 统一状态指示 -->
              <path d="M21 11v2" />
              <path d="M17 8v2" />
              <path d="M13 5v2" />
            </svg>
            <span>传输核心端口资源</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="grid h-full items-center">
            <transmission-core-port-chart />
          </div>
        </div>
      </div>


      <!-- 宽带接入网络端口资源 -->
      <div class="dashboard-card broadband-access-network-port-resource">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <!-- 服务器机架 -->
              <path d="M5 2h14v20H5z" />
              <!-- 服务器节点 -->
              <path d="M8 6h8" />
              <path d="M8 10h8" />
              <path d="M8 14h8" />
              <!-- 右侧状态指示 -->
              <path d="M19 6v4" />
              <path d="M19 14v4" />
            </svg>
            <span>宽带接入网络端口资源</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <div class="grid grid-cols-2 gap-4 h-full items-center">
              <broadband-access-network10-g-chart />
              <broadband-access-network-switch-chart />
            </div>
          </div>
        </div>
      </div>
      <!-- 数字电路接入网络端口资源 -->
      <div class="dashboard-card digital-circuit-access-network-port-resource">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-blue-600 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <!-- 多机房结构 -->
              <path d="M3 2h6v20H3z" />
              <path d="M9 5h6v17H9z" />
              <path d="M15 8h6v14h-6z" />
              <!-- 统一状态指示 -->
              <path d="M21 11v2" />
              <path d="M17 8v2" />
              <path d="M13 5v2" />
            </svg>
            <span>数字电路接入网络端口资源</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <digital-circuit-access-chart />
          </div>
        </div>
      </div>
    </main>
  </div>

</template>

<script setup>
import MetropolitanCoreBandwidth163Chart from './wired-charts/MetropolitanCoreBandwidth163Chart.vue';
import MetropolitanCoreBandwidthCN2Chart from './wired-charts/MetropolitanCoreBandwidthCN2Chart.vue';
import WiredCoverageMap from './wired-charts/WiredCoverageMap.vue';
import TransmissionCorePortChart from './wired-charts/TransmissionCorePortChart.vue';
import BroadbandAccessNetwork10GChart from './wired-charts/BroadbandAccessNetwork10GChart.vue';
import BroadbandAccessNetworkSwitchChart from './wired-charts/BroadbandAccessNetworkSwitchChart .vue';
import DigitalCircuitAccessChart from './wired-charts/DigitalCircuitAccessChart.vue';

defineOptions({
  name: 'wired-dashboard'
});

</script>
<style scoped>
/* 主内容区 */
.dashboard-content {
  display: grid;
  grid-template-columns: repeat(15, 1fr);
  grid-template-rows: repeat(8, 1fr);
  height: calc(100vh - 85px);
  width: 100%;
  padding: 4px 14px 8px 4px;
  gap: 6px;
  position: relative;
  z-index: 10;
}

/* 图表位置和大小 */
/* 城域网核心带宽资源 */
.metropolitan-core-bandwidth-resource {
  grid-column: 1 / 4;
  grid-row: 1 / 5;
}

/* 有线覆盖 */
.wired-coverage {
  grid-column: 4 / 10;
  grid-row: 1 / 9;
}

/* 传输核心端口资源 */
.transmission-core-port-resource {
  grid-column: 10 / 13;
  grid-row: 1 / 5;
}

/* 宽带接入网络端口资源 */
.broadband-access-network-port-resource {
  grid-column: 1 / 4;
  grid-row: 5 / 9;
}

/* 数字电路接入网络端口资源 */
.digital-circuit-access-network-port-resource {
  grid-column: 10 / 13;
  grid-row: 5 / 9;
}
</style>
