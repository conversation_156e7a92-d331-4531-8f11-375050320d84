<template>
  <div ref="chartDom" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive } from 'vue';
import * as echarts from 'echarts';
import { callChangzhouAPI } from '@/store/changzhou/util';
import { createLocalStorage } from '@/utils/cache';
import { DIGITAL_CIRCUIT_DATA_KEY } from '@/enums/cacheEnum';

const chartDom = ref(null);
let chart = null;

// 创建缓存实例，设置1天过期时间
const cacheStorage = createLocalStorage({ timeout: 60 * 60 * 24 });

// 预设数据常量
const MOCK_DATA = {
  msap: {
    total: 2203,
    used: 529,
    unused: 1674,
    usedRatio: 0.2401
  },
  otn: {
    total: 5434,
    used: 2571,
    unused: 2863,
    usedRatio: 0.4731
  }
};

// 定义响应式数据对象
const portData = reactive({
  isLoaded: false,
  isUsingMockData: false,
  isUsingCacheData: false,
  dataSource: '', // 'cache', 'mock', 'api'
  msap: {
    total: 0,
    used: 0,
    unused: 0,
    usedRatio: 0
  },
  otn: {
    total: 0,
    used: 0,
    unused: 0,
    usedRatio: 0
  }
});

// 检查缓存数据
const checkCacheData = () => {
  try {
    const cachedData = cacheStorage.get(DIGITAL_CIRCUIT_DATA_KEY);
    if (cachedData && cachedData.msap && cachedData.otn) {
      console.log('使用缓存数据:', cachedData);

      // 使用缓存数据更新状态
      portData.msap = { ...cachedData.msap };
      portData.otn = { ...cachedData.otn };
      portData.isUsingCacheData = true;
      portData.dataSource = 'cache';

      return true;
    }
  } catch (error) {
    console.error('读取缓存数据失败:', error);
  }
  return false;
};

// 设置缓存数据
const setCacheData = (data) => {
  try {
    const cacheData = {
      msap: { ...data.msap },
      otn: { ...data.otn },
      timestamp: Date.now()
    };
    cacheStorage.set(DIGITAL_CIRCUIT_DATA_KEY, cacheData);
    console.log('缓存数据已更新:', cacheData);
  } catch (error) {
    console.error('设置缓存数据失败:', error);
  }
};

// 使用预设数据
const useMockData = () => {
  console.log('使用预设数据:', MOCK_DATA);
  portData.msap = { ...MOCK_DATA.msap };
  portData.otn = { ...MOCK_DATA.otn };
  portData.isUsingMockData = true;
  portData.dataSource = 'mock';
};

// 获取全市数字电路接入网络端口资源数据
const fetchPortData = async () => {
  try {
    // 并行请求两个接口
    const [msapResponse, otnResponse] = await Promise.all([
      callChangzhouAPI('czmsapcount', 'V20250410100734577'),
      callChangzhouAPI('czotncount', 'V20250410101121585')
    ]);

    const apiData = {
      msap: { total: 0, used: 0, unused: 0, usedRatio: 0 },
      otn: { total: 0, used: 0, unused: 0, usedRatio: 0 }
    };

    // 处理MSAP端口数据
    if (msapResponse && msapResponse.count && msapResponse.count.length > 0) {
      const data = msapResponse.count[0];
      const total = parseInt(data['端口总数']);
      const used = parseInt(data['端口占用数']);
      const usedRatio = parseFloat(data['占用端口百分比'].replace('%', '')) / 100;

      apiData.msap = {
        total,
        used,
        unused: total - used,
        usedRatio
      };
    }

    // 处理接入OTN端口数据
    if (otnResponse && otnResponse.data && otnResponse.data.length > 0) {
      const data = otnResponse.data[0];
      const total = parseInt(data['端口总数']);
      const used = parseInt(data['端口占用数']);
      const usedRatio = parseFloat(data['占用端口百分比'].replace('%', '')) / 100;

      apiData.otn = {
        total,
        used,
        unused: total - used,
        usedRatio
      };
    }

    // 更新响应式数据
    portData.msap = { ...apiData.msap };
    portData.otn = { ...apiData.otn };
    portData.isLoaded = true;
    portData.isUsingMockData = false;
    portData.isUsingCacheData = false;
    portData.dataSource = 'api';

    // 更新缓存
    setCacheData(apiData);

    // 获取真实数据后更新图表
    updateChartData();

    console.log('API数据已更新:', apiData);
  } catch (error) {
    console.error('获取数字电路接入网络端口资源数据失败:', error);
  }
};

// 计算使用百分比
const calculatePercentages = () => {
  const msapPercentage = (portData.msap.usedRatio * 100).toFixed(2) + '%';
  const otnPercentage = (portData.otn.usedRatio * 100).toFixed(2) + '%';
  return [msapPercentage, otnPercentage];
};

// 更新图表数据
const updateChartData = () => {
  if (!chart) return;

  const usedData = [portData.msap.used, portData.otn.used];
  const totalData = [portData.msap.total, portData.otn.total];
  const percentages = calculatePercentages();

  chart.setOption({
    title: {
      text: '全市 - 数字电路接入网络端口资源'
    },
    series: [
      {
        data: totalData
      },
      {
        data: usedData
      }
    ]
  });
};

onMounted(async () => {
  // 数据加载策略：缓存数据 > 预设数据 > 空白状态
  const hasCacheData = checkCacheData();

  if (!hasCacheData) {
    // 无缓存时使用预设数据
    useMockData();
  }

  chart = echarts.init(chartDom.value, null, { renderer: 'svg' });

  // 区域名称
  const regions = ['MSAP', '接入OTN'];

  // 初始数据
  const usedData = [portData.msap.used, portData.otn.used];
  const totalData = [portData.msap.total, portData.otn.total];
  const percentages = calculatePercentages();

  const option = {
    title: {
      text: '全市 - 数字电路接入网络端口资源',
      left: 'center',
      top: 0,
      textStyle: {
        color: '#1e293b',
        fontSize: 13,
        fontWeight: 'normal'
      }
    },
    grid: {
      top: 30, // 为标题留出空间
      bottom: 30,
      left: 53,
      right: 14,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(255,255,255,0.95)',
      borderColor: '#3b82f6',
      borderWidth: 1,
      textStyle: {
        color: '#1e293b'
      }
    },
    // 将x轴和y轴互换
    yAxis: {
      type: 'category',
      data: regions,
      axisLine: {
        lineStyle: {
          color: '#cbd5e1'
        }
      },
      axisLabel: {
        color: '#64748b',
        fontSize: 11
      },
      axisTick: {
        show: false
      }
    },
    xAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisLabel: {
        color: '#64748b',
        fontSize: 11
      }
    },
    series: [
      {
        name: '总量',
        type: 'bar',
        barWidth: '50%',
        data: totalData,
        itemStyle: {
          // 使用自定义颜色函数根据数据索引设置不同颜色
          color: function (params) {
            // 品牌绿、品牌橙
            const colors = ['#16a34a', '#f59e42'];
            return colors[params.dataIndex];
          },
          borderRadius: [0, 5, 5, 0]  // 调整圆角方向
        },
        label: {
          show: true,
          position: 'right',  // 标签位置改为右侧
          color: '#1e293b',
          fontSize: 12,
          fontWeight: 'bold',
          formatter: '{c}'
        },
        z: 1
      },
      {
        name: '已使用',
        type: 'bar',
        barWidth: '50%',
        barGap: '-100%',  // 使两个柱子重叠
        data: usedData,
        itemStyle: {
          // 使用自定义颜色函数根据数据索引设置不同颜色
          color: function (params) {
            // 品牌绿、品牌橙（深色）
            const colors = ['#15803d', '#ea580c'];
            return colors[params.dataIndex];
          },
          borderRadius: [0, 0, 0, 0]
        },
        label: {
          show: true,
          position: 'right',  // 标签位置改为内部右侧
          color: '#1e293b',
          fontSize: 12,
          fontWeight: 'bold',
          formatter: function (params) {
            return percentages[params.dataIndex];
          }
        },
        z: 2
      }
    ],
    backgroundColor: 'transparent',
    animation: true,
    animationDuration: 1200,
    animationEasing: 'elasticOut'
  };

  chart.setOption(option);

  // 添加窗口大小变化监听
  window.addEventListener('resize', resizeChart);

  // 后台异步获取最新数据（无论是否有缓存都要更新）
  fetchPortData();
});



// 重设图表大小
const resizeChart = () => {
  chart && chart.resize();
};

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', resizeChart);
});
</script>
