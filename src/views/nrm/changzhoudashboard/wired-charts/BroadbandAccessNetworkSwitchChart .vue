<template>
  <div ref="chartDom" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive } from 'vue';
import * as echarts from 'echarts';
import { callChangzhouAPI } from '@/store/changzhou/util';
import { createLocalStorage } from '@/utils/cache';
import { SWITCH_DATA_KEY } from '@/enums/cacheEnum';

const chartDom = ref(null);
let chart = null;

// 创建缓存实例，设置1天过期时间
const cacheStorage = createLocalStorage({ timeout: 60 * 60 * 24 });

// 预设数据常量
const MOCK_DATA = {
  used: 12703,
  unused: 63556,
  total: 76259,
  usedRatio: 0.1666
};

// 定义响应式数据对象
const switchData = reactive({
  isLoaded: false, // API数据是否已加载
  isUsingMockData: false, // 是否正在使用预设数据
  isUsingCacheData: false, // 是否正在使用缓存数据
  dataSource: '', // 'cache', 'mock', 'api'
  total: 0, // 总端口数
  used: 0, // 已使用端口数
  unused: 0, // 未使用端口数
  usedRatio: 0 // 使用率
});

// 检查缓存数据
const checkCacheData = () => {
  try {
    // 从缓存中获取数据
    const cachedData = cacheStorage.get(SWITCH_DATA_KEY);

    // 检查缓存数据是否有效
    if (cachedData && cachedData.total && cachedData.used !== undefined) {
      console.log('使用缓存数据:', cachedData);

      // 使用缓存数据更新状态
      switchData.total = cachedData.total;
      switchData.used = cachedData.used;
      switchData.unused = cachedData.unused;
      switchData.usedRatio = cachedData.usedRatio;
      switchData.isUsingCacheData = true;
      switchData.dataSource = 'cache';

      return true; // 表示找到了有效缓存
    }
  } catch (error) {
    console.error('读取缓存数据失败:', error);
  }
  return false; // 表示没有有效缓存
};

// 设置缓存数据
const setCacheData = (data) => {
  try {
    // 构造要缓存的数据对象
    const cacheData = {
      total: data.total,
      used: data.used,
      unused: data.unused,
      usedRatio: data.usedRatio,
      timestamp: Date.now() // 添加时间戳
    };

    // 存储到缓存
    cacheStorage.set(SWITCH_DATA_KEY, cacheData);
    console.log('缓存数据已更新:', cacheData);
  } catch (error) {
    console.error('设置缓存数据失败:', error);
  }
};

// 使用预设数据
const useMockData = () => {
  console.log('使用预设数据:', MOCK_DATA);
  switchData.total = MOCK_DATA.total;
  switchData.used = MOCK_DATA.used;
  switchData.unused = MOCK_DATA.unused;
  switchData.usedRatio = MOCK_DATA.usedRatio;
  switchData.isUsingMockData = true;
  switchData.dataSource = 'mock';
};

// 获取全市交换机口数据
const fetchSwitchData = async () => {
  try {
    const response = await callChangzhouAPI('czjhjdkcount', 'V20250410090858832');

    if (response && response.count && response.count.length > 0) {
      const data = response.count[0];

      // 解析数据
      const total = parseInt(data['端口总数']);
      const used = parseInt(data['端口占用数']);
      const usedRatio = parseFloat(data['占用端口百分比'].replace('%', '')) / 100;

      const apiData = {
        total,
        used,
        unused: total - used,
        usedRatio
      };

      // 更新响应式数据
      switchData.total = apiData.total;
      switchData.used = apiData.used;
      switchData.unused = apiData.unused;
      switchData.usedRatio = apiData.usedRatio;
      switchData.isLoaded = true;
      switchData.isUsingMockData = false;
      switchData.isUsingCacheData = false;
      switchData.dataSource = 'api';

      // 更新缓存
      setCacheData(apiData);

      // 获取真实数据后更新图表
      updateChartData();

      console.log('API数据已更新:', apiData);
    }
  } catch (error) {
    console.error('获取交换机口数据失败:', error);
  }
};

// 更新图表数据
const updateChartData = () => {
  if (!chart) return;

  chart.setOption({
    title: {
      text: '全市 - 交换机口'
    },
    series: [
      {
        data: [
          {
            value: switchData.used,
            name: '已使用',
            itemStyle: { color: '#3b82f6' }
          },
          {
            value: switchData.unused,
            name: '未使用',
            itemStyle: { color: '#e5e7eb' }
          }
        ]
      }
    ]
  });
};

onMounted(async () => {
  // 数据加载策略：缓存数据 > 预设数据 > 空白状态
  const hasCacheData = checkCacheData();

  if (!hasCacheData) {
    // 无缓存时使用预设数据
    useMockData();
  }

  // 先初始化图表
  chart = echarts.init(chartDom.value, null, { renderer: 'svg' });
  const option = {
    title: {
      text: '全市 - 交换机口',
      left: 'center',
      top: '10%',
      textStyle: {
        color: '#1e293b',
        fontSize: 13
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)',
      backgroundColor: 'rgba(255,255,255,0.95)',
      borderColor: '#3b82f6',
      borderWidth: 1,
      textStyle: {
        color: '#1e293b',
        fontSize: 12
      }
    },
    legend: {
      bottom: '10%',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        color: '#64748b',
        fontSize: 12
      }
    },
    series: [
      {
        name: '交换机口数据',
        type: 'pie',
        radius: ['50%', '80%'],
        center: ['50%', '50%'],
        clockwise: false,
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#cbd5e1',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'center',
          formatter: () => {
            // 使用已计算的使用率
            const percentage = (switchData.usedRatio * 100).toFixed(2);
            return percentage + '%';
          },
          fontSize: 16,
          fontWeight: 'bold',
          color: '#1e293b'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: '#3b82f6'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: switchData.used,
            name: '已使用',
            itemStyle: { color: '#3b82f6' }
          },
          {
            value: switchData.unused,
            name: '未使用',
            itemStyle: { color: '#e5e7eb' }
          },
        ]
      }
    ]
  };

  chart.setOption(option);

  // 添加窗口大小变化监听
  window.addEventListener('resize', resizeChart);

  // 后台异步获取最新数据（无论是否有缓存都要更新）
  fetchSwitchData();
});



// 重设图表大小
const resizeChart = () => {
  chart && chart.resize();
};

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', resizeChart);
});
</script>
