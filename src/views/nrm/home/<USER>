<template>
  <PageWrapper class="home-box">
    <!-- <Card /> -->
    <!-- <Home /> -->
    <NewHome />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import Card from './Card.vue';
  import Home from './home.vue';
  import NewHome from './NewHome.vue'
  const loading = ref(true);

  defineOptions({
    name: 'home'
  });

  setTimeout(() => {
    loading.value = false;
  }, 1500);
</script>
<style lang="less" scoped>
.home-box {
  :deep(.vben-page-wrapper-content) {
    margin: 0;
  }
}
</style>
