<template>
  <div class="modern-home">
    <!-- Hero Banner Section -->
    <div class="hero-banner">
      <div class="hero-overlay"></div>
      <div class="container">
        <div class="hero-content">
          <div class="hero-badge">
            <span class="badge-text">江苏电信IBOC自研产品</span>
          </div>
          <h1 class="hero-title">
            元凤
          </h1>
          <h2 class="hero-subtitle">
            云网数字孪生平台
          </h2>
          <p class="hero-description">
            基于"1+4+4+X"架构的云网数字孪生解决方案<br>
          </p>
          <div class="hero-stats">
            <div class="stat-card">
              <div class="stat-number">100万+</div>
              <div class="stat-label">日均API调用</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">1500+</div>
              <div class="stat-label">原子能力API</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">30万+</div>
              <div class="stat-label">自研代码行数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">4项</div>
              <div class="stat-label">技术专利</div>
            </div>
          </div>
          <div class="hero-actions">
            <button class="btn-primary" @click="scrollToArchitecture">
              <span>了解架构</span>
              <i class="arrow-down"></i>
            </button>
            <button class="btn-secondary" @click="scrollToCases">
              查看案例
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Overview Section -->
    <div class="overview-section" id="architecture">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">产品概述</h2>
          <p class="section-subtitle">核心架构："底座+能力组件+应用"理念，形成"1+4+4+X"功能架构</p>
        </div>

        <div class="architecture-display">
          <div class="architecture-image">
            <img src="/resource/首页图/architecture-diagram.png" alt="1+4+4+X架构图" />
          </div>
          <div class="architecture-details">
            <div class="arch-item">
              <div class="arch-number">1</div>
              <div class="arch-content">
                <h4>底座</h4>
                <p>汇聚全专业资源数据，包括本地资源数据、集团OTMS网管数据、网络发现数据、业务配置数据</p>
              </div>
            </div>
            <div class="arch-item">
              <div class="arch-number">4</div>
              <div class="arch-content">
                <h4>大关键能力</h4>
                <p>资源智聚、资源智享、资源智算、资源智绘</p>
              </div>
            </div>
            <div class="arch-item">
              <div class="arch-number">4+X</div>
              <div class="arch-content">
                <h4>应用</h4>
                <p>基于底座和能力组件搭建各专业场景应用</p>
              </div>
            </div>
          </div>
        </div>

        <div class="deployment-info">
          <div class="info-card">
            <h4>部署方式</h4>
            <p>支持私有化部署，可基于容器或虚机部署</p>
          </div>
          <div class="info-card">
            <h4>研发团队</h4>
            <p>江苏电信IBOC 12人核心自研团队，拥有4项专利，自研代码30万行+</p>
          </div>
        </div>
      </div>
    </div>
    <!-- Core Capabilities Section -->
    <div class="capabilities-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">核心能力</h2>
          <p class="section-subtitle">四大关键能力支撑智能化网络资源管理</p>
        </div>

        <!-- 跑马灯展示 -->
        <div class="capabilities-marquee">
          <div class="marquee-container">
            <div class="marquee-content">
              <div class="capability-item">
                <div class="capability-number">01</div>
                <div class="capability-name">资源智聚</div>
                <div class="capability-desc">日均处理100W+次数据调用</div>
              </div>
              <div class="capability-item">
                <div class="capability-number">02</div>
                <div class="capability-name">资源智享</div>
                <div class="capability-desc">已开发原子能力API超1500个</div>
              </div>
              <div class="capability-item">
                <div class="capability-number">03</div>
                <div class="capability-name">资源智算</div>
                <div class="capability-desc">故障定位精确到井盖级别</div>
              </div>
              <div class="capability-item">
                <div class="capability-number">04</div>
                <div class="capability-name">资源智绘</div>
                <div class="capability-desc">支持多维度图形化呈现</div>
              </div>
              <!-- 重复一遍用于无缝滚动 -->
              <div class="capability-item">
                <div class="capability-number">01</div>
                <div class="capability-name">资源智聚</div>
                <div class="capability-desc">日均处理100W+次数据调用</div>
              </div>
              <div class="capability-item">
                <div class="capability-number">02</div>
                <div class="capability-name">资源智享</div>
                <div class="capability-desc">已开发原子能力API超1500个</div>
              </div>
              <div class="capability-item">
                <div class="capability-number">03</div>
                <div class="capability-name">资源智算</div>
                <div class="capability-desc">故障定位精确到井盖级别</div>
              </div>
              <div class="capability-item">
                <div class="capability-number">04</div>
                <div class="capability-name">资源智绘</div>
                <div class="capability-desc">支持多维度图形化呈现</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 详细能力展示 -->
        <div class="capabilities-detail">
          <div class="capability-section" data-aos="fade-up">
            <div class="capability-content">
              <div class="capability-badge">01</div>
              <h3>资源智聚</h3>
              <div class="capability-features">
                <div class="feature-item">
                  <div class="feature-icon">🔄</div>
                  <div class="feature-text">采控孪生组件支持中盈、科大两种采集模式</div>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">📡</div>
                  <div class="feature-text">实现网元设备、端口、邻居关系等数据的动态采集</div>
                </div>
                <div class="feature-item highlight">
                  <div class="feature-icon">⚡</div>
                  <div class="feature-text"><strong>日均处理100W+次数据调用，节省上千人日联调时间</strong></div>
                </div>
              </div>
            </div>
            <div class="capability-image">
              <div class="image-container">
                <img src="/resource/首页图/资源智聚.png" alt="资源智聚" />
                <div class="image-overlay">
                  <div class="floating-stats">
                    <div class="stat-bubble">100W+</div>
                    <div class="stat-bubble">实时采集</div>
                    <div class="stat-bubble">智能聚合</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="capability-section reverse" data-aos="fade-up">
            <div class="capability-content">
              <div class="capability-badge">02</div>
              <h3>资源智享</h3>
              <div class="capability-features">
                <div class="feature-item">
                  <div class="feature-icon">🔗</div>
                  <div class="feature-text">以TSP3.0统一模型为基础，开放API接口</div>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">⚙️</div>
                  <div class="feature-text">突破分布式事务关键技术，将事务封装为服务</div>
                </div>
                <div class="feature-item highlight">
                  <div class="feature-icon">🚀</div>
                  <div class="feature-text"><strong>已开发原子能力API超1500个</strong></div>
                </div>
              </div>
            </div>
            <div class="capability-image">
              <div class="image-container">
                <img src="/resource/首页图/资源智享.png" alt="资源智享" />
                <div class="image-overlay">
                  <div class="floating-stats">
                    <div class="stat-bubble">1500+</div>
                    <div class="stat-bubble">API接口</div>
                    <div class="stat-bubble">开放共享</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="capability-section" data-aos="fade-up">
            <div class="capability-content">
              <div class="capability-badge">03</div>
              <h3>资源智算</h3>
              <div class="capability-features">
                <div class="feature-item">
                  <div class="feature-icon">🧠</div>
                  <div class="feature-text">基于图数据库构建网络资源知识图谱模型</div>
                </div>
                <div class="feature-item highlight">
                  <div class="feature-icon">🎯</div>
                  <div class="feature-text"><strong>故障定位计算能力：将故障定位范围缩短至两个井盖之间</strong></div>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">⚡</div>
                  <div class="feature-text">计算时长达到毫秒级，无数据同步延迟</div>
                </div>
              </div>
            </div>
            <div class="capability-image">
              <div class="image-container">
                <img src="/resource/首页图/资源智算.png" alt="资源智算" />
                <div class="image-overlay">
                  <div class="floating-stats">
                    <div class="stat-bubble">毫秒级</div>
                    <div class="stat-bubble">精准计算</div>
                    <div class="stat-bubble">智能分析</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="capability-section reverse" data-aos="fade-up">
            <div class="capability-content">
              <div class="capability-badge">04</div>
              <h3>资源智绘</h3>
              <div class="capability-features">
                <div class="feature-item">
                  <div class="feature-icon">🎨</div>
                  <div class="feature-text">逻辑图谱：支持客户组网视图、组网专线资源树等图形化呈现</div>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">🗺️</div>
                  <div class="feature-text">GIS组件：支持资源图层定义、数据渲染、事件处理等功能</div>
                </div>
              </div>
            </div>
            <div class="capability-image">
              <div class="image-container">
                <img src="/resource/首页图/资源智绘.png" alt="资源智绘" />
                <div class="image-overlay">
                  <div class="floating-stats">
                    <div class="stat-bubble">可视化</div>
                    <div class="stat-bubble">多维展示</div>
                    <div class="stat-bubble">交互体验</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Application Scenarios Section -->
    <div class="scenarios-section" id="cases">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">应用场景</h2>
          <p class="section-subtitle">真实案例验证平台能力</p>
        </div>

        <div class="scenarios-detail">
          <div class="scenario-section" data-aos="fade-up" @click="navigateTo('/nrm/res-app/faultpositioning')">
            <div class="scenario-content">
              <div class="scenario-badge">故障定位</div>
              <h3>某市5条光缆群障处理</h3>
              <div class="scenario-features">
                <div class="feature-highlight">
                  <div class="highlight-number">10秒内</div>
                  <div class="highlight-text">定位障碍光缆段</div>
                </div>
                <div class="feature-highlight">
                  <div class="highlight-number">井盖级别</div>
                  <div class="highlight-text">精准定位范围</div>
                </div>
                <div class="feature-highlight">
                  <div class="highlight-number">一键生成</div>
                  <div class="highlight-text">全量用户清单</div>
                </div>
              </div>
              <div class="scenario-description">
                智能分析障碍设施，快速计算影响范围，政企客户及时发现，公众客户一键拦截
              </div>
            </div>
            <div class="scenario-image">
              <div class="image-container">
                <img src="/resource/首页图/障碍定位案例.png" alt="故障定位案例" />
                <div class="image-overlay">
                  <div class="play-button">
                    <div class="play-icon">▶</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="scenario-section reverse" data-aos="fade-up" @click="navigateTo('/nrm/res-app/risk-manage')">
            <div class="scenario-content">
              <div class="scenario-badge">隐患治理</div>
              <h3>重要客户主备电路同路由问题</h3>
              <div class="scenario-features">
                <div class="feature-highlight">
                  <div class="highlight-number">多层级</div>
                  <div class="highlight-text">同光缆、同管道、同板卡定位</div>
                </div>
                <div class="feature-highlight">
                  <div class="highlight-number">闭环管控</div>
                  <div class="highlight-text">审核、派单、处理、回单</div>
                </div>
              </div>
              <div class="scenario-description">
                灵活纳管，生命线业务和组网线路同路由隐患每日巡检、提前预警
              </div>
            </div>
            <div class="scenario-image">
              <div class="image-container">
                <img src="/resource/首页图/双路由案例.png" alt="隐患治理案例" />
                <div class="image-overlay">
                  <div class="play-button">
                    <div class="play-icon">▶</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="scenario-section" data-aos="fade-up" @click="navigateTo('/nrm/res-app/faultpositioning/externalforcepoints')">
            <div class="scenario-content">
              <div class="scenario-badge">外力点管控</div>
              <h3>某地路段施工作业管理</h3>
              <div class="scenario-features">
                <div class="feature-highlight">
                  <div class="highlight-number">秒级呈现</div>
                  <div class="highlight-text">图形化操作全量业务</div>
                </div>
                <div class="feature-highlight">
                  <div class="highlight-number">分钟级</div>
                  <div class="highlight-text">重要业务优先识别</div>
                </div>
              </div>
              <div class="scenario-description">
                施工区域地图一键绘制，地图在线统一管理，影响光缆段一键导出
              </div>
            </div>
            <div class="scenario-image">
              <div class="image-container">
                <img src="/resource/首页图/外力点案例.png" alt="外力点管控案例" />
                <div class="image-overlay">
                  <div class="play-button">
                    <div class="play-icon">▶</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="scenario-section reverse" data-aos="fade-up" @click="navigateTo('/nrm/res-app/cut-over/cut-over')">
            <div class="scenario-content">
              <div class="scenario-badge">割接影响分析</div>
              <h3>某地光缆割接操作</h3>
              <div class="scenario-features">
                <div class="feature-highlight">
                  <div class="highlight-number">10秒输出</div>
                  <div class="highlight-text">全量业务影响评估</div>
                </div>
                <div class="feature-highlight">
                  <div class="highlight-number">可探测</div>
                  <div class="highlight-text">割接前后在线状态</div>
                </div>
                <div class="feature-highlight">
                  <div class="highlight-number">上百起</div>
                  <div class="highlight-text">每月规避客户报障</div>
                </div>
              </div>
              <div class="scenario-description">
                前置拦截，割接前评估影响面。后向评估，割接后检测恢复情况
              </div>
            </div>
            <div class="scenario-image">
              <div class="image-container">
                <img src="/resource/首页图/割接功能案例.png" alt="割接影响分析案例" />
                <div class="image-overlay">
                  <div class="play-button">
                    <div class="play-icon">▶</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Open Capabilities Section -->
    <div class="open-capabilities-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">开放能力</h2>
          <p class="section-subtitle">强大的API生态和二次开发支持</p>
        </div>

        <div class="capabilities-stats">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-icon">🔌</div>
              <div class="stat-number">1500+</div>
              <div class="stat-label">原子能力API</div>
              <div class="stat-desc">业务人员自行开发超200个</div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">📊</div>
              <div class="stat-number">100万+</div>
              <div class="stat-label">日均调用量</div>
              <div class="stat-desc">高并发稳定运行</div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">🗃️</div>
              <div class="stat-number">678张</div>
              <div class="stat-label">资源数据表</div>
              <div class="stat-desc">全面覆盖网络资源</div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">🔧</div>
              <div class="stat-number">开放</div>
              <div class="stat-label">二次开发支持</div>
              <div class="stat-desc">开放非核心代码，支持API接口开放</div>
            </div>
          </div>
        </div>

        <div class="standards-info">
          <div class="standard-card">
            <h4>标准化程度</h4>
            <p>基于集团新一代架构，能力注册到DCOOS</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Team Carousel Section -->
    <div class="team-carousel-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">研发团队</h2>
          <p class="section-subtitle">核心技术团队与地市协同创新机制</p>
        </div>

        <div class="team-carousel">
          <div class="carousel-container">
            <div class="carousel-track" :style="{ transform: `translateX(-${currentTeamSlide * 100}%)` }">
              <!-- IBOC团队 -->
              <div class="carousel-slide">
                <div class="team-showcase">
                  <div class="team-image-large">
                    <img src="/resource/首页图/team-photo.png" alt="江苏电信IBOC团队" />
                  </div>
                  <div class="team-info-large">
                    <h3>江苏电信IBOC团队</h3>
                    <p class="team-subtitle">核心技术研发团队</p>
                    <div class="team-highlights">
                      <div class="highlight-item">
                        <div class="highlight-number">12人</div>
                        <div class="highlight-label">核心自研团队</div>
                      </div>
                      <div class="highlight-item">
                        <div class="highlight-number">30万+</div>
                        <div class="highlight-label">自研代码行数</div>
                      </div>
                      <div class="highlight-item">
                        <div class="highlight-number">4项</div>
                        <div class="highlight-label">技术专利</div>
                      </div>
                    </div>
                    <div class="team-description-large">
                      专注于云网数字孪生平台的核心技术研发，拥有深厚的电信网络资源管理经验，
                      致力于打造业界领先的智能化网络资源管理解决方案。
                    </div>
                  </div>
                </div>
              </div>

              <!-- 地市揭榜挂帅团队 -->
              <div class="carousel-slide">
                <div class="team-showcase">
                  <div class="team-image-large">
                    <img src="/resource/首页图/collaboration-team.png" alt="地市揭榜挂帅团队" />
                  </div>
                  <div class="team-info-large">
                    <h3>地市揭榜挂帅团队</h3>
                    <p class="team-subtitle">全省协同创新团队</p>
                    <div class="team-highlights">
                      <div class="highlight-item">
                        <div class="highlight-number">13个</div>
                        <div class="highlight-label">地市分公司</div>
                      </div>
                      <div class="highlight-item">
                        <div class="highlight-number">50+</div>
                        <div class="highlight-label">业务专家</div>
                      </div>
                      <div class="highlight-item">
                        <div class="highlight-number">200+</div>
                        <div class="highlight-label">自研API接口</div>
                      </div>
                    </div>
                    <div class="team-description-large">
                      <div class="collaboration-framework">
                        <h4>分工协作机制</h4>
                        <div class="collaboration-grid">
                          <div class="collaboration-item">
                            <strong>省公司职责：</strong>平台架构设计、技术选型、核心开发
                          </div>
                          <div class="collaboration-item">
                            <strong>地市职责：</strong>平台测试、优化推广、业务场景开发
                          </div>
                        </div>
                        <div class="collaboration-features">
                          <div class="feature-item">
                            <span class="feature-icon">🤝</span>
                            <span>共建共享机制，确保开发、测试、部署各环节顺利进行</span>
                          </div>
                          <div class="feature-item">
                            <span class="feature-icon">📋</span>
                            <span>详细项目管理计划，周通报进展，推进紧凑有序</span>
                          </div>
                          <div class="feature-item">
                            <span class="feature-icon">💬</span>
                            <span>建立良好管理机制和沟通渠道，多部门协作顺畅</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 轮播控制 -->
          <div class="carousel-controls">
            <button
              class="carousel-btn prev"
              @click="prevTeamSlide"
              :disabled="currentTeamSlide === 0"
            >
              <i class="arrow-left"></i>
            </button>
            <div class="carousel-indicators">
              <button
                v-for="(team, index) in 2"
                :key="index"
                class="indicator"
                :class="{ active: currentTeamSlide === index }"
                @click="currentTeamSlide = index"
              ></button>
            </div>
            <button
              class="carousel-btn next"
              @click="nextTeamSlide"
              :disabled="currentTeamSlide === 1"
            >
              <i class="arrow-right"></i>
            </button>
          </div>
        </div>

        <!-- 优秀案例展示 -->
        <div class="excellence-cases">
          <div class="cases-header">
            <h3>揭榜团队二次开发优秀案例</h3>
            <p class="cases-subtitle">南京分公司2024年度创新成果</p>
          </div>

   

           

          <div class="nanjing-projects">
            <div class="project-card">
              <h4 class="project-title">客户业务视图与健康报告</h4>
              <div class="project-image">
                <img src="/resource/首页图/南京课题1.png" alt="客户业务视图与健康报告" />
              </div>
              <div class="project-description">
                <div class="project-features">
                  <span class="feature-tag">保护组自动归集</span>
                  <span class="feature-tag">业务隐患一键排查</span>
                  <span class="feature-tag">风险点列表展示</span>
                  <span class="feature-tag">GIS图生成路由路径</span>
                </div>
                <p class="project-desc">
                  聚焦政企客户资源数字化管控，通过客户数据收集与分析、客户细分与定位、
                  个性化资源服务策略等，实现客户价值最大化，提升客户满意度和忠诚度。
                </p>
              </div>
            </div>

            <div class="project-card">
              <h4 class="project-title">重要设备视图</h4>
              <div class="project-image">
                <img src="/resource/首页图/南京课题2.png" alt="重要设备视图" />
              </div>
              <div class="project-description">
                <div class="project-features">
                  <span class="feature-tag">OTN网设备拓扑</span>
                  <span class="feature-tag">SDH网逻辑拓扑</span>
                  <span class="feature-tag">IPRAN网拓扑</span>
                  <span class="feature-tag">同屏显示物理路径</span>
                </div>
                <p class="project-desc">
                  用算法替代报表，用拓扑呈现网络。让线路看懂网络，实现网络物理和逻辑一屏展现，
                  实现资源关系自动发现，构建多网络融合的设备视图。
                </p>
              </div>
            </div>

            <div class="project-card">
              <h4 class="project-title">光缆网视图建设</h4>
              <div class="project-image">
                <img src="/resource/首页图/南京课题3.png" alt="光缆网视图建设" />
              </div>
              <div class="project-description">
                <div class="project-features">
                  <span class="feature-tag">局点组件部署</span>
                  <span class="feature-tag">全区光缆视图</span>
                  <span class="feature-tag">AI路由搜索</span>
                  <span class="feature-tag">规划维护一张图</span>
                </div>
                <p class="project-desc">
                  开发AI路由算法实现障碍路由优化方案的自动生成。利用AI实现路由自动搜索，
                  构建全区域光缆网络数字化视图，实现光缆资源的智能化管理和优化配置。
                </p>
              </div>
            </div>
          </div>

          <!-- 年度揭榜情况 -->
          <div class="annual-projects">
            <div class="annual-header">
              <h3>年度揭榜情况</h3>
              <p class="annual-subtitle">持续推进地市协同创新，扩大平台应用覆盖</p>
            </div>

            <div class="year-section">
              <div class="year-title">
                <span class="year-badge">2024年</span>
                <span class="year-cities">南京、盐城</span>
              </div>
              <p class="year-desc">率先启动揭榜挂帅机制，协同完成三大核心课题建设</p>
            </div>

            <div class="year-section current-year">
              <div class="year-title">
                <span class="year-badge current">2025年</span>
                <span class="year-cities">无锡、常州、南通</span>
              </div>
              <p class="year-desc">扩大揭榜范围，聚焦专业化应用场景，深化平台能力建设</p>

              <div class="current-projects">
                <div class="current-project-card">
                  <div class="project-city">无锡</div>
                  <h4>双路由隐患治理流程智能化</h4>
                  <p>建立智能化的双路由隐患识别和治理机制，提升网络安全保障能力</p>
                </div>

                <div class="current-project-card">
                  <div class="project-city">常州</div>
                  <h4>资源分析型应用能力提升</h4>
                  <p>深化资源数据分析能力，构建更加精准的资源配置和优化决策体系</p>
                </div>

                <div class="current-project-card">
                  <div class="project-city">南通</div>
                  <h4>光缆阻断抢修全流程管控</h4>
                  <p>建立光缆故障快速响应机制，实现从故障发现到抢修完成的全流程智能管控</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Awards Section -->
    <div class="awards-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">获奖荣誉</h2>
          <p class="section-subtitle">技术创新成果获得业界认可</p>
        </div>

        <div class="awards-showcase">
          <div class="awards-image-container">
            <img src="/resource/首页图/获奖荣誉.png" alt="获奖荣誉展示" />
          </div>
          <div class="awards-content">
            <div class="awards-grid">
              <div class="award-card">
                <div class="award-icon">🏆</div>
                <div class="award-content">
                  <h4>黑客马拉松专项奖</h4>
                  <p>2022研发链落地开发大赛</p>
                </div>
              </div>
              <div class="award-card">
                <div class="award-icon">🥇</div>
                <div class="award-content">
                  <h4>资源专题组第一</h4>
                  <p>技术创新能力突出</p>
                </div>
              </div>
              <div class="award-card">
                <div class="award-icon">🎖️</div>
                <div class="award-content">
                  <h4>4项技术专利</h4>
                  <p>核心技术自主可控</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Advantages Section -->
    <div class="advantages-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">产品优势</h2>
          <p class="section-subtitle">全自研、标准化、一体化、开放性强</p>
        </div>

        <div class="advantages-grid">
          <div class="advantage-card">
            <div class="advantage-icon">🔧</div>
            <h3>全自研</h3>
            <p>不依赖于厂家，部署灵活，按需剪裁</p>
          </div>
          <div class="advantage-card">
            <div class="advantage-icon">📋</div>
            <h3>标准化</h3>
            <p>符合集团新一代云网操作系统规范</p>
          </div>
          <div class="advantage-card">
            <div class="advantage-icon">🔄</div>
            <h3>一体化</h3>
            <p>数据采集、校验、应用全流程综合平台</p>
          </div>
          <div class="advantage-card">
            <div class="advantage-icon">🔓</div>
            <h3>开放性强</h3>
            <p>支持二次开发，已落地多个揭榜挂帅项目</p>
          </div>
          <div class="advantage-card">
            <div class="advantage-icon">✅</div>
            <h3>实战验证</h3>
            <p>支撑新老城两线业务参数配置超5万单</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Section -->
    <div class="footer-section">
      <div class="container">
        <div class="footer-content">
          <div class="footer-info">
            <h3 class="footer-title">元凤 云网数字孪生平台</h3>
            <p class="footer-description">
              江苏电信IBOC团队自研产品，专业的网络资源智能管理解决方案
            </p>
          </div>
          <div class="footer-contact">
            <h4>联系我们</h4>
            <p>江苏电信IBOC-OSS团队</p>

          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2025 中国电信 版权所有</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { onMounted, ref } from 'vue';

defineOptions({
  name: 'NewHome'
});

const router = useRouter();

// 团队轮播相关
const currentTeamSlide = ref(0);

const nextTeamSlide = () => {
  if (currentTeamSlide.value < 1) {
    currentTeamSlide.value++;
  }
};

const prevTeamSlide = () => {
  if (currentTeamSlide.value > 0) {
    currentTeamSlide.value--;
  }
};

// 初始化动画
onMounted(() => {
  // 简单的滚动动画实现
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // 观察所有带有 data-aos 属性的元素
  const animatedElements = document.querySelectorAll('[data-aos]');
  animatedElements.forEach((el) => {
    (el as HTMLElement).style.opacity = '0';
    (el as HTMLElement).style.transform = 'translateY(30px)';
    (el as HTMLElement).style.transition = 'all 0.6s ease';
    observer.observe(el);
  });
});

// 导航到指定路由
const navigateTo = (path: string) => {
  router.push(path);
};

// 滚动到架构区域
const scrollToArchitecture = () => {
  const element = document.getElementById('architecture');
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

// 滚动到案例区域
const scrollToCases = () => {
  const element = document.getElementById('cases');
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

// 查看文档
const goToDocumentation = () => {
  message.info('技术文档功能开发中...');
};

// 联系支持
const contactSupport = () => {
  message.info('如需技术支持，请联系江苏电信IBOC团队');
};
</script>

<style lang='less' scoped>
.modern-home {
  background: #fff;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 40px;
}

.full-width {
  width: 100%;
  padding: 0;
}

// Hero Banner Section
.hero-banner {
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 58, 138, 0.9) 50%, rgba(59, 130, 246, 0.85) 100%),
              url('/resource/首页图/hero-banner.png') center/cover no-repeat;
  position: relative;
  display: flex;
  align-items: center;
  color: white;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="circuit" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1.5" fill="%23ffffff" opacity="0.08"/><path d="M0,15 L30,15 M15,0 L15,30" stroke="%23ffffff" stroke-width="0.3" opacity="0.06"/><circle cx="5" cy="5" r="0.8" fill="%23ffd700" opacity="0.1"/><circle cx="25" cy="25" r="0.8" fill="%23ffd700" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23circuit)"/></svg>');
    opacity: 0.4;
    animation: circuitFlow 20s linear infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 58, 138, 0.6) 100%);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 100%;
  margin: 0 auto;
}

.hero-badge {
  display: inline-block;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 50px;
  padding: 8px 24px;
  margin-bottom: 24px;
  backdrop-filter: blur(8px);

  .badge-text {
    color: rgba(255, 215, 0, 0.9);
    font-weight: 600;
    font-size: 0.9rem;
  }
}

.hero-title {
  font-size: 5.5rem;
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 32px;
  background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 60%, #ffd700 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
  text-shadow: 0 0 60px rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  text-align: center;
}

.hero-subtitle {
  font-size: 2.2rem;
  font-weight: 600;
  margin-bottom: 40px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2px 15px rgba(255, 255, 255, 0.2);
  letter-spacing: 0.02em;
  text-align: center;
}

.hero-description {
  font-size: 1.4rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 50px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;

  .highlight-number {
    color: #ffd700;
    font-weight: 800;
    font-size: 1.5rem;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }
}

/* 隐藏滚动条（可选） */
.hero-stats::-webkit-scrollbar {
  display: none;
}
.hero-stats {
  display: flex; /* 改用flex布局 */
  flex-wrap: nowrap; /* 禁止换行 */
  justify-content: center; /* 水平居中 */
  gap: 40px;
  margin: 0 auto 60px;
  max-width: 1400px;
  width: 100%;
  padding: 0 20px;
  overflow-x: auto; /* 添加水平滚动条（当内容超出时） */
  scrollbar-width: none; /* 隐藏滚动条（可选） */
  -ms-overflow-style: none; /* IE隐藏滚动条 */
}

.stat-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 24px;
  padding: 40px 36px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  flex: 0 0 300px; /* 固定宽度，不伸缩 */
  /* 保持原有的装饰效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 0 40px rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.3);

    &::before {
      opacity: 1;
    }

    .stat-number {
      transform: scale(1.1);
      text-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    }
  }

  .stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: #ffd700;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  }

  .stat-label {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    letter-spacing: 0.3px;
    white-space: nowrap;
    line-height: 1.3;
  }
}
.hero-actions {
  display: flex;
  gap: 20px;
  justify-content: center;

  .btn-primary, .btn-secondary {
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
  }

  .btn-primary {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1e3a8a;
    box-shadow: 0 4px 16px rgba(255, 215, 0, 0.25);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(255, 215, 0, 0.35);
    }

    .arrow-down {
      width: 16px;
      height: 16px;
      border-right: 2px solid currentColor;
      border-bottom: 2px solid currentColor;
      transform: rotate(45deg);
      animation: bounce 2s infinite;
    }
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(8px);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
    }
  }
}

// Overview Section
.overview-section {
  padding: 100px 0;
  background: linear-gradient(180deg, #ffffff 0%, #f8faff 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 120px;
  position: relative;

  .section-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1e3a8a;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -12px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
      border-radius: 2px;
    }
  }

  .section-subtitle {
    font-size: 1.4rem;
    color: #6b7280;
    line-height: 1.7;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
  }
}

.architecture-display {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  margin-bottom: 60px;
}

.architecture-image {
  img {
    width: 100%;
    height: auto;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(30, 58, 138, 0.15);
  }
}

.architecture-details {
  .arch-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 32px;

    .arch-number {
      background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
      color: white;
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 700;
      font-size: 1.2rem;
      margin-right: 20px;
      flex-shrink: 0;
    }

    .arch-content {
      h4 {
        font-size: 1.3rem;
        font-weight: 600;
        color: #1e3a8a;
        margin-bottom: 8px;
      }

      p {
        color: #6b7280;
        line-height: 1.6;
      }
    }
  }
}

.deployment-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  .info-card {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(30, 58, 138, 0.1);
    border-radius: 16px;
    padding: 32px;
    backdrop-filter: blur(8px);

    h4 {
      font-size: 1.2rem;
      font-weight: 600;
      color: #1e3a8a;
      margin-bottom: 12px;
    }

    p {
      color: #6b7280;
      line-height: 1.6;
    }
  }
}
// Capabilities Section
.capabilities-section {
  padding: 160px 0;
  background: linear-gradient(180deg, #f8faff 0%, #ffffff 50%, #f0f8ff 100%);
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%233b82f6" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
  }
}

// 跑马灯样式
.capabilities-marquee {
  margin-bottom: 120px;
  overflow: hidden;
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 30%, #3b82f6 70%, #60a5fa 100%);
  border-radius: 32px;
  padding: 60px 0;
  position: relative;
  box-shadow: 0 20px 60px rgba(30, 58, 138, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);

  &::before, &::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100px;
    z-index: 2;
    pointer-events: none;
  }

  &::before {
    left: 0;
    background: linear-gradient(90deg, #1e3a8a, transparent);
  }

  &::after {
    right: 0;
    background: linear-gradient(270deg, #3b82f6, transparent);
  }
}

.marquee-container {
  width: 100%;
  overflow: hidden;
}

.marquee-content {
  display: flex;
  animation: marquee 30s linear infinite;
  gap: 60px;

  .capability-item {
    flex-shrink: 0;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 24px;
    padding: 48px 56px;
    color: white;
    text-align: center;
    min-width: 380px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.12);
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 215, 0, 0.3);

      &::before {
        opacity: 1;
      }

      .capability-number {
        transform: scale(1.1);
        text-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
      }
    }

    .capability-number {
      font-size: 3.5rem;
      font-weight: 800;
      color: #ffd700;
      margin-bottom: 16px;
      transition: all 0.3s ease;
      text-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
    }

    .capability-name {
      font-size: 1.6rem;
      font-weight: 700;
      margin-bottom: 12px;
      letter-spacing: 0.5px;
    }

    .capability-desc {
      font-size: 1.1rem;
      opacity: 0.95;
      line-height: 1.5;
      font-weight: 400;
    }
  }
}

// 详细能力展示
.capabilities-detail {
  .capability-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 120px;
    align-items: center;
    margin-bottom: 180px;
    padding: 0 40px;

    &.reverse {
      .capability-content {
        order: 2;
      }

      .capability-image {
        order: 1;
      }
    }

    .capability-content {
      .capability-badge {
        display: inline-block;
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #1e3a8a;
        padding: 12px 32px;
        border-radius: 50px;
        font-weight: 700;
        font-size: 1.1rem;
        margin-bottom: 32px;
        box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);
        letter-spacing: 0.5px;
      }

      h3 {
        font-size: 3.2rem;
        font-weight: 800;
        color: #1e3a8a;
        margin-bottom: 40px;
        line-height: 1.1;
        letter-spacing: -0.02em;
      }

      .capability-features {
        margin-bottom: 24px;

        .feature-item {
          display: flex;
          align-items: center;
          margin-bottom: 24px;
          padding: 24px 28px;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 16px;
          border-left: 6px solid #3b82f6;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(10px);
          box-shadow: 0 4px 20px rgba(30, 58, 138, 0.08);

          &:hover {
            transform: translateX(12px) translateY(-2px);
            box-shadow: 0 12px 40px rgba(30, 58, 138, 0.15);
            background: rgba(255, 255, 255, 0.95);
          }

          &.highlight {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.12) 0%, rgba(59, 130, 246, 0.08) 100%);
            border-left-color: #ffd700;
            border-left-width: 8px;
            box-shadow: 0 8px 32px rgba(255, 215, 0, 0.15);

            .feature-text {
              color: #1e3a8a;
            }

            .feature-icon {
              transform: scale(1.1);
            }
          }

          .feature-icon {
            font-size: 2rem;
            margin-right: 20px;
            flex-shrink: 0;
            transition: transform 0.3s ease;
          }

          .feature-text {
            color: #374151;
            line-height: 1.6;
            font-size: 1.1rem;

            strong {
              color: #1e3a8a;
              font-weight: 700;
              font-size: 1.15rem;
            }
          }
        }
      }
    }

    .capability-image {
      position: relative;

      .image-container {
        position: relative;
        border-radius: 32px;
        overflow: hidden;
        box-shadow: 0 30px 80px rgba(30, 58, 138, 0.2), 0 0 40px rgba(59, 130, 246, 0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:hover {
          transform: translateY(-12px) scale(1.02);
          box-shadow: 0 40px 100px rgba(30, 58, 138, 0.3), 0 0 60px rgba(255, 215, 0, 0.1);

          .image-overlay {
            opacity: 1;
          }
        }

        img {
          width: 100%;
          height: auto;
          display: block;
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(30, 58, 138, 0.8) 0%, rgba(59, 130, 246, 0.6) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          .floating-stats {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            justify-content: center;

            .stat-bubble {
              background: rgba(255, 255, 255, 0.9);
              color: #1e3a8a;
              padding: 8px 16px;
              border-radius: 50px;
              font-weight: 600;
              font-size: 0.9rem;
              backdrop-filter: blur(8px);
              animation: float 3s ease-in-out infinite;

              &:nth-child(2) {
                animation-delay: 1s;
              }

              &:nth-child(3) {
                animation-delay: 2s;
              }
            }
          }
        }
      }
    }
  }
}

// Scenarios Section
.scenarios-section {
  padding: 160px 0;
  background: linear-gradient(180deg, #ffffff 0%, #f8faff 50%, #e6f1ff 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagon" width="30" height="26" patternUnits="userSpaceOnUse"><polygon points="15,2 25,8 25,18 15,24 5,18 5,8" fill="none" stroke="%233b82f6" stroke-width="0.5" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagon)"/></svg>');
    pointer-events: none;
  }
}

.scenarios-detail {
  .scenario-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 120px;
    align-items: center;
    margin-bottom: 180px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0 40px;

    &:hover {
      transform: translateY(-4px);

      .scenario-image .image-container {
        transform: translateY(-8px);
        box-shadow: 0 30px 80px rgba(30, 58, 138, 0.25);

        .image-overlay {
          opacity: 1;
        }
      }
    }

    &.reverse {
      .scenario-content {
        order: 2;
      }

      .scenario-image {
        order: 1;
      }
    }

    .scenario-content {
      .scenario-badge {
        display: inline-block;
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        color: white;
        padding: 14px 32px;
        border-radius: 50px;
        font-weight: 700;
        font-size: 1.1rem;
        margin-bottom: 32px;
        box-shadow: 0 8px 24px rgba(30, 58, 138, 0.3);
        letter-spacing: 0.5px;
      }

      h3 {
        font-size: 2.8rem;
        font-weight: 800;
        color: #1e3a8a;
        margin-bottom: 40px;
        line-height: 1.2;
        letter-spacing: -0.01em;
      }

      .scenario-features {
        display: grid;
        gap: 16px;
        margin-bottom: 24px;

        .feature-highlight {
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 16px;
          padding: 28px 32px;
          border-left: 6px solid #ffd700;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(10px);
          box-shadow: 0 6px 24px rgba(30, 58, 138, 0.08);

          &:hover {
            transform: translateX(12px) translateY(-2px);
            box-shadow: 0 16px 48px rgba(30, 58, 138, 0.15);
            background: rgba(255, 255, 255, 0.95);
            border-left-width: 8px;
          }

          .highlight-number {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #1e3a8a;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: 800;
            font-size: 1.1rem;
            margin-right: 20px;
            flex-shrink: 0;
            min-width: 100px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(255, 215, 0, 0.2);
            letter-spacing: 0.5px;
          }

          .highlight-text {
            color: #374151;
            font-weight: 600;
            line-height: 1.5;
            font-size: 1.1rem;
          }
        }
      }

      .scenario-description {
        color: #6b7280;
        font-size: 1.3rem;
        line-height: 1.7;
        font-style: italic;
        font-weight: 400;
        margin-top: 8px;
      }
    }

    .scenario-image {
      position: relative;

      .image-container {
        position: relative;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(30, 58, 138, 0.15);
        transition: all 0.3s ease;

        img {
          width: 100%;
          height: auto;
          display: block;
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(30, 58, 138, 0.8) 0%, rgba(59, 130, 246, 0.6) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          .play-button {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;

            &:hover {
              transform: scale(1.1);
              background: white;
            }

            .play-icon {
              color: #1e3a8a;
              font-size: 1.5rem;
              margin-left: 4px;
            }
          }
        }
      }
    }
  }
}
// Open Capabilities Section
.open-capabilities-section {
  padding: 100px 0;
  background: linear-gradient(180deg, #f8faff 0%, #ffffff 100%);
}

.capabilities-stats {
  margin-bottom: 60px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(30, 58, 138, 0.1);
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(30, 58, 138, 0.15);
  }

  .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 16px;
  }

  .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
  }

  .stat-desc {
    font-size: 0.9rem;
    color: #6b7280;
    line-height: 1.4;
  }
}

.standards-info {
  .standard-card {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    border-radius: 16px;
    padding: 40px;
    text-align: center;

    h4 {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 12px;
    }

    p {
      font-size: 1.1rem;
      line-height: 1.6;
      opacity: 0.9;
    }
  }
}

// Team Carousel Section
.team-carousel-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f0f7ff 0%, #e0f2fe 100%);
}

.team-carousel {
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.carousel-container {
  overflow: hidden;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(30, 58, 138, 0.1);
}

.carousel-track {
  display: flex;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.carousel-slide {
  min-width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
}

.team-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  padding: 60px;
  align-items: center;
}

.team-image-large {
  text-align: center;

  img {
    width: 100%;
    max-width: 600px;
    height: auto;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(30, 58, 138, 0.2);
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }
  }
}

.team-info-large {
  h3 {
    font-size: 2.8rem;
    font-weight: 800;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .team-subtitle {
    font-size: 1.3rem;
    color: #6b7280;
    margin-bottom: 40px;
    font-weight: 500;
  }

  .team-highlights {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 40px;

    .highlight-item {
      text-align: center;
      padding: 24px;
      background: linear-gradient(135deg, rgba(30, 58, 138, 0.08) 0%, rgba(59, 130, 246, 0.08) 100%);
      border-radius: 16px;
      border: 2px solid rgba(30, 58, 138, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        border-color: rgba(30, 58, 138, 0.2);
        box-shadow: 0 12px 32px rgba(30, 58, 138, 0.15);
      }

      .highlight-number {
        display: block;
        font-size: 2.2rem;
        font-weight: 800;
        color: #1e3a8a;
        margin-bottom: 8px;
      }

      .highlight-label {
        font-size: 1rem;
        color: #6b7280;
        font-weight: 600;
      }
    }
  }

  .team-description-large {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #4b5563;
    background: rgba(30, 58, 138, 0.05);
    padding: 24px;
    border-radius: 16px;
    border-left: 4px solid #3b82f6;

    .collaboration-framework {
      h4 {
        font-size: 1.2rem;
        font-weight: 700;
        color: #1e3a8a;
        margin-bottom: 16px;
        text-align: center;
      }

      .collaboration-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 20px;

        .collaboration-item {
          background: rgba(255, 255, 255, 0.8);
          padding: 16px;
          border-radius: 12px;
          border: 1px solid rgba(30, 58, 138, 0.1);
          font-size: 0.95rem;

          strong {
            color: #1e3a8a;
            display: block;
            margin-bottom: 4px;
          }
        }
      }

      .collaboration-features {
        .feature-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          font-size: 0.95rem;

          .feature-icon {
            font-size: 1.2rem;
            margin-right: 12px;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}

.carousel-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
  margin-top: 40px;
}

.carousel-btn {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: 2px solid #3b82f6;
  background: rgba(255, 255, 255, 0.9);
  color: #3b82f6;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background: #3b82f6;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  .arrow-left, .arrow-right {
    width: 0;
    height: 0;
  }

  .arrow-left {
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 12px solid currentColor;
    margin-right: 2px;
  }

  .arrow-right {
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 12px solid currentColor;
    margin-left: 2px;
  }
}

.carousel-indicators {
  display: flex;
  gap: 12px;

  .indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: rgba(59, 130, 246, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      background: #3b82f6;
      transform: scale(1.2);
    }

    &:hover {
      background: #3b82f6;
    }
  }
}

// Excellence Cases Section
.excellence-cases {
  margin-top: 60px;
  padding: 50px;
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.03) 0%, rgba(59, 130, 246, 0.03) 100%);
  border-radius: 24px;
  border: 1px solid rgba(30, 58, 138, 0.1);
}

.cases-header {
  text-align: center;
  margin-bottom: 40px;

  h3 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .cases-subtitle {
    font-size: 1.1rem;
    color: #6b7280;
    margin: 0;
  }
}

.cases-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 40px;

  .overview-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    border: 1px solid rgba(30, 58, 138, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(30, 58, 138, 0.15);
      border-color: rgba(30, 58, 138, 0.2);
    }

    .overview-icon {
      font-size: 2.5rem;
      margin-right: 16px;
      flex-shrink: 0;
    }

    .overview-content {
      h4 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e3a8a;
        margin: 0 0 8px 0;
        line-height: 1.3;
      }

      p {
        font-size: 0.9rem;
        color: #6b7280;
        margin: 0;
        line-height: 1.4;
      }
    }
  }
}

.nanjing-projects {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  margin-top: 20px;

  .project-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 24px;
    border: 1px solid rgba(30, 58, 138, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(30, 58, 138, 0.08);

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(30, 58, 138, 0.15);
      border-color: rgba(30, 58, 138, 0.2);
    }

    .project-title {
      font-size: 1.3rem;
      font-weight: 700;
      color: #1e3a8a;
      margin: 0 0 20px 0;
      text-align: center;
      background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .project-image {
      text-align: center;
      margin-bottom: 20px;

      img {
        width: 100%;
        max-width: 300px;
        height: 200px;
        object-fit: cover;
        border-radius: 12px;
        box-shadow: 0 8px 20px rgba(30, 58, 138, 0.15);
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }
    }

    .project-description {
      .project-features {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 16px;
        justify-content: center;

        .feature-tag {
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(30, 58, 138, 0.1) 100%);
          color: #1e3a8a;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 500;
          border: 1px solid rgba(30, 58, 138, 0.2);
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(30, 58, 138, 0.2) 100%);
            transform: translateY(-2px);
          }
        }
      }

      .project-desc {
        font-size: 0.95rem;
        color: #4b5563;
        line-height: 1.6;
        text-align: center;
        margin: 0;
        background: rgba(30, 58, 138, 0.03);
        padding: 16px;
        border-radius: 12px;
        border-left: 3px solid #3b82f6;
      }
    }
  }
}

// Annual Projects Section
.annual-projects {
  margin-top: 50px;
  padding: 40px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(30, 58, 138, 0.05) 100%);
  border-radius: 20px;
  border: 1px solid rgba(30, 58, 138, 0.1);
}

.annual-header {
  text-align: center;
  margin-bottom: 40px;

  h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .annual-subtitle {
    font-size: 1.1rem;
    color: #6b7280;
    margin: 0;
  }
}

.year-section {
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  border: 1px solid rgba(30, 58, 138, 0.08);

  &.current-year {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(30, 58, 138, 0.15);
    box-shadow: 0 8px 24px rgba(30, 58, 138, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }

  .year-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .year-badge {
      background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 600;
      margin-right: 16px;

      &.current {
        background: linear-gradient(135deg, #3b82f6 0%, #1e3a8a 100%);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }
    }

    .year-cities {
      font-size: 1.2rem;
      font-weight: 600;
      color: #1e3a8a;
    }
  }

  .year-desc {
    font-size: 1rem;
    color: #4b5563;
    line-height: 1.6;
    margin: 0;
  }
}

.current-projects {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 24px;

  .current-project-card {
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid rgba(30, 58, 138, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 28px rgba(30, 58, 138, 0.15);
      border-color: rgba(30, 58, 138, 0.2);
    }

    .project-city {
      background: linear-gradient(135deg, #3b82f6 0%, #1e3a8a 100%);
      color: white;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 600;
      display: inline-block;
      margin-bottom: 12px;
    }

    h4 {
      font-size: 1.1rem;
      font-weight: 600;
      color: #1e3a8a;
      margin: 0 0 12px 0;
      line-height: 1.3;
    }

    p {
      font-size: 0.9rem;
      color: #6b7280;
      line-height: 1.5;
      margin: 0;
    }
  }
}

// Awards Section
.awards-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8faff 0%, #e6f1ff 100%);
}

.awards-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.awards-image-container {
  text-align: center;

  img {
    width: 100%;
    max-width: 600px;
    height: auto;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(30, 58, 138, 0.15);
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }
  }
}

.awards-content {
  .awards-grid {
    display: grid;
    gap: 24px;
  }
}

.award-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(30, 58, 138, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 40px rgba(30, 58, 138, 0.15);
    border-color: rgba(30, 58, 138, 0.2);
  }

  .award-icon {
    font-size: 2.5rem;
    margin-right: 20px;
    flex-shrink: 0;
  }

  .award-content {
    h4 {
      font-size: 1.3rem;
      font-weight: 700;
      color: #1e3a8a;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 1rem;
      color: #6b7280;
      margin: 0;
      line-height: 1.5;
    }
  }
}



// Advantages Section
.advantages-section {
  padding: 100px 0;
  background: linear-gradient(180deg, #ffffff 0%, #f8faff 100%);

}

.advantages-grid {
  display: flex; /* 改用flex布局 */
  flex-wrap: nowrap; /* 禁止换行 */
  gap: 30px;
  overflow-x: auto; /* 启用水平滚动 */
  scrollbar-width: none; /* 隐藏滚动条（Firefox） */
  -ms-overflow-style: none; /* 隐藏滚动条（IE） */
  padding-bottom: 10px; /* 给滚动条留空间 */
}

/* 隐藏滚动条（Webkit浏览器） */
.advantages-grid::-webkit-scrollbar {
  display: none;
}
.advantage-card {
  flex: 0 0 300px; /* 固定宽度，禁止伸缩 */
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(30, 58, 138, 0.1);
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(30, 58, 138, 0.15);
  }

  .advantage-icon {
    font-size: 2.5rem;
    margin-bottom: 16px;
  }

  h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 12px;
  }

  p {
    color: #6b7280;
    line-height: 1.6;
  }
}

// Footer Section
.footer-section {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  margin-bottom: 40px;
}

.footer-info {
  .footer-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: white;
  }

  .footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-size: 1.1rem;
  }
}

.footer-contact {
  h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: white;
  }

  p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 8px;
  }
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 20px;
  text-align: center;

  p {
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
    font-size: 0.9rem;
  }
}

// Animations
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) rotate(45deg);
  }
  40% {
    transform: translateY(-5px) rotate(45deg);
  }
  60% {
    transform: translateY(-3px) rotate(45deg);
  }
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes circuitFlow {
  0% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(10px) translateY(-5px);
  }
  50% {
    transform: translateX(0) translateY(-10px);
  }
  75% {
    transform: translateX(-10px) translateY(-5px);
  }
  100% {
    transform: translateX(0) translateY(0);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.6);
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .architecture-display {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .team-showcase {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 40px;
    text-align: center;
  }

  .team-image-large img {
    max-width: 400px;
  }

  .team-info-large {
    h3 {
      font-size: 2.2rem;
    }

    .team-highlights {
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;

      .highlight-item {
        padding: 16px;

        .highlight-number {
          font-size: 1.8rem;
        }

        .highlight-label {
          font-size: 0.9rem;
        }
      }
    }
  }

  .awards-showcase {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .awards-image-container img {
    max-width: 400px;
  }

  .capability-section {
    grid-template-columns: 1fr !important;
    gap: 40px;

    &.reverse {
      .capability-content {
        order: 1;
      }

      .capability-image {
        order: 2;
      }
    }
  }

  .scenario-section {
    grid-template-columns: 1fr !important;
    gap: 40px;

    &.reverse {
      .scenario-content {
        order: 1;
      }

      .scenario-image {
        order: 2;
      }
    }
  }

  .marquee-content {
    .capability-item {
      min-width: 240px;
    }
  }
}

@media (max-width: 1200px) {
  .team-showcase {
    gap: 60px;
    padding: 50px;
  }

  .team-image-large img {
    max-width: 500px;
  }

  .team-info-large h3 {
    font-size: 2.4rem;
  }

  .awards-showcase {
    gap: 60px;
  }

  .awards-image-container img {
    max-width: 500px;
  }

  .excellence-cases {
    padding: 40px;
    margin-top: 50px;
  }

  .cases-overview {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .nanjing-projects {
    grid-template-columns: 1fr;
    gap: 24px;

    .project-card {
      padding: 20px;

      .project-image img {
        max-width: 250px;
        height: 180px;
      }
    }
  }

  .annual-projects {
    padding: 32px;
    margin-top: 40px;

    .current-projects {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .hero-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    max-width: 1200px;
    justify-items: center;
  }

  .stat-card {
    padding: 36px 30px;
    min-width: 160px;

    .stat-label {
      font-size: 1rem;
      white-space: nowrap;
    }
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .team-showcase {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 30px;
  }

  .team-image-large img {
    max-width: 350px;
  }

  .team-info-large {
    h3 {
      font-size: 1.8rem;
    }

    .team-subtitle {
      font-size: 1.1rem;
    }

    .team-highlights {
      grid-template-columns: 1fr;
      gap: 12px;

      .highlight-item {
        padding: 16px;

        .highlight-number {
          font-size: 1.6rem;
        }

        .highlight-label {
          font-size: 0.85rem;
        }
      }
    }

    .team-description-large {
      font-size: 1rem;
      padding: 20px;

      .collaboration-framework {
        .collaboration-grid {
          grid-template-columns: 1fr;
          gap: 12px;
        }

        .collaboration-item {
          padding: 12px;
          font-size: 0.9rem;
        }

        .collaboration-features .feature-item {
          font-size: 0.9rem;
        }
      }
    }
  }

  .excellence-cases {
    padding: 30px;
    margin-top: 40px;

    .cases-header h3 {
      font-size: 1.8rem;
    }

    .cases-overview {
      grid-template-columns: 1fr;
      gap: 16px;

      .overview-card {
        padding: 16px;

        .overview-icon {
          font-size: 2rem;
        }

        .overview-content {
          h4 {
            font-size: 1rem;
          }

          p {
            font-size: 0.85rem;
          }
        }
      }
    }

    .nanjing-projects {
      grid-template-columns: 1fr;
      gap: 20px;

      .project-card {
        padding: 20px;

        .project-title {
          font-size: 1.2rem;
        }

        .project-image img {
          max-width: 280px;
          height: 160px;
        }

        .project-description {
          .project-features .feature-tag {
            font-size: 0.75rem;
            padding: 4px 8px;
          }

          .project-desc {
            font-size: 0.9rem;
            padding: 14px;
          }
        }
      }
    }
  }

  .annual-projects {
    padding: 24px;
    margin-top: 32px;

    .annual-header h3 {
      font-size: 1.6rem;
    }

    .year-section {
      padding: 20px;

      .year-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .year-badge {
          margin-right: 0;
        }

        .year-cities {
          font-size: 1.1rem;
        }
      }
    }

    .current-projects {
      grid-template-columns: 1fr;
      gap: 16px;

      .current-project-card {
        padding: 16px;

        h4 {
          font-size: 1rem;
        }

        p {
          font-size: 0.85rem;
        }
      }
    }
  }

  .carousel-controls {
    gap: 20px;
    margin-top: 30px;
  }

  .carousel-btn {
    width: 48px;
    height: 48px;
  }

  .awards-showcase {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .awards-image-container img {
    max-width: 350px;
  }

  .award-card {
    padding: 20px;

    .award-icon {
      font-size: 2rem;
      margin-right: 16px;
    }

    .award-content {
      h4 {
        font-size: 1.1rem;
      }

      p {
        font-size: 0.9rem;
      }
    }
  }

  .hero-title {
    font-size: 3.5rem !important;
    transform: scale(1.02);
  }

  .hero-subtitle {
    font-size: 1.8rem !important;
  }

  .hero-description {
    font-size: 1.2rem !important;
  }

  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    max-width: 800px;
    justify-items: center;
  }

  .stat-card {
    padding: 32px 28px;
    min-width: 160px;

    .stat-number {
      font-size: 2.5rem;
    }

    .stat-label {
      font-size: 1rem;
      white-space: nowrap;
    }
  }

  .hero-actions {
    flex-direction: column;
    gap: 20px;
  }

  .section-header {
    margin-bottom: 80px;

    .section-title {
      font-size: 2.5rem !important;
    }

    .section-subtitle {
      font-size: 1.2rem !important;
    }
  }

  .capabilities-marquee {
    padding: 40px 0;
  }

  .marquee-content {
    .capability-item {
      min-width: 280px;
      padding: 32px 40px;

      .capability-number {
        font-size: 2.5rem;
      }

      .capability-name {
        font-size: 1.3rem;
      }

      .capability-desc {
        font-size: 1rem;
      }
    }
  }

  .capability-section {
    margin-bottom: 100px;
    gap: 60px !important;
    padding: 0 20px !important;

    .capability-content {
      h3 {
        font-size: 2.2rem !important;
      }

      .feature-item {
        padding: 20px 24px;

        .feature-text {
          font-size: 1rem;

          strong {
            font-size: 1.05rem;
          }
        }
      }
    }
  }

  .scenario-section {
    margin-bottom: 100px;
    gap: 60px !important;
    padding: 0 20px !important;

    .scenario-content {
      h3 {
        font-size: 2rem !important;
      }

      .feature-highlight {
        padding: 20px 24px;

        .highlight-number {
          font-size: 1rem;
          min-width: 80px;
          padding: 10px 16px;
        }

        .highlight-text {
          font-size: 1rem;
        }
      }

      .scenario-description {
        font-size: 1.1rem;
      }
    }
  }

  .deployment-info {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

@media (max-width: 480px) {
  .hero-stats {
    grid-template-columns: 1fr;
    justify-items: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .hero-title {
    font-size: 2.5rem !important;
    transform: scale(1);
  }

  .hero-subtitle {
    font-size: 1.4rem !important;
  }

  .team-showcase {
    padding: 20px;
    gap: 24px;
  }

  .team-image-large img {
    max-width: 280px;
  }

  .team-info-large {
    h3 {
      font-size: 1.5rem;
    }

    .team-subtitle {
      font-size: 1rem;
    }

    .team-highlights {
      .highlight-item {
        padding: 12px;

        .highlight-number {
          font-size: 1.4rem;
        }

        .highlight-label {
          font-size: 0.8rem;
        }
      }
    }

    .team-description-large {
      font-size: 0.9rem;
      padding: 16px;

      .collaboration-framework {
        .collaboration-grid {
          grid-template-columns: 1fr;
          gap: 10px;
        }

        .collaboration-item {
          padding: 10px;
          font-size: 0.85rem;
        }

        .collaboration-features .feature-item {
          font-size: 0.85rem;
          margin-bottom: 10px;

          .feature-icon {
            font-size: 1rem;
            margin-right: 8px;
          }
        }
      }
    }
  }

  .excellence-cases {
    padding: 20px;
    margin-top: 30px;

    .cases-header {
      h3 {
        font-size: 1.5rem;
      }

      .cases-subtitle {
        font-size: 1rem;
      }
    }

    .cases-overview {
      .overview-card {
        padding: 12px;
        flex-direction: column;
        text-align: center;

        .overview-icon {
          font-size: 1.8rem;
          margin-right: 0;
          margin-bottom: 8px;
        }

        .overview-content {
          h4 {
            font-size: 0.95rem;
          }

          p {
            font-size: 0.8rem;
          }
        }
      }
    }

    .nanjing-projects {
      .project-card {
        padding: 16px;

        .project-title {
          font-size: 1.1rem;
          margin-bottom: 16px;
        }

        .project-image img {
          max-width: 240px;
          height: 140px;
        }

        .project-description {
          .project-features .feature-tag {
            font-size: 0.7rem;
            padding: 3px 6px;
          }

          .project-desc {
            font-size: 0.85rem;
            padding: 12px;
          }
        }
      }
    }
  }

  .annual-projects {
    padding: 20px;
    margin-top: 24px;

    .annual-header {
      h3 {
        font-size: 1.4rem;
      }

      .annual-subtitle {
        font-size: 1rem;
      }
    }

    .year-section {
      padding: 16px;
      margin-bottom: 24px;

      .year-title {
        .year-badge {
          font-size: 0.8rem;
          padding: 6px 12px;
        }

        .year-cities {
          font-size: 1rem;
        }
      }

      .year-desc {
        font-size: 0.9rem;
      }
    }

    .current-projects .current-project-card {
      padding: 14px;

      .project-city {
        font-size: 0.75rem;
        padding: 3px 8px;
      }

      h4 {
        font-size: 0.95rem;
      }

      p {
        font-size: 0.8rem;
      }
    }
  }

  .carousel-controls {
    gap: 16px;
    margin-top: 24px;
  }

  .carousel-btn {
    width: 44px;
    height: 44px;
  }

  .awards-image-container img {
    max-width: 280px;
  }

  .award-card {
    padding: 16px;

    .award-icon {
      font-size: 1.8rem;
      margin-right: 12px;
    }

    .award-content {
      h4 {
        font-size: 1rem;
      }

      p {
        font-size: 0.85rem;
      }
    }
  }
}
</style>
