<template>
  <div :class="getWrapClass" id="tabNewClass">
    <Tabs
      type="editable-card"
      size="small"
      :animated="false"
      :hideAdd="true"
      :tabBarGutter="3"
      :activeKey="activeKeyRef"
      @change="handleChange"
      @edit="(e) => handleEdit(`${e}`)"
    >
      <template v-for="item in getTabsState" :key="item.query ? item.fullPath : item.path">
        <Tabs.TabPane :closable="!(item && item.meta && item.meta.affix)">
          <template #tab>
            <TabContent :tabItem="item" />
          </template>
        </Tabs.TabPane>
      </template>

      <template #rightExtra v-if="getShowRedo || getShowQuick">
        <SettingButton v-if="(getShowFold && getIsUnFold) || !getShowHeader" />
        <!-- <TabRedo v-if="getShowRedo" /> -->
        <!-- <TabContent isExtra :tabItem="$route" v-if="getShowQuick" /> -->
        <!-- <FoldButton v-if="getShowFold" /> -->
      </template>
    </Tabs>
  </div>
</template>
<script lang="ts" setup>
  import type { RouteLocationNormalized, RouteMeta } from 'vue-router';

  import { computed, unref, ref } from 'vue';

  import { Tabs } from 'ant-design-vue';
  import TabContent from './components/TabContent.vue';
  import FoldButton from './components/FoldButton.vue';
  import TabRedo from './components/TabRedo.vue';

  import { useGo } from '@/hooks/web/usePage';

  import { useMultipleTabStore } from '@/store/modules/multipleTab';
  import { useUserStore } from '@/store/modules/user';

  import { initAffixTabs, useTabsDrag } from './useMultipleTabs';
  import { useDesign } from '@/hooks/web/useDesign';
  import { useMultipleTabSetting } from '@/hooks/setting/useMultipleTabSetting';

  import { REDIRECT_NAME } from '@/router/constant';
  import { listenerRouteChange } from '@/logics/mitt/routeChange';

  import { useRouter } from 'vue-router';

  import { useMouse } from '@vueuse/core';
  import { multipleTabHeight } from '@/settings/designSetting';

  import SettingButton from './components/SettingButton.vue';
  import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
  import { useMenuSetting } from '@/hooks/setting/useMenuSetting';

  defineOptions({ name: 'MultipleTabs' });

  const affixTextList = initAffixTabs();
  const activeKeyRef = ref('');

  useTabsDrag(affixTextList);
  const tabStore = useMultipleTabStore();
  const userStore = useUserStore();
  const router = useRouter();

  const { prefixCls } = useDesign('multiple-tabs');
  const go = useGo();
  const { getShowQuick, getShowRedo, getShowFold } = useMultipleTabSetting();

  const getTabsState = computed(() => {
    return tabStore.getTabList.filter((item) => !item.meta?.hideTab);
  });

  const unClose = computed(() => unref(getTabsState).length === 1);

  const { y: mouseY } = useMouse();

  const { getShowMenu } = useMenuSetting();
  const { getShowHeader } = useHeaderSetting();
  const getIsUnFold = computed(() => !unref(getShowMenu) && !unref(getShowHeader));

  const getWrapClass = computed(() => {
    return [
      prefixCls,
      {
        [`${prefixCls}--hide-close`]: unref(unClose),
        [`${prefixCls}--hover`]: unref(mouseY) < multipleTabHeight,
      },
    ];
  });

  listenerRouteChange((route) => {
    const { name } = route;
    if (name === REDIRECT_NAME || !route || !userStore.getToken) {
      return;
    }

    const { path, fullPath, meta = {} } = route;
    const { currentActiveMenu, hideTab } = meta as RouteMeta;
    const isHide = !hideTab ? null : currentActiveMenu;
    const p = isHide || fullPath || path;
    if (activeKeyRef.value !== p) {
      activeKeyRef.value = p as string;
    }

    if (isHide) {
      const findParentRoute = router.getRoutes().find((item) => item.path === currentActiveMenu);

      findParentRoute && tabStore.addTab(findParentRoute as unknown as RouteLocationNormalized);
    } else {
      tabStore.addTab(unref(route));
    }
  });

  function handleChange(activeKey: any) {
    activeKeyRef.value = activeKey;
    go(activeKey, false);
  }

  // Close the current tab
  function handleEdit(targetKey: string) {
    // Added operation to hide, currently only use delete operation
    if (unref(unClose)) {
      return;
    }

    tabStore.closeTabByKey(targetKey, router);
  }
</script>
<style lang="less">
  @import url('./index.less');
  #tabNewClass {
    .ant-tabs-tab-active {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(240, 248, 255, 0.98) 100%) !important;
      border-radius: 12px 12px 0 0 !important;
      box-shadow: 0 6px 20px rgba(30, 58, 138, 0.15), 0 2px 8px rgba(255, 215, 0, 0.2), inset 0 1px 0 rgba(255, 215, 0, 0.3) !important;
      transform: translateY(-2px) !important;
      border: 2px solid rgba(255, 215, 0, 0.4) !important;
      border-bottom: none !important;
      backdrop-filter: blur(8px) !important;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(30, 58, 138, 0.05) 0%, rgba(255, 215, 0, 0.03) 100%);
        border-radius: 10px 10px 0 0;
        pointer-events: none;
      }

      span {
        color: #1e3a8a !important;
        font-weight: 600 !important;
        text-shadow: none !important;
        position: relative;
        z-index: 1;
      }
      svg {
        fill: #1e3a8a !important;
        filter: none !important;
      }
    }

    .ant-tabs-tab {
      border: 1px solid rgba(30, 58, 138, 0.08) !important;
      border-bottom: none !important;
      border-radius: 12px 12px 0 0 !important;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 255, 0.95) 100%) !important;
      margin-right: 3px !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      backdrop-filter: blur(4px) !important;
      font-weight: 500 !important;
      color: #4a5568 !important;

      &:hover:not(.ant-tabs-tab-active) {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(240, 248, 255, 0.98) 100%) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 16px rgba(30, 58, 138, 0.12), 0 2px 4px rgba(255, 215, 0, 0.08) !important;
        border-color: rgba(30, 58, 138, 0.15) !important;

        span {
          color: #1e3a8a !important;
        }
      }
    }

    .vben-multiple-tabs-content__info {
      font-size: 14px !important;
      font-weight: 400 !important;
    }

    .ant-tabs-nav {
      background: transparent !important;

      &::before {
        border: none !important;
      }
    }

    .ant-tabs-content-holder {
      background: transparent !important;
    }
  }
</style>
