@prefix-cls: ~'@{namespace}-multiple-tabs';
@prefix-cls-default-layout: ~'@{namespace}-default-layout';

html[data-theme='light'] {
  .@{prefix-cls} {
    .ant-tabs-tab:not(.ant-tabs-tab-active) {
      border: 1px solid #d9d9d9 !important;
      border-bottom-color: #f0f0f0 !important;
    }
  }
}

.@{prefix-cls-default-layout}-out {
  &.ant-layout-auto-collapse-tabs {
    .@{prefix-cls} {
      margin-top: -(@multiple-height + 2);
      opacity: 0.1;

      &:hover,
      &--hover {
        margin-top: 0;
        transition-delay: 0s;
        opacity: 1;
      }
    }
  }
  .@{prefix-cls} {
    transition:
      margin 0.2s ease-in-out 0.6s,
      opacity 0.2s ease-in-out 0.6s;
  }
}

.@{prefix-cls} {
  z-index: 10;
  height: @multiple-height + 2;
  border-bottom: none;
  background: linear-gradient(135deg, #f8faff 0%, #e6f1ff 50%, #f0f8ff 100%);
  line-height: @multiple-height + 2;
  box-shadow: 0 2px 12px rgba(30, 58, 138, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
  backdrop-filter: blur(8px);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(30, 58, 138, 0.2) 30%, rgba(255, 215, 0, 0.3) 50%, rgba(30, 58, 138, 0.2) 70%, transparent 100%);
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(30, 58, 138, 0.1) 50%, transparent 100%);
  }

  .ant-tabs-small {
    height: @multiple-height;
  }

  .ant-tabs.ant-tabs-card {
    .ant-tabs-nav {
      height: @multiple-height + 2;
      margin: 0;
      padding-top: 2px;
      border: 0;
      background-color: @component-background;
      box-shadow: none;

      .ant-tabs-nav-container {
        height: @multiple-height;
        padding-top: 2px;
        background: transparent;
      }

      .ant-tabs-tab {
        height: calc(@multiple-height);
        padding-right: 12px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 255, 0.95) 100%);
        color: #4a5568;
        line-height: calc(@multiple-height);
        border-radius: 12px 12px 0 0;
        margin-right: 3px;
        border: 1px solid rgba(30, 58, 138, 0.08);
        border-bottom: none;
        position: relative;
        font-weight: 500;
        backdrop-filter: blur(4px);

        &::before {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: transparent;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 215, 0, 0.02) 100%);
          border-radius: 12px 12px 0 0;
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: none;
        }

        &:hover {
          .ant-tabs-tab-remove {
            opacity: 1;
          }
        }

        .ant-tabs-tab-remove {
          width: 8px;
          height: 30px;
          margin-right: -4px;
          margin-left: 2px;
          transition: none;
          opacity: 0;
          color: inherit;
          font-size: 12px;

          &:hover {
            svg {
              width: 0.8em;
            }
          }
        }

        // > div {
        //   display: flex;
        //   justify-content: center;
        //   align-items: center;
        // }

        svg {
          fill: @text-color-base;
        }
      }

      .ant-tabs-tab:not(.ant-tabs-tab-active) {
        &:hover {
          color: #1e3a8a;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(240, 248, 255, 0.98) 100%);
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(30, 58, 138, 0.12), 0 2px 4px rgba(255, 215, 0, 0.08);
          border-color: rgba(30, 58, 138, 0.15);

          &::before {
            background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 50%, rgba(255, 215, 0, 0.3) 100%);
          }

          &::after {
            opacity: 1;
          }
        }
      }

      .ant-tabs-tab-active {
        position: relative;
        padding-left: 18px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid rgba(255, 215, 0, 0.4);
        border-bottom: none;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(240, 248, 255, 0.98) 100%);
        box-shadow: 0 6px 20px rgba(30, 58, 138, 0.15), 0 2px 8px rgba(255, 215, 0, 0.2), inset 0 1px 0 rgba(255, 215, 0, 0.3);
        transform: translateY(-2px);
        backdrop-filter: blur(8px);

        span {
          color: #1e3a8a !important;
          font-weight: 600;
          text-shadow: none;
          position: relative;
          z-index: 1;
        }

        .ant-tabs-tab-remove {
          opacity: 1;
        }

        svg {
          width: 0.7em;
          fill: #1e3a8a;
          filter: none;
        }

        &::before {
          background: linear-gradient(135deg, rgba(30, 58, 138, 0.05) 0%, rgba(255, 215, 0, 0.03) 100%);
          border-radius: 10px 10px 0 0;
          pointer-events: none;
        }

        &::after {
          background: linear-gradient(135deg, rgba(30, 58, 138, 0.05) 0%, rgba(255, 215, 0, 0.03) 100%);
          opacity: 1;
          border-radius: 10px 10px 0 0;
        }
      }
    }

    .ant-tabs-nav > div:nth-child(1) {
      padding: 0 6px;

      .ant-tabs-tab {
        margin-right: 3px !important;
      }
    }
  }

  .ant-tabs-tab:not(.ant-tabs-tab-active) {
    .anticon-close {
      font-size: 12px;

      svg {
        width: 0.6em;
      }
    }
  }

  .ant-dropdown-trigger {
    display: inline-flex;
  }

  &--hide-close {
    .ant-tabs-tab-remove {
      opacity: 0 !important;
    }
  }

  &-content {
    &__extra-quick,
    &__extra-redo,
    &__extra-fold {
      display: inline-block;
      width: 36px;
      height: @multiple-height;
      border-left: 1px solid @border-color-base;
      color: @text-color-secondary;
      line-height: @multiple-height;
      text-align: center;
      cursor: pointer;

      &:hover {
        color: @text-color-base;
      }

      span[role='img'] {
        transform: rotate(90deg);
      }
    }

    &__extra-redo {
      span[role='img'] {
        transform: rotate(0deg);
      }
    }

    &__info {
      display: inline-block;
      width: 100%;
      height: @multiple-height - 2;
      margin-left: -10px;
      padding-left: 0;
      font-size: 12px;
      cursor: pointer;
      user-select: none;
    }
  }
}

.ant-tabs-dropdown-menu {
  &-title-content {
    display: flex;
    align-items: center;

    .@{prefix-cls} {
      &-content__info {
        width: auto;
        margin-left: 0;
        line-height: 28px;
      }
    }
  }

  &-item-remove {
    margin-left: auto;
  }
}

.multiple-tabs__dropdown {
  .ant-dropdown-content {
    width: 172px;
  }
}
