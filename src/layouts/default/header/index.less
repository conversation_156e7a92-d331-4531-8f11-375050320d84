@header-trigger-prefix-cls: ~'@{namespace}-layout-header-trigger';
@header-prefix-cls: ~'@{namespace}-layout-header';
@breadcrumb-prefix-cls: ~'@{namespace}-layout-breadcrumb';
@logo-prefix-cls: ~'@{namespace}-app-logo';

.ant-layout .@{header-prefix-cls} {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: @header-height;
  margin-left: -1px;
  padding: 0;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
  color: @white;
  line-height: @header-height;
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  backdrop-filter: blur(10px);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 215, 0, 0.3) 50%, transparent 100%);
  }
 
  &--mobile {
    .@{breadcrumb-prefix-cls},
    .error-action,
    .notify-item,
    .fullscreen-item {
      display: none;
    }

    .@{logo-prefix-cls} {
      min-width: unset;
      padding-right: 0;

      &__title {
        display: none;
      }
    }
    .@{header-trigger-prefix-cls} {
      padding: 0 4px 0 8px !important;
    }
    .@{header-prefix-cls}-action {
      padding-right: 4px;
    }
  }

  &--fixed {
    position: fixed;
    z-index: @layout-header-fixed-z-index;
    top: 0;
    left: 0;
    width: 100%;
  }

  &-logo {
    min-width: 192px;
    height: @header-height;
    padding: 0 10px;
    font-size: 14px;

    img {
      width: @logo-width;
      height: @logo-width;
      margin-right: 2px;
    }
  }

  &-left {
    display: flex;
    flex: 0 0 auto;
    align-items: center;
    height: 100%;
    min-width: 200px;

    .@{header-trigger-prefix-cls} {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 1px 10px 0;
      cursor: pointer;

      .anticon {
        font-size: 16px;
      }

      &.light {
        &:hover {
          background-color: @header-light-bg-hover-color;
        }

        svg {
          fill: #000;
        }
      }

      &.dark {
        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }

  &-menu {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    min-width: 200px;
    max-width: none;
    height: 100%;
    position: relative;
    z-index: 1;
    overflow: visible;

    .ant-menu {
      background: transparent !important;
      border: none !important;

      .ant-menu-item {
        color: rgba(255, 255, 255, 0.95) !important;
        border-radius: 8px !important;
        margin: 0 6px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        position: relative;
        font-weight: 500;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 215, 0, 0.05));
          border-radius: 8px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          background: rgba(255, 255, 255, 0.12) !important;
          color: #fff !important;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(30, 58, 138, 0.2), 0 2px 4px rgba(255, 215, 0, 0.1);

          &::before {
            opacity: 1;
          }
        }

        &.ant-menu-item-selected {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 215, 0, 0.1)) !important;
          color: #fff !important;
          box-shadow: 0 4px 16px rgba(30, 58, 138, 0.25), inset 0 1px 0 rgba(255, 215, 0, 0.2);
          border: 1px solid rgba(255, 215, 0, 0.2);
        }
      }

      .ant-menu-submenu {
        .ant-menu-submenu-title {
          color: rgba(255, 255, 255, 0.95) !important;
          border-radius: 8px !important;
          margin: 0 6px !important;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
          font-weight: 500;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 215, 0, 0.05));
            border-radius: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &:hover {
            background: rgba(255, 255, 255, 0.12) !important;
            color: #fff !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.2), 0 2px 4px rgba(255, 215, 0, 0.1);

            &::before {
              opacity: 1;
            }
          }
        }

        &.ant-menu-submenu-selected {
          .ant-menu-submenu-title {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 215, 0, 0.1)) !important;
            color: #fff !important;
            box-shadow: 0 4px 16px rgba(30, 58, 138, 0.25), inset 0 1px 0 rgba(255, 215, 0, 0.2);
            border: 1px solid rgba(255, 215, 0, 0.2);
          }
        }
      }
    }
  }

  &-action {
    display: flex;
    flex: 0 0 auto;
    align-items: center;
    min-width: 180px;
    max-width: 300px;

    &__item {
      display: flex !important;
      align-items: center;
      height: @header-height;
      padding: 0 2px;
      font-size: 1.2em;
      cursor: pointer;

      .ant-badge {
        height: @header-height;
        line-height: @header-height;
      }

      .ant-badge-dot {
        top: 14px;
        right: 2px;
      }
    }

    span[role='img'] {
      padding: 0 8px;
    }
  }

  &--light {
    border-bottom: none;
    border-left: none;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%) !important;

    .@{header-prefix-cls}-logo {
      color: rgba(255, 255, 255, 0.95);

      &:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 215, 0, 0.08));
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(30, 58, 138, 0.15);
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        color: rgba(255, 255, 255, 0.9);

        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          transition: all 0.3s ease;
          transform: translateY(-1px);
        }
      }

      &-icon,
      span[role='img'] {
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  &--dark {
    border-bottom: 1px solid @border-color-base;
    border-left: 1px solid @border-color-base;
    background-color: @header-dark-bg-color !important;
    .@{header-prefix-cls}-logo {
      &:hover {
        background-color: @header-dark-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        .ant-badge {
          span {
            color: @white;
          }
        }

        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }
}

/* 响应式布局优化 */
@media (max-width: 1200px) {
  .ant-layout .@{header-prefix-cls} {
    &-left {
      min-width: 150px;
    }

    &-menu {
      min-width: 150px;
    }

    &-action {
      min-width: 150px;
    }
  }
}

@media (max-width: 768px) {
  .ant-layout .@{header-prefix-cls} {
    &-left {
      min-width: 100px;
    }

    &-menu {
      min-width: 100px;
    }

    &-action {
      min-width: 120px;
    }
  }
}

/* 确保flex布局在窗口大小变化时正确重新计算 */
.ant-layout .@{header-prefix-cls} {
  transition: none !important;

  &-menu {
    transition: none !important;
    flex-shrink: 1;
    flex-grow: 1;

    .ant-menu {
      transition: none !important;
      width: 100%;
      overflow: visible;
      flex: 1;
    }
  }

  &-left {
    flex-shrink: 0;
  }

  &-action {
    flex-shrink: 0;
  }
}

/* 强制重新计算布局的辅助类 */
.layout-recalculate {
  .ant-layout .@{header-prefix-cls} {
    &-menu {
      width: auto !important;
      min-width: 0 !important;
      flex: 1 1 auto !important;
    }
  }
}
