<template>
  <Layout.Header :class="getHeaderClass" class="bg-header">
    <!-- left start -->
    <div :class="`${prefixCls}-left`">
      <!-- logo -->
      <AppLogo
        v-if="getShowHeaderLogo || getIsMobile"
        :class="`${prefixCls}-logo`"
        :theme="getHeaderTheme"
        :style="getLogoWidth"
      />
      <LayoutTrigger
        v-if="
          (getShowContent && getShowHeaderTrigger && !getSplit && !getIsMixSidebar) || getIsMobile
        "
        :theme="getHeaderTheme"
        :sider="false"
      />
      <LayoutBreadcrumb v-if="getShowContent && getShowBread" :theme="getHeaderTheme" />
    </div>
    <!-- left end -->

    <!-- menu start -->
    <div v-if="getShowTopMenu && !getIsMobile" :class="`${prefixCls}-menu flex-left`">
      <LayoutMenu
        :isHorizontal="true"
        :theme="getHeaderTheme"
        :splitType="getSplitType"
        :menuMode="getMenuMode"
      />
    </div>
    <!-- menu-end -->

    <!-- action  -->
    <div :class="`${prefixCls}-action use-float`">
      <!--
      <AppSearch v-if="getShowSearch" :class="`${prefixCls}-action__item `" />
      -->
      <ErrorAction v-if="getUseErrorHandle" :class="`${prefixCls}-action__item error-action`" />

      <!-- <Notify v-if="getShowNotice" :class="`${prefixCls}-action__item notify-item`" /> -->

      <!-- <FullScreen v-if="getShowFullScreen" :class="`${prefixCls}-action__item fullscreen-item`" /> -->
      <!--
      <AppLocalePicker
        v-if="getShowLocalePicker"
        :reload="true"
        :showText="false"
        :class="`${prefixCls}-action__item`"
      />
      -->

      <UserDropDown :theme="getHeaderTheme" />
      <!--
      <SettingDrawer v-if="getShowSetting" :class="`${prefixCls}-action__item`" />
      -->
    </div>
  </Layout.Header>
</template>
<script lang="ts" setup>
  import { Layout } from 'ant-design-vue';
  import { computed, unref, onMounted, onUnmounted, nextTick } from 'vue';

  import { AppLocalePicker, AppLogo, AppSearch } from '@/components/Application';
  import { SettingButtonPositionEnum } from '@/enums/appEnum';
  import { MenuModeEnum, MenuSplitTyeEnum } from '@/enums/menuEnum';
  import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
  import { useMenuSetting } from '@/hooks/setting/useMenuSetting';
  import { useRootSetting } from '@/hooks/setting/useRootSetting';
  import { useAppInject } from '@/hooks/web/useAppInject';
  import { useDesign } from '@/hooks/web/useDesign';
  import { useLocale } from '@/locales/useLocale';
  import { createAsyncComponent } from '@/utils/factory/createAsyncComponent';
  import { propTypes } from '@/utils/propTypes';

  import LayoutMenu from '../menu/index.vue';
  import LayoutTrigger from '../trigger/index.vue';
  import { ErrorAction, FullScreen, LayoutBreadcrumb, Notify, UserDropDown } from './components';

  const SettingDrawer = createAsyncComponent(() => import('@/layouts/default/setting/index.vue'), {
    loading: true,
  });
  defineOptions({ name: 'LayoutHeader' });

  const props = defineProps({
    fixed: propTypes.bool,
  });
  const { prefixCls } = useDesign('layout-header');
  const {
    getShowTopMenu,
    getShowHeaderTrigger,
    getSplit,
    getIsMixMode,
    getMenuWidth,
    getIsMixSidebar,
  } = useMenuSetting();
  const { getUseErrorHandle, getShowSettingButton, getSettingButtonPosition } = useRootSetting();

  const {
    getHeaderTheme,
    getShowFullScreen,
    getShowNotice,
    getShowContent,
    getShowBread,
    getShowHeaderLogo,
    getShowHeader,
    getShowSearch,
  } = useHeaderSetting();

  const { getShowLocalePicker } = useLocale();

  const { getIsMobile } = useAppInject();

  const getHeaderClass = computed(() => {
    const theme = unref(getHeaderTheme);
    return [
      prefixCls,
      {
        [`${prefixCls}--fixed`]: props.fixed,
        [`${prefixCls}--mobile`]: unref(getIsMobile),
        [`${prefixCls}--${theme}`]: theme,
      },
    ];
  });

  const getShowSetting = computed(() => {
    if (!unref(getShowSettingButton)) {
      return false;
    }
    const settingButtonPosition = unref(getSettingButtonPosition);

    if (settingButtonPosition === SettingButtonPositionEnum.AUTO) {
      return unref(getShowHeader);
    }
    return settingButtonPosition === SettingButtonPositionEnum.HEADER;
  });

  const getLogoWidth = computed(() => {
    if (!unref(getIsMixMode) || unref(getIsMobile)) {
      return {};
    }
    const width = unref(getMenuWidth) < 180 ? 180 : unref(getMenuWidth);
    return { width: `${width}px` };
  });

  const getSplitType = computed(() => {
    return unref(getSplit) ? MenuSplitTyeEnum.TOP : MenuSplitTyeEnum.NONE;
  });

  const getMenuMode = computed(() => {
    return unref(getSplit) ? MenuModeEnum.HORIZONTAL : null;
  });

  // 修复响应式布局问题
  let resizeTimer: NodeJS.Timeout | null = null;

  const handleResize = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }

    resizeTimer = setTimeout(() => {
      // 强制重新计算布局
      nextTick(() => {
        const headerElement = document.querySelector('.ant-layout-header');
        if (headerElement) {
          // 触发重新布局
          const display = headerElement.style.display;
          headerElement.style.display = 'none';
          headerElement.offsetHeight; // 强制重排
          headerElement.style.display = display;
        }
      });
    }, 100);
  };

  onMounted(() => {
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }
  });
</script>
<style lang="less">
  @import url('./index.less');
    .bg-header {
      justify-content: flex-start !important;
      background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%) !important;
      box-shadow: 0 4px 20px rgba(30, 58, 138, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1) !important;
      position: relative;
      backdrop-filter: blur(10px) !important;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
        pointer-events: none;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, rgba(255, 215, 0, 0.3) 50%, transparent 100%);
      }

      .ant-menu-light {
        background-color: transparent !important;

        .ant-menu-item {
          color: rgba(255, 255, 255, 0.95) !important;
          border-radius: 8px !important;
          margin: 0 6px !important;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
          font-weight: 500 !important;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 215, 0, 0.05));
            border-radius: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &:hover {
            background: rgba(255, 255, 255, 0.12) !important;
            color: #fff !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.2), 0 2px 4px rgba(255, 215, 0, 0.1) !important;

            &::before {
              opacity: 1;
            }
          }

          &.ant-menu-item-selected {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 215, 0, 0.1)) !important;
            color: #fff !important;
            box-shadow: 0 4px 16px rgba(30, 58, 138, 0.25), inset 0 1px 0 rgba(255, 215, 0, 0.2) !important;
            border: 1px solid rgba(255, 215, 0, 0.2) !important;
          }
        }

        .ant-menu-submenu {
          .ant-menu-submenu-title {
            color: rgba(255, 255, 255, 0.95) !important;
            border-radius: 8px !important;
            margin: 0 6px !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            font-weight: 500 !important;
            position: relative;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 215, 0, 0.05));
              border-radius: 8px;
              opacity: 0;
              transition: opacity 0.3s ease;
            }

            &:hover {
              background: rgba(255, 255, 255, 0.12) !important;
              color: #fff !important;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(30, 58, 138, 0.2), 0 2px 4px rgba(255, 215, 0, 0.1) !important;

              &::before {
                opacity: 1;
              }
            }
          }

          &.ant-menu-submenu-selected {
            .ant-menu-submenu-title {
              background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 215, 0, 0.1)) !important;
              color: #fff !important;
              box-shadow: 0 4px 16px rgba(30, 58, 138, 0.25), inset 0 1px 0 rgba(255, 215, 0, 0.2) !important;
              border: 1px solid rgba(255, 215, 0, 0.2) !important;
            }
          }
        }
      }
    }

    .flex-left {
      display: flex;
      position: relative;
      z-index: 1;
    }

    .use-float {
      position: relative;
      z-index: 1;
    }
</style>

<!-- 全局菜单下拉框样式 -->
<style lang="less">
  // 菜单下拉框美化
  .app-top-menu-popup {
    .ant-menu {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 255, 0.98) 100%) !important;
      backdrop-filter: blur(12px) !important;
      border-radius: 12px !important;
      box-shadow: 0 8px 32px rgba(30, 58, 138, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1) !important;
      border: 1px solid rgba(30, 58, 138, 0.1) !important;
      padding: 8px !important;
      min-width: 180px !important;

      .ant-menu-item {
        border-radius: 8px !important;
        margin: 2px 0 !important;
        padding: 8px 16px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        color: #4a5568 !important;
        font-weight: 500 !important;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(30, 58, 138, 0.05) 0%, rgba(255, 215, 0, 0.03) 100%);
          border-radius: 8px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          background: linear-gradient(135deg, rgba(30, 58, 138, 0.08) 0%, rgba(255, 215, 0, 0.05) 100%) !important;
          color: #1e3a8a !important;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(30, 58, 138, 0.1) !important;

          &::before {
            opacity: 1;
          }
        }

        &.ant-menu-item-selected {
          background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%) !important;
          color: #fff !important;
          box-shadow: 0 4px 16px rgba(30, 58, 138, 0.25), inset 0 1px 0 rgba(255, 215, 0, 0.2) !important;
          border: 1px solid rgba(255, 215, 0, 0.3) !important;

          &::before {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 215, 0, 0.1) 100%);
            opacity: 1;
          }
        }
      }

      .ant-menu-submenu {
        .ant-menu-submenu-title {
          border-radius: 8px !important;
          margin: 2px 0 !important;
          padding: 8px 16px !important;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
          color: #4a5568 !important;
          font-weight: 500 !important;

          &:hover {
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.08) 0%, rgba(255, 215, 0, 0.05) 100%) !important;
            color: #1e3a8a !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.1) !important;
          }
        }

        &.ant-menu-submenu-selected {
          .ant-menu-submenu-title {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%) !important;
            color: #fff !important;
            box-shadow: 0 4px 16px rgba(30, 58, 138, 0.25), inset 0 1px 0 rgba(255, 215, 0, 0.2) !important;
            border: 1px solid rgba(255, 215, 0, 0.3) !important;
          }
        }
      }
    }
  }

  // 子菜单下拉框
  .ant-menu-submenu-popup {
    .ant-menu {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 255, 0.98) 100%) !important;
      backdrop-filter: blur(12px) !important;
      border-radius: 12px !important;
      box-shadow: 0 8px 32px rgba(30, 58, 138, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1) !important;
      border: 1px solid rgba(30, 58, 138, 0.1) !important;
      padding: 8px !important;
    }
  }
</style>
