// token key
export const TOKEN_KEY = 'TOKEN__';

export const LOCALE_KEY = 'LOCALE__';

// user info key
export const USER_INFO_KEY = 'USER__INFO__';

// role info key
export const ROLES_KEY = 'ROLES__KEY__';

export const AREA_CODE_KEY = 'USER__AREA_CODE__';

export const AREA_NAME_KEY = 'USER__AREA_NAME__';

// project config key
export const PROJ_CFG_KEY = 'PROJ__CFG__KEY__';
export const API_ADDRESS = 'API_ADDRESS__';

// lock info
export const LOCK_INFO_KEY = 'LOCK__INFO__KEY__';

export const MULTIPLE_TABS_KEY = 'MULTIPLE_TABS__KEY__';

export const APP_DARK_MODE_KEY = '__APP__DARK__MODE__';

// base global local key
export const APP_LOCAL_CACHE_KEY = 'COMMON__LOCAL__KEY__';

// base global session key
export const APP_SESSION_CACHE_KEY = 'COMMON__SESSION__KEY__';

// table 列设置
export const TABLE_SETTING_KEY = 'TABLE__SETTING__KEY__';

// 常州交换机口数据缓存
export const SWITCH_DATA_KEY = 'CHANGZHOU__SWITCH__DATA__KEY__';

// 常州数字电路端口数据缓存
export const DIGITAL_CIRCUIT_DATA_KEY = 'CHANGZHOU__DIGITAL__CIRCUIT__DATA__KEY__';

// 常州分光器数据缓存
export const OPTICAL_SPLITTER_DATA_KEY = 'CHANGZHOU__OPTICAL__SPLITTER__DATA__KEY__';

export enum CacheTypeEnum {
  SESSION,
  LOCAL,
}
