import { defineApplicationConfig } from '@vben/vite-config';
import Inspector from 'vite-plugin-vue-inspector';
import cdn from 'vite-plugin-cdn-import';
import AutoImport from 'unplugin-auto-import/vite';
import postcssPxToViewport from 'postcss-px-to-viewport-8-with-include';
// console.log(import.meta);
export default defineApplicationConfig({
  overrides: {
    css: {
      postcss: {
        plugins: [
          postcssPxToViewport({
            unitToConvert: 'px', // 要转换的单位
            viewportWidth: 1920, // 设计稿宽度
            unitPrecision: 5, // 单位转换后保留的精度
            propList: ['*'], // 指定转换那些属性，*表示全部
            viewportUnit: 'vw', // 希望使用的视口单位
            fontViewportUnit: 'vw', // 字体使用的视口单位
            selectorBlackList: [], // 要忽略的选择器
            minPixelValue: 1, // 最小的转换数值
            mediaQuery: false, // 是否在媒体查询中也转换px
            replace: true, // 是否直接更换原来的单位
            include: [/NewHome/],
            //  include: /newHome\.less$/
            // filter: (file) => {
            //   // 返回 true 或 false 来控制是否转换
            //   return file === '/src/views/nrm/home/<USER>';
            // }
          }),
        ],
      },
    },
    optimizeDeps: {
      include: [
        'echarts/core',
        'echarts/charts',
        'echarts/components',
        'echarts/renderers',
        'qrcode',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
      ],
    },
    server: {
      port: 39049,
      proxy: {
        '/basic-api': {
          target: 'http://daas.oss.telecomjs.com:39041',
          changeOrigin: true,
          ws: true,
          timeout: 1000000,
          rewrite: (path) => path.replace(new RegExp(`^/basic-api`), ''),
          // only https
          // secure: false
        },
        '/rest-api': {
          target: 'http://nrm.oss.telecomjs.com:39049/rest-api',
          // target: 'http://localhost:1082',
          // target: 'http://*************:39048',
          changeOrigin: false,
          ws: true,
          timeout: 1000000,
          rewrite: (path) => path.replace(new RegExp(`^/rest-api`), ''),
        },
        '/gather-rest-api': {
          // target: 'http://nrm.oss.telecomjs.com:39049/gather-rest-api',
          target: 'http://127.0.0.1:1082',
          changeOrigin: false,
          ws: true,
          timeout: 1000000,
          rewrite: (path) => path.replace(new RegExp(`^/gather-rest-api`), ''),
        },
        '/daas-dev-api': {
          target: 'http://nrm.oss.telecomjs.com:39045',
          changeOrigin: false,
          ws: true,
          timeout: 1000000,
          rewrite: (path) => path.replace(new RegExp(`^/daas-dev-api`), ''),
        },
        '/daas-gray-api': {
          target: 'http://nrm.oss.telecomjs.com:39049/daas-gray-api',
          changeOrigin: false,
          ws: true,
          timeout: 1000000,
          rewrite: (path) => path.replace(new RegExp(`^/daas-gray-api`), ''),
        },
        '/daas-nrm-api': {
          target: 'http://nrm.oss.telecomjs.com:39049/daas-nrm-api',
          changeOrigin: false,
          ws: true,
          timeout: 1000000,
          rewrite: (path) => path.replace(new RegExp(`^/daas-nrm-api`), ''),
        },
        '/daas-nrm-gray-api': {
          target: 'http://nrm.oss.telecomjs.com:39049/daas-nrm-gray-api',
          changeOrigin: false,
          ws: true,
          timeout: 1000000,
          rewrite: (path) => path.replace(new RegExp(`^/daas-nrm-gray-api`), ''),
        },
        '/graph-rest-api': {
          target: 'http://127.0.0.1:1084',
          // target:'http://***********:1084',
          // target:'http://daas.oss.telecomjs.com:39049/graph-rest-api',
          changeOrigin: false,
          ws: true,
          timeout: 1000000,
          rewrite: (path) => path.replace(new RegExp(`^/graph-rest-api`), ''),
        },
        '/gis-platform-new': {
          target: 'http://daas.oss.telecomjs.com:39049/gis-platform-new',
          // target:'http://***********:1084',
          // target:'http://daas.oss.telecomjs.com:39049/graph-rest-api',
          changeOrigin: false,
          ws: true,
          timeout: 1000000,
          rewrite: (path) => path.replace(new RegExp(`^/gis-platform-new`), ''),
        },
      },
      open: true, // 项目启动后，自动打开
      warmup: {
        clientFiles: ['./index.html', './src/{views,components}/*'],
      },
    },
    plugins: [
      Inspector({
        openInEditorHost: 'http://localhost:5173',
      }),
      cdn({
        modules: [],
      }),
      AutoImport({
        imports: [
          // 自动导入 Vue Composition API
          'vue',
          // 如果你还使用了其他库，比如 VueUse，也可以在这里添加
          // '@vueuse/core',
        ],
        // 其他选项...
      }),
    ],
  },
});
