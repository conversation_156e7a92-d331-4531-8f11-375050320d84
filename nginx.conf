worker_processes  auto;

events {
    worker_connections  1024;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    sendfile        on;
    keepalive_timeout  65;
	  underscores_in_headers on;
	  client_max_body_size  300m;
	  # 设置客户端请求头的超时时间
    client_header_timeout 600s;

    # 设置客户端请求体的超时时间
    client_body_timeout 600s;

    # 设置发送响应到客户端的超时时间
    send_timeout 600s;

    # 设置与上游服务器建立连接的超时时间
    proxy_connect_timeout 600s;

    # 设置往上游服务器发送请求的超时时间
    proxy_send_timeout 600s;

    # 设置从上游服务器读取响应的超时时间
    proxy_read_timeout 600s;
    server {
      listen 80 ;
      server_name localhost;

    location /basic-api/ {
		  proxy_http_version 1.1;
      rewrite /basic-api/(.*)$ /$1 break;
		  proxy_pass http://daas-manage-rest;
		  proxy_intercept_errors on;
    }
	  location /rest-api/ {
		  proxy_http_version 1.1;
      rewrite /rest-api/(.*)$ /$1 break;
		  proxy_pass http://nrm-rest-service;
		  proxy_intercept_errors on;
    }
	  location /order-monitor-api/ {
		  proxy_http_version 1.1;
      rewrite /order-monitor-api/(.*)$ /$1 break;
		  proxy_pass http://**************:8088;
		  proxy_intercept_errors on;
    }
	  location /prod-order-monitor-api/ {
		  proxy_http_version 1.1;
      rewrite /prod-order-monitor-api/(.*)$ /$1 break;
		  proxy_pass http://**************:8088;
		  proxy_intercept_errors on;
    }
	  location /address-complete-api/ {
		  proxy_http_version 1.1;
      rewrite /address-complete-api/(.*)$ /$1 break;
		  proxy_pass http://***************;
		  proxy_intercept_errors on;
    }
	  location /prod-address-complete-api/ {
		  proxy_http_version 1.1;
      rewrite /prod-address-complete-api/(.*)$ /$1 break;
		  proxy_pass http://************:20501;
		  proxy_intercept_errors on;
    }
	  location /graph-rest-api/ {
		  proxy_http_version 1.1;
      rewrite /graph-rest-api/(.*)$ /$1 break;
		  proxy_pass http://service-nrm-graph-rest;
		  proxy_set_header Accept application/json;
		  proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "Upgrade";
      proxy_set_header Host $host;
      proxy_intercept_errors on;
    }
    location /ws-idc {
    	proxy_http_version 1.1;
      # rewrite /graph-rest-ws/(.*)$ /$1 break;
      proxy_pass http://service-nrm-graph-rest;
    	proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "Upgrade";
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;  # 客户端真实 IP
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;  # 转发 IP
      proxy_set_header X-Forwarded-Proto $scheme;  # 转发协议
      proxy_read_timeout 3600s;

    }
    location /gather-rest-api/ {
    	proxy_http_version 1.1;
      rewrite /gather-rest-api/(.*)$ /$1 break;
    	proxy_pass http://nrm-gather-rest;
      proxy_set_header Accept application/json;
      proxy_intercept_errors on;
    }
	  location /daas-dev-api/ {
		  proxy_http_version 1.1;
      rewrite /daas-dev-api/(.*)$ /$1 break;
		  proxy_pass http://daas-interface-rest-debug;
		  proxy_intercept_errors on;
    }
	  location /daas-gray-api/ {
		  proxy_http_version 1.1;
      rewrite /daas-gray-api/(.*)$ /$1 break;
		  proxy_pass http://daas-interface-rest-gray;
		  proxy_intercept_errors on;
    }
    location /daas-nrm-gray-api/ {
    	proxy_http_version 1.1;
      rewrite /daas-nrm-gray-api/(.*)$ /$1 break;
    	proxy_pass http://daas-interface-rest-nrm-gray;
    	proxy_intercept_errors on;
    }
	  location /gis-platform/ {
		  proxy_http_version 1.1;
      rewrite /gis-platform/(.*)$ /$1 break;
		  proxy_pass http://10.143.25.248:21000;
		  proxy_set_header X-APP-ID "30249cce6de52a0052b98686cbadf3d9";
		  proxy_set_header X-APP-KEY "bccc8e5ffdc50f1af4c186c6aedf5df0";
		  proxy_intercept_errors on;
		  # proxy_pass http://10.128.86.64:8000/serviceAgent/rest/jtgis;
    }

    location /gis-platform-new/ {
       proxy_http_version 1.1;
       # rewrite /gis-platform-new/(.*)$ /serviceAgent/rest/jtgis/$1 break;
       # proxy_pass http://10.143.25.248:21000;
       proxy_set_header X-APP-ID "30249cce6de52a0052b98686cbadf3d9";
       proxy_set_header X-APP-KEY "bccc8e5ffdc50f1af4c186c6aedf5df0";
       #proxy_pass http://10.128.86.64:8000/serviceAgent/rest/jtgis/;
       proxy_pass https://10.141.176.9:21600/;
       proxy_ssl_verify off;
       proxy_intercept_errors on;
    }

    location /gis-platform-new-v2/ {
       proxy_http_version 1.1;
       # rewrite /gis-platform-new/(.*)$ /serviceAgent/rest/jtgis/$1 break;
       # proxy_pass http://10.143.25.248:21000;
       proxy_set_header X-APP-ID "30249cce6de52a0052b98686cbadf3d9";
       proxy_set_header X-APP-KEY "bccc8e5ffdc50f1af4c186c6aedf5df0";
       #proxy_pass http://10.128.86.64:8000/serviceAgent/rest/jtgis/;

       proxy_pass https://10.141.176.9:21600/;
       proxy_ssl_verify off;
       proxy_intercept_errors on;
    }

    location /ai-api/ {
    	 proxy_http_version 1.1;
       rewrite /ai-api/(.*)$ /$1 break;
    	 proxy_pass http://svc-nrm-ai;
    	 proxy_intercept_errors on;
    }

	  location /noc-fanshua/ {
		  proxy_http_version 1.1;
      rewrite /noc-fanshua/(.*)$ /$1 break;
		  proxy_pass http://ckpt.jsinfo.net:32417;
		  proxy_intercept_errors on;
    }
    location /zdintf-dev/ {
		  proxy_http_version 1.1;
      rewrite /zdintf-dev/(.*)$ /$1 break;
		  proxy_pass http://***************:8001/ida/services;
		  proxy_intercept_errors on;
    }
	  location /zdintf-prod/ {
		  proxy_http_version 1.1;
      rewrite /zdintf-prod/(.*)$ /$1 break;
		  proxy_pass http://zdintf.telecomjs.com:8001/ida/services;
		  proxy_intercept_errors on;
    }
    location /jt-eop/ {
		  proxy_http_version 1.1;
      rewrite /jt-eop/(.*)$ /$1 break;
		  proxy_pass http://************:20501;
		  proxy_intercept_errors on;
    }
    location /address-order-api/ {
    	proxy_http_version 1.1;
      rewrite /address-order-api/(.*)$ /$1 break;
    	proxy_pass http://**************:8088;
    	proxy_intercept_errors on;
    }
    location /elastic-search {
      proxy_http_version 1.1;
      rewrite /elastic-search/(.*)$ /$1 break;
      proxy_pass http://*************:19201;
      proxy_intercept_errors on;
    }

    location /res-base-test {
       proxy_http_version 1.1;
       rewrite /res-base-test/(.*)$ /$1 break;
       proxy_pass http://**************:29309;
       proxy_intercept_errors on;
    }

    location / {
      root /app/dist/;
      index  index.html;
      add_header 'Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
      add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
      proxy_intercept_errors on;
      #try_files $uri $uri/ /index.html;
    }


  }
}
